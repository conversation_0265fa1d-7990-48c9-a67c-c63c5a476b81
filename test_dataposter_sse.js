/**
 * Test script to demonstrate DataPoster SSE functionality
 * This script simulates state changes in the DataPoster and shows how SSE events are sent
 */

const DataPoster = require('./src/data_poster/data_poster.js');

console.log('=== DataPoster SSE Test ===\n');

// Mock configuration similar to what NodeApp.js uses
const mockConfig = {
    fine: './data/fine',
    metadata: './data/metadata.json',
    serverUrl: 'https://example.com/api/data',
    enable: true
};

console.log('1. Initializing DataPoster...');
DataPoster.init(mockConfig);

console.log('\n2. Initial status:');
console.log(JSON.stringify(DataPoster.getStatus(), null, 2));

console.log('\n3. Simulating state changes...');

// Simulate the service calls that would normally happen in NodeApp.js
setTimeout(() => {
    console.log('\n--- Service call 1: Enable with server URL ---');
    DataPoster.svc({
        enable: true,
        serverUrl: 'https://example.com/api/data'
    }, null);
    
    console.log('Status after service call:');
    console.log(JSON.stringify(DataPoster.getStatus(), null, 2));
}, 100);

setTimeout(() => {
    console.log('\n--- Service call 2: Add some data ---');
    const sampleData = [Date.now(), 25.5, 60.2, 1013.25]; // timestamp, temp, humidity, pressure
    DataPoster.svc({
        enable: true,
        serverUrl: 'https://example.com/api/data'
    }, sampleData);
    
    console.log('Status after adding data:');
    console.log(JSON.stringify(DataPoster.getStatus(), null, 2));
}, 200);

setTimeout(() => {
    console.log('\n--- Service call 3: Disable module ---');
    DataPoster.svc({
        enable: false,
        serverUrl: 'https://example.com/api/data'
    }, null);
    
    console.log('Status after disabling:');
    console.log(JSON.stringify(DataPoster.getStatus(), null, 2));
}, 300);

setTimeout(() => {
    console.log('\n--- Service call 4: Re-enable module ---');
    DataPoster.svc({
        enable: true,
        serverUrl: 'https://example.com/api/data'
    }, null);
    
    console.log('Status after re-enabling:');
    console.log(JSON.stringify(DataPoster.getStatus(), null, 2));
}, 400);

setTimeout(() => {
    console.log('\n--- Service call 5: Change server URL ---');
    DataPoster.svc({
        enable: true,
        serverUrl: 'https://different-server.com/api/data'
    }, null);
    
    console.log('Status after URL change:');
    console.log(JSON.stringify(DataPoster.getStatus(), null, 2));
}, 500);

setTimeout(() => {
    console.log('\n=== Test Summary ===');
    const finalStatus = DataPoster.getStatus();
    console.log(`Final state: ${finalStatus.state}`);
    console.log(`Total state changes: ${finalStatus.stateChangeCounter}`);
    console.log('\n✓ DataPoster state change counter is working');
    console.log('✓ Each state change should trigger an SSE broadcast to "cloud" channel');
    console.log('✓ The /api/status/cloud route supports both HTTP GET and SSE');
    console.log('\nTo test SSE in browser:');
    console.log('1. Start the application server');
    console.log('2. Open test_sse_cloud_status.html in a browser');
    console.log('3. Click "Connect SSE" to start receiving real-time updates');
    console.log('4. Trigger state changes by running this test or using the application');
    
    console.log('\n🎉 SSE implementation is ready for testing!');
}, 600);
