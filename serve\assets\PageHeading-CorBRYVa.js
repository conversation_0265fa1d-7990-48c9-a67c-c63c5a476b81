import{T as n,F as t,X as l,D as s,E as o,a1 as c,I as i,Q as p,J as u}from"./index-CzmOWWdj.js";import{Q as d}from"./QSpace-i2TdLNVM.js";const m={class:"text-h1 row"},g={__name:"PageHeading",props:{title:{type:String,required:!0},icon:{type:String,default:null},nospace:{type:Boolean,default:!1}},setup(r){const e=r;return(a,f)=>(t(),n("div",m,[l("div",null,[e.icon?(t(),s(p,{key:0,name:e.icon,class:"q-mr-sm"},null,8,["name"])):o("",!0),i(u(e.title),1)]),a.$slots.default&&!e.nospace?(t(),s(d,{key:0})):o("",!0),c(a.$slots,"default")]))}};export{g as _};
