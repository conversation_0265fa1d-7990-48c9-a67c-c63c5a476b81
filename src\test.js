// const {startupLogger} = require('./Logger.js')

// // Test the logger
// startupLogger.init();
// console.log("before logging stuff",startupLogger.getLogs());

// startupLogger.log('System started mama mia');
// startupLogger.error(new Error('Test error'));

// console.log("after logging stuff",startupLogger.getLogs());


// const fs = require('fs');

// /**
//  * Calculates the size of a file on disk and returns it in a human-readable format.
//  * @param {string} filePath - The absolute or relative path to the log file.
//  * @returns {string} The formatted file size (e.g., "1.45 MB") or an error message.
//  */
// function getLogFileSize(filePath) {
//     try {
//         // Get the statistics object for the file
//         const stats = fs.statSync(filePath);

//         // The size property gives the file size in bytes
//         const fileSizeInBytes = stats.size;

//         // Helper logic to convert bytes to a readable format
//         if (fileSizeInBytes === 0) {
//             return '0 Bytes';
//         }

//         const units = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
//         const i = Math.floor(Math.log(fileSizeInBytes) / Math.log(1024));
//         const size = parseFloat((fileSizeInBytes / Math.pow(1024, i)).toFixed(2));

//         return `${size} ${units[i]}`;

//     } catch (error) {
//         // Catches errors like "file not found"
//         // return `Error: Could not get stats for ${filePath}. The file may not exist.`;
//         console.log("error enc", error)
//     }
// }

// const logFilePath = './logs/startup.log';
// const fileSize = getLogFileSize(logFilePath);
// console.log(`The size of ${logFilePath} is ${fileSize}`);
