const { loggerConstructor } = require('./src/logger.js');
const fs = require('fs');

console.log('=== Debugging Logger File Writing ===');

// Clean up
const testFiles = fs.readdirSync('./logs').filter(f => f.includes('debug-test'));
testFiles.forEach(file => fs.unlinkSync(`./logs/${file}`));

const logger = loggerConstructor({
    size: 10,
    filename: 'debug-test',
    filesize: 500,
    persist: false,
    storeOnly: ['info'],
    verboseStack: false,
    enableConsole: true,
});

console.log('Logging a few messages...');
logger.log('Message 1');
logger.log('Message 2');
logger.log('Message 3');

// Check memory
console.log('Memory logs:', logger.getLogs());

// Wait and check files
setTimeout(() => {
    console.log('Checking files...');
    const files = fs.readdirSync('./logs').filter(f => f.includes('debug-test'));
    console.log('Files:', files);
    
    files.forEach(file => {
        const content = fs.readFileSync(`./logs/${file}`, 'utf8');
        console.log(`${file}: "${content}"`);
    });
}, 1000);
