const dayjs = require("dayjs")
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');
const { projectSettings } = require("./Global");
const { DatalogHandler } = require("./DatalogHandler");
dayjs.extend(utc);
dayjs.extend(timezone);

class DownloadDataStream {
    #datalogInstance;
    /**
     * @type {{ t0: number, t1: number }}
     * Unix timestamps representing the start and end of the selected range.
     * @private
     */
    #range = null;
    #res = null;
    #tz = null;
    #tFormat;
    fileName = "data.csv";

    /**
     * Creates an instance of YourClassName.
     * @param {Object} query - Configuration or filter parameters.
     * @param {DatalogHandler} datalogHandler - Instance of the DatalogHandler class.
     */
    constructor(query, datalogHandler) {
        this.#datalogInstance = datalogHandler;
        this.#tz = query.tz;
        this.#tFormat = query.tFormat;
        this.#res = query.res;
        this.#range = this.#getRange(parseInt(query.t0), parseInt(query.t1), query.anchor);
        this.fileName = this.#calculateFileName();
    }

    #calculateFileName() {
        const timestamp = dayjs().tz(this.#tz).format('YYYY-MM-DD_HH-mm-ss');
        return `${projectSettings.PROJECT_NAME}_${timestamp}.csv`;
    }

    #getRange(t0, t1, anchor) {
        switch (anchor) {
            case "system": {
                return {
                    t0: dayjs(dayjs.unix(t0).utc().format("YYYY-MM-DD HH:mm:ss")).unix(),
                    t1: dayjs(dayjs.unix(t1).utc().format("YYYY-MM-DD HH:mm:ss")).unix()
                };
            }
            case "local":
            case "utc":
            default:
                return {
                    t0,
                    t1
                };
        }
    }

    #formatTimestamp(timestamp) {
        switch (this.#tFormat) {
            case 'utc':
                return dayjs.unix(timestamp).utc().format("YYYY-MM-DD HH:mm:ss");
            case 'local':
                return dayjs.unix(timestamp).tz(this.#tz).format("YYYY-MM-DD HH:mm:ss");
            case 'system':
                return dayjs.unix(timestamp).format("YYYY-MM-DD HH:mm:ss");
            case 'unix':
            default:
                return timestamp;
        }
    }

    startStream(buffer) {
        buffer.push('\uFEFF');
        buffer.push(`Timestamp (${this.#tFormat.toUpperCase()})\n`)
        buffer.push(`${this.#datalogInstance.getMetadata("csv")}\n`)
        this.#datalogInstance.createStreamFilesFormat(this.#range.t0, this.#range.t1, "array", this.#res, false)
            .on("data", (data) => {
                data[0] = this.#formatTimestamp(data[0]);
                buffer.push(`${data.join(",")}\n`)
            })
            .on("end", () => buffer.push(null))
    }
}

module.exports = { DownloadDataStream }