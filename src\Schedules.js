//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// Schedules.js
//
// Load the schedules.json JSON file and initialize in Global
//
//=============================================================================================
console.log('========== Schedules.js ============')
const fs = require('fs')
const { schedules, timestamp }          = require('./Global.js')
const { clearObject, arrayToObject, parseDurationMs } = require('./Util.js')
const { ProjectDefinitions } = require('./Project.js')
const SchedulesFile = ProjectDefinitions.schedulesFile

/**
 * Checks if a given timestamp falls within a recurring schedule defined by a base date,
 * interval days, and duration.
 *
 * @param {Date} timestampDate - The date to check against the schedule
 * @param {Date} baseDate - The starting date of the schedule
 * @param {number} intervalDays - The interval in days between schedule occurrences
 * @param {number} durationMs - The duration of each schedule occurrence in milliseconds
 * @returns {boolean} True if the timestamp falls within the schedule, false otherwise
 *
 * @example
 * const timestamp = new Date('2023-05-15T14:30:00Z');
 * const baseDate = new Date('2023-05-01T12:00:00Z');
 * const inSchedule = isTimestampInSchedule(timestamp, baseDate, 7, 3600000);
 * /// Returns true if timestamp falls within a weekly schedule starting from baseDate
 *
 * See: https://chatgpt.com/share/68388cab-7318-800b-a228-7e5f8c2da242
 * See: https://chatgpt.com/c/683886bc-e3a8-800b-ab3b-88c8cd06799a (as <EMAIL>)
 */
function isTimestampInSchedule(timestampDate, baseDate, intervalDays, durationMs) {
	const MS_PER_SECOND = 1000;

	// Early exit if before start
	if (timestampDate < baseDate) return false;

	// Extract local YMD from both dates
	const timestampYMD = new Date(
		timestampDate.getFullYear(),
		timestampDate.getMonth(),
		timestampDate.getDate()
	)
	const baseYMD = new Date(
		baseDate.getFullYear(),
		baseDate.getMonth(),
		baseDate.getDate()
	)

	// Calculate the number of days difference in calendar days
	const MS_PER_DAY = 24 * 60 * 60 * 1000;
	const daysSinceStart = Math.round((timestampYMD - baseYMD) / MS_PER_DAY)

	// Check if this day is aligned to the interval
	if (daysSinceStart % intervalDays !== 0) {
		return false
	}

	// Construct the expected local start time for this recurrence
	const recurrenceStart = new Date(
		timestampDate.getFullYear(),
		timestampDate.getMonth(),
		timestampDate.getDate(),
		baseDate.getHours(),
		baseDate.getMinutes(),
		baseDate.getSeconds()
	)

	const recurrenceEnd = new Date(recurrenceStart.getTime() + durationMs)

	// Now check whether the timestamp falls within the recurrence window
	return timestampDate >= recurrenceStart && timestampDate <= recurrenceEnd
}

/**
 * Parse a string representing a repeat interval and return the number of days.
 *
 */
function parseRepeatDays(str)
{
	const n = parseInt(str)
	if(isNaN(n))
	{
		return null
	}
	const modifier = str.slice(-1)
	if(!isNaN(modifier)) //  no modifier
	{
		return n
	}
	let r = null
	switch(modifier.toLowerCase())
	{
		case 'd':
			r = n
			break
		case 'w':
			r = n * 7
			break
	}
	return r
}


// function parseDurationMs(str)
// {
// 	const n = parseInt(str)
// 	if(isNaN(n))
// 	{
// 		// if(str[0].toLowerCase() == 'e') // An event has zero duration
// 		return null
// 	}
// 	const modifier = str.slice(-1)
// 	if(!isNaN(modifier)) //  no modifier
// 	{
// 		return n * 1000
// 	}
// 	let r = null
// 	switch(modifier.toLowerCase())
// 	{
// 		case 'd':
// 			r = n * 24 * 60 * 60 * 1000
// 			break
// 		case 'h':
// 			r = n * 60 * 60 * 1000
// 			break
// 		case 'm':
// 			r = n * 60 * 1000
// 			break
// 		case 's':
// 			r = n * 1000
// 			break
// 	}
// 	return r
// }


function parse(schedulesText)
{
	const errors = []
	let errCnt = 0
	const sArray = typeof schedulesText == 'string' ? JSON.parse(schedulesText) : schedulesText
	sArray.forEach((schedule, i) => {
		const err = {}
		errors[i] = err
		const baseDate = new Date(schedule.date + 'T' + schedule.time)
		if(isNaN(baseDate))
		{
			// Determine if the error is due to an invalid date or time
			const dateError = isNaN(new Date(schedule.date))
			if(dateError)
			{
				console.error('Error parsing schedule date:', schedule.date)
				err.date = 'Invalid date'
			}
			else
			{
				console.error('Error parsing schedule time:', schedule.time)
				err.time = 'Invalid time'
			}
			schedule.disabled = true
			return
		}
		schedule.baseDate = baseDate

		// Parse the interval/repeat
		const repeatDays = parseRepeatDays(schedule.repeat)
		if(repeatDays)
		{
			schedule.repeatDays = repeatDays
		}
		else
		{
			console.error('Error parsing schedule repeat:', schedule.repeat)
			err.repeat = 'Invalid repeat, must be a positive non-0 interger followed by "d"(day) or "w"(week)'
			schedule.disabled = true
		}

		// Parse the duration
		const durationMs = parseDurationMs(schedule.duration)
		if(durationMs === null)
		{
			console.error('Error parsing schedule duration:', schedule.duration)
			err.duration = 'Invalid duration, must be an interger followed by "d"(day), "h"(hour), "m"(minute), or "s"(second)'
			schedule.disabled = true
		}
		else
		{
			schedule.durationMs = durationMs
		}

		schedule.value     = false
		schedule.valueLast = false

		if(Object.keys(err).length > 0) errCnt++
	})
	return { parsed: sArray, errors: errCnt ? errors : null }
}


	// baseDate: 2025-05-19T09:00:00.000Z,
	// repeatDays: 14,
	// durationMs: 7200000

function svc()
{
	const timestampDate = new Date(timestamp.ms)
	Object.keys(schedules).forEach(name => {
		const schedule = schedules[name]
		const value = isTimestampInSchedule(timestampDate, schedule.baseDate, schedule.repeatDays, schedule.durationMs || 2000)
		if(schedule.durationMs) // Not Event mode
		{
			schedule.value = value
		}
		else
		{
			if(value && !schedule.valueLast)
			{
				schedule.value = !schedule.value
			}
		}
		schedule.valueLast = value
	})
}

function init()
{
	// Try to load the file
	try {
		fileStr = fs.readFileSync(SchedulesFile, 'utf8')
		const { parsed: schedulesArray, errors } = parse(fileStr)

		if (errors) {
			console.error('Error parsing ' + SchedulesFile, errors)
			return
		}

		clearObject(schedules)

		Object.assign(schedules, arrayToObject(schedulesArray, 'name'))

		// console.log('Schedules loaded:', SchedulesFile, schedules)
	} catch (err) {
		console.error('Error loading ' + SchedulesFile, err)
	}
	return schedules
}

module.exports = {
	parse,
	svc,
	init,
}