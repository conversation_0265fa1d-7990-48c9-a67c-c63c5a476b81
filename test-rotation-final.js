const { loggerConstructor } = require('./src/logger.js');
const fs = require('fs');

console.log('=== Final Rotation Test ===');

// Clean up
const testFiles = fs.readdirSync('./logs').filter(f => f.includes('final-test'));
testFiles.forEach(file => fs.unlinkSync(`./logs/${file}`));

const logger = loggerConstructor({
    size: 20,
    filename: 'final-test',
    filesize: 600, // Small size for rotation
    persist: true,
    storeOnly: ['info'],
    verboseStack: false,
    enableConsole: true,
});

console.log('\n1. Initial state:');
logger.init();
console.log('Memory logs after init:', logger.getLogs().length);

console.log('\n2. Generating logs to trigger rotation...');
for(let i = 0; i < 15; i++) {
    logger.log(`Test message ${i}: This is a longer message to help trigger file rotation when the size limit is reached.`);
    
    // Check files periodically
    if (i % 5 === 4) {
        const files = fs.readdirSync('./logs').filter(f => f.includes('final-test'));
        console.log(`After message ${i}: Files = [${files.join(', ')}]`);
    }
}

console.log('\n3. Final check after logging...');
setTimeout(() => {
    const files = fs.readdirSync('./logs').filter(f => f.includes('final-test'));
    console.log('Final files:', files);
    console.log(`Total files: ${files.length} (should be <= 2)`);
    
    // Check file contents
    files.forEach(file => {
        const content = fs.readFileSync(`./logs/${file}`, 'utf8');
        const lines = content.split('\n').filter(line => line.trim());
        console.log(`\n${file}: ${lines.length} log entries`);
        
        if (lines.length > 0) {
            try {
                const firstLog = JSON.parse(lines[0]);
                const lastLog = JSON.parse(lines[lines.length - 1]);
                console.log(`  First: ${firstLog.message.substring(0, 50)}...`);
                console.log(`  Last: ${lastLog.message.substring(0, 50)}...`);
            } catch (e) {
                console.log(`  Error parsing logs: ${e.message}`);
            }
        }
    });
    
    console.log('\n4. Testing persist functionality...');
    
    // Create new logger instance to test persist
    const persistLogger = loggerConstructor({
        size: 20,
        filename: 'final-test',
        filesize: 600,
        persist: true,
        storeOnly: ['info'],
        verboseStack: false,
        enableConsole: false,
    });
    
    persistLogger.init();
    const loadedLogs = persistLogger.getLogs();
    console.log(`Loaded ${loadedLogs.length} logs from files into memory`);
    
    if (loadedLogs.length > 0) {
        console.log('First loaded:', loadedLogs[0].message.substring(0, 50) + '...');
        console.log('Last loaded:', loadedLogs[loadedLogs.length - 1].message.substring(0, 50) + '...');
    }
    
    // Verify maxFiles works
    if (files.length <= 2) {
        console.log('\n✅ SUCCESS: All tests passed!');
        console.log('- File rotation works correctly');
        console.log('- maxFiles limit is respected');
        console.log('- Persist functionality loads logs correctly');
    } else {
        console.log('\n❌ ERROR: maxFiles limit exceeded');
    }
    
}, 2000);
