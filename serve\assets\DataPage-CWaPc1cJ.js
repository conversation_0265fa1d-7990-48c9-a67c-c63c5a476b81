import{i as at,e as de,ai as Ye,g as oe,r as y,a as d,o as $e,f as Ae,j as lt,aj as ot,h,Q as j,C as Pe,ac as nt,ak as rt,a8 as ye,c as ne,w as te,al as it,am as ut,b as Oe,p as st,an as ct,ao as dt,ap as xe,a6 as vt,D as ft,F as ae,G as B,X as T,H as k,T as ve,E as fe,U as me,M as A,O as pe,I as mt,J as Me}from"./index-CzmOWWdj.js";import{j as be,e as bt,a as Fe,c as Ue,f as gt}from"./use-key-composition-CoMUTTxZ.js";import{Q as ht}from"./QResizeObserver-Bk4whrbK.js";import{u as Qe}from"./use-timeout-DeCFbuIx.js";import{r as yt,Q as le}from"./QSelect-BNWcW_ch.js";import{Q as Le,a as Re}from"./QPopupProxy-D3bKa31G.js";import{Q as Ie}from"./QInput-CCOJFSf5.js";import{o as kt,u as wt}from"./use-checkbox-DDSE4paG.js";import{Q as qt}from"./QCheckbox-BLQMX8bg.js";import{Q as Ct}from"./QToggle-D9UX9nQt.js";import{Q as Tt}from"./QPage-Disdm55v.js";import{_ as Dt}from"./PageHeading-CorBRYVa.js";import{_ as _t}from"./AqPersistToggleButton-BcqzKwP0.js";import{a as St,b as Vt}from"./Login-BVtZ5S7B.js";import{d as G}from"./dayjs-mToOJNbe.js";import"./QDialog-Cuvxr_1w.js";import"./focusout-C-pmmZED.js";import"./QMenu-D3shCIOy.js";import"./QSpace-i2TdLNVM.js";import"./_commonjsHelpers-Bx2EM-6T.js";let xt=0;const pt=["click","keydown"],Mt={icon:String,label:[Number,String],alert:[Boolean,String],alertIcon:String,name:{type:[Number,String],default:()=>`t_${xt++}`},noCaps:Boolean,tabindex:[String,Number],disable:Boolean,contentClass:String,ripple:{type:[Boolean,Object],default:!0}};function Qt(e,D,b,v){const s=at(Ye,de);if(s===de)return console.error("QTab/QRouteTab component needs to be child of QTabs"),de;const{proxy:R}=oe(),I=y(null),V=y(null),Y=y(null),S=d(()=>e.disable===!0||e.ripple===!1?!1:Object.assign({keyCodes:[13,32],early:!0},e.ripple===!0?{}:e.ripple)),Q=d(()=>s.currentModel.value===e.name),M=d(()=>"q-tab relative-position self-stretch flex flex-center text-center"+(Q.value===!0?" q-tab--active"+(s.tabProps.value.activeClass?" "+s.tabProps.value.activeClass:"")+(s.tabProps.value.activeColor?` text-${s.tabProps.value.activeColor}`:"")+(s.tabProps.value.activeBgColor?` bg-${s.tabProps.value.activeBgColor}`:""):" q-tab--inactive")+(e.icon&&e.label&&s.tabProps.value.inlineLabel===!1?" q-tab--full":"")+(e.noCaps===!0||s.tabProps.value.noCaps===!0?" q-tab--no-caps":"")+(e.disable===!0?" disabled":" q-focusable q-hoverable cursor-pointer")),C=d(()=>"q-tab__content self-stretch flex-center relative-position q-anchor--skip non-selectable "+(s.tabProps.value.inlineLabel===!0?"row no-wrap q-tab__content--inline":"column")+(e.contentClass!==void 0?` ${e.contentClass}`:"")),g=d(()=>e.disable===!0||s.hasFocus.value===!0||Q.value===!1&&s.hasActiveTab.value===!0?-1:e.tabindex||0);function o(u,i){if(i!==!0&&u?.qAvoidFocus!==!0&&I.value?.focus(),e.disable!==!0){s.updateModel({name:e.name}),b("click",u);return}}function _(u){nt(u,[13,32])?o(u,!0):rt(u)!==!0&&u.keyCode>=35&&u.keyCode<=40&&u.altKey!==!0&&u.metaKey!==!0&&s.onKbdNavigate(u.keyCode,R.$el)===!0&&ye(u),b("keydown",u)}function x(){const u=s.tabProps.value.narrowIndicator,i=[],f=h("div",{ref:Y,class:["q-tab__indicator",s.tabProps.value.indicatorClass]});e.icon!==void 0&&i.push(h(j,{class:"q-tab__icon",name:e.icon})),e.label!==void 0&&i.push(h("div",{class:"q-tab__label"},e.label)),e.alert!==!1&&i.push(e.alertIcon!==void 0?h(j,{class:"q-tab__alert-icon",color:e.alert!==!0?e.alert:void 0,name:e.alertIcon}):h("div",{class:"q-tab__alert"+(e.alert!==!0?` text-${e.alert}`:"")})),u===!0&&i.push(f);const a=[h("div",{class:"q-focus-helper",tabindex:-1,ref:I}),h("div",{class:C.value},Pe(D.default,i))];return u===!1&&a.push(f),a}const $={name:d(()=>e.name),rootRef:V,tabIndicatorRef:Y,routeData:v};$e(()=>{s.unregisterTab($)}),Ae(()=>{s.registerTab($)});function O(u,i){const f={ref:V,class:M.value,tabindex:g.value,role:"tab","aria-selected":Q.value===!0?"true":"false","aria-disabled":e.disable===!0?"true":void 0,onClick:o,onKeydown:_,...i};return lt(h(u,f,x()),[[ot,S.value]])}return{renderTab:O,$tabs:s}}const ge=ne({name:"QTab",props:Mt,emits:pt,setup(e,{slots:D,emit:b}){const{renderTab:v}=Qt(e,D,b);return()=>v("div")}});function Lt(e,D,b){const v=b===!0?["left","right"]:["top","bottom"];return`absolute-${D===!0?v[0]:v[1]}${e?` text-${e}`:""}`}const Rt=["left","center","right","justify"],It=ne({name:"QTabs",props:{modelValue:[Number,String],align:{type:String,default:"center",validator:e=>Rt.includes(e)},breakpoint:{type:[String,Number],default:600},vertical:Boolean,shrink:Boolean,stretch:Boolean,activeClass:String,activeColor:String,activeBgColor:String,indicatorColor:String,leftIcon:String,rightIcon:String,outsideArrows:Boolean,mobileArrows:Boolean,switchIndicator:Boolean,narrowIndicator:Boolean,inlineLabel:Boolean,noCaps:Boolean,dense:Boolean,contentClass:String,"onUpdate:modelValue":[Function,Array]},setup(e,{slots:D,emit:b}){const{proxy:v}=oe(),{$q:s}=v,{registerTick:R}=be(),{registerTick:I}=be(),{registerTick:V}=be(),{registerTimeout:Y,removeTimeout:S}=Qe(),{registerTimeout:Q,removeTimeout:M}=Qe(),C=y(null),g=y(null),o=y(e.modelValue),_=y(!1),x=y(!0),$=y(!1),O=y(!1),u=[],i=y(0),f=y(!1);let a=null,n=null,L;const E=d(()=>({activeClass:e.activeClass,activeColor:e.activeColor,activeBgColor:e.activeBgColor,indicatorClass:Lt(e.indicatorColor,e.switchIndicator,e.vertical),narrowIndicator:e.narrowIndicator,inlineLabel:e.inlineLabel,noCaps:e.noCaps})),z=d(()=>{const t=i.value,l=o.value;for(let r=0;r<t;r++)if(u[r].name.value===l)return!0;return!1}),N=d(()=>`q-tabs__content--align-${_.value===!0?"left":O.value===!0?"justify":e.align}`),re=d(()=>`q-tabs row no-wrap items-center q-tabs--${_.value===!0?"":"not-"}scrollable q-tabs--${e.vertical===!0?"vertical":"horizontal"} q-tabs__arrows--${e.outsideArrows===!0?"outside":"inside"} q-tabs--mobile-with${e.mobileArrows===!0?"":"out"}-arrows`+(e.dense===!0?" q-tabs--dense":"")+(e.shrink===!0?" col-shrink":"")+(e.stretch===!0?" self-stretch":"")),Ee=d(()=>"q-tabs__content scroll--mobile row no-wrap items-center self-stretch hide-scrollbar relative-position "+N.value+(e.contentClass!==void 0?` ${e.contentClass}`:"")),J=d(()=>e.vertical===!0?{container:"height",content:"offsetHeight",scroll:"scrollHeight"}:{container:"width",content:"offsetWidth",scroll:"scrollWidth"}),X=d(()=>e.vertical!==!0&&s.lang.rtl===!0),ie=d(()=>yt===!1&&X.value===!0);te(X,K),te(()=>e.modelValue,t=>{ue({name:t,setCurrent:!0,skipEmit:!0})}),te(()=>e.outsideArrows,Z);function ue({name:t,setCurrent:l,skipEmit:r}){o.value!==t&&(r!==!0&&e["onUpdate:modelValue"]!==void 0&&b("update:modelValue",t),(l===!0||e["onUpdate:modelValue"]===void 0)&&(ze(o.value,t),o.value=t))}function Z(){R(()=>{C.value&&ke({width:C.value.offsetWidth,height:C.value.offsetHeight})})}function ke(t){if(J.value===void 0||g.value===null)return;const l=t[J.value.container],r=Math.min(g.value[J.value.scroll],Array.prototype.reduce.call(g.value.children,(q,m)=>q+(m[J.value.content]||0),0)),w=l>0&&r>l;_.value=w,w===!0&&I(K),O.value=l<parseInt(e.breakpoint,10)}function ze(t,l){const r=t!=null&&t!==""?u.find(q=>q.name.value===t):null,w=l!=null&&l!==""?u.find(q=>q.name.value===l):null;if(ee===!0)ee=!1;else if(r&&w){const q=r.tabIndicatorRef.value,m=w.tabIndicatorRef.value;a!==null&&(clearTimeout(a),a=null),q.style.transition="none",q.style.transform="none",m.style.transition="none",m.style.transform="none";const c=q.getBoundingClientRect(),p=m.getBoundingClientRect();m.style.transform=e.vertical===!0?`translate3d(0,${c.top-p.top}px,0) scale3d(1,${p.height?c.height/p.height:1},1)`:`translate3d(${c.left-p.left}px,0,0) scale3d(${p.width?c.width/p.width:1},1,1)`,V(()=>{a=setTimeout(()=>{a=null,m.style.transition="transform .25s cubic-bezier(.4, 0, .2, 1)",m.style.transform="none"},70)})}w&&_.value===!0&&H(w.rootRef.value)}function H(t){const{left:l,width:r,top:w,height:q}=g.value.getBoundingClientRect(),m=t.getBoundingClientRect();let c=e.vertical===!0?m.top-w:m.left-l;if(c<0){g.value[e.vertical===!0?"scrollTop":"scrollLeft"]+=Math.floor(c),K();return}c+=e.vertical===!0?m.height-q:m.width-r,c>0&&(g.value[e.vertical===!0?"scrollTop":"scrollLeft"]+=Math.ceil(c),K())}function K(){const t=g.value;if(t===null)return;const l=t.getBoundingClientRect(),r=e.vertical===!0?t.scrollTop:Math.abs(t.scrollLeft);X.value===!0?(x.value=Math.ceil(r+l.width)<t.scrollWidth-1,$.value=r>0):(x.value=r>0,$.value=e.vertical===!0?Math.ceil(r+l.height)<t.scrollHeight:Math.ceil(r+l.width)<t.scrollWidth)}function we(t){n!==null&&clearInterval(n),n=setInterval(()=>{Ne(t)===!0&&F()},5)}function qe(){we(ie.value===!0?Number.MAX_SAFE_INTEGER:0)}function Ce(){we(ie.value===!0?0:Number.MAX_SAFE_INTEGER)}function F(){n!==null&&(clearInterval(n),n=null)}function He(t,l){const r=Array.prototype.filter.call(g.value.children,p=>p===l||p.matches&&p.matches(".q-tab.q-focusable")===!0),w=r.length;if(w===0)return;if(t===36)return H(r[0]),r[0].focus(),!0;if(t===35)return H(r[w-1]),r[w-1].focus(),!0;const q=t===(e.vertical===!0?38:37),m=t===(e.vertical===!0?40:39),c=q===!0?-1:m===!0?1:void 0;if(c!==void 0){const p=X.value===!0?-1:1,P=r.indexOf(l)+c*p;return P>=0&&P<w&&(H(r[P]),r[P].focus({preventScroll:!0})),!0}}const Ke=d(()=>ie.value===!0?{get:t=>Math.abs(t.scrollLeft),set:(t,l)=>{t.scrollLeft=-l}}:e.vertical===!0?{get:t=>t.scrollTop,set:(t,l)=>{t.scrollTop=l}}:{get:t=>t.scrollLeft,set:(t,l)=>{t.scrollLeft=l}});function Ne(t){const l=g.value,{get:r,set:w}=Ke.value;let q=!1,m=r(l);const c=t<m?-1:1;return m+=c*5,m<0?(q=!0,m=0):(c===-1&&m<=t||c===1&&m>=t)&&(q=!0,m=t),w(l,m),K(),q}function Te(t,l){for(const r in t)if(t[r]!==l[r])return!1;return!0}function We(){let t=null,l={matchedLen:0,queryDiff:9999,hrefLen:0};const r=u.filter(c=>c.routeData?.hasRouterLink.value===!0),{hash:w,query:q}=v.$route,m=Object.keys(q).length;for(const c of r){const p=c.routeData.exact.value===!0;if(c.routeData[p===!0?"linkIsExactActive":"linkIsActive"].value!==!0)continue;const{hash:P,query:se,matched:et,href:tt}=c.routeData.resolvedLink.value,ce=Object.keys(se).length;if(p===!0){if(P!==w||ce!==m||Te(q,se)===!1)continue;t=c.name.value;break}if(P!==""&&P!==w||ce!==0&&Te(se,q)===!1)continue;const U={matchedLen:et.length,queryDiff:m-ce,hrefLen:tt.length-P.length};if(U.matchedLen>l.matchedLen){t=c.name.value,l=U;continue}else if(U.matchedLen!==l.matchedLen)continue;if(U.queryDiff<l.queryDiff)t=c.name.value,l=U;else if(U.queryDiff!==l.queryDiff)continue;U.hrefLen>l.hrefLen&&(t=c.name.value,l=U)}if(t===null&&u.some(c=>c.routeData===void 0&&c.name.value===o.value)===!0){ee=!1;return}ue({name:t,setCurrent:!0})}function Ge(t){if(S(),f.value!==!0&&C.value!==null&&t.target&&typeof t.target.closest=="function"){const l=t.target.closest(".q-tab");l&&C.value.contains(l)===!0&&(f.value=!0,_.value===!0&&H(l))}}function Je(){Y(()=>{f.value=!1},30)}function W(){_e.avoidRouteWatcher===!1?Q(We):M()}function De(){if(L===void 0){const t=te(()=>v.$route.fullPath,W);L=()=>{t(),L=void 0}}}function Xe(t){u.push(t),i.value++,Z(),t.routeData===void 0||v.$route===void 0?Q(()=>{if(_.value===!0){const l=o.value,r=l!=null&&l!==""?u.find(w=>w.name.value===l):null;r&&H(r.rootRef.value)}}):(De(),t.routeData.hasRouterLink.value===!0&&W())}function Ze(t){u.splice(u.indexOf(t),1),i.value--,Z(),L!==void 0&&t.routeData!==void 0&&(u.every(l=>l.routeData===void 0)===!0&&L(),W())}const _e={currentModel:o,tabProps:E,hasFocus:f,hasActiveTab:z,registerTab:Xe,unregisterTab:Ze,verifyRouteModel:W,updateModel:ue,onKbdNavigate:He,avoidRouteWatcher:!1};st(Ye,_e);function Se(){a!==null&&clearTimeout(a),F(),L?.()}let Ve,ee;return $e(Se),it(()=>{Ve=L!==void 0,Se()}),ut(()=>{Ve===!0&&(De(),ee=!0,W()),Z()}),()=>h("div",{ref:C,class:re.value,role:"tablist",onFocusin:Ge,onFocusout:Je},[h(ht,{onResize:ke}),h("div",{ref:g,class:Ee.value,onScroll:K},Oe(D.default)),h(j,{class:"q-tabs__arrow q-tabs__arrow--left absolute q-tab__icon"+(x.value===!0?"":" q-tabs__arrow--faded"),name:e.leftIcon||s.iconSet.tabs[e.vertical===!0?"up":"left"],onMousedownPassive:qe,onTouchstartPassive:qe,onMouseupPassive:F,onMouseleavePassive:F,onTouchendPassive:F}),h(j,{class:"q-tabs__arrow q-tabs__arrow--right absolute q-tab__icon"+($.value===!0?"":" q-tabs__arrow--faded"),name:e.rightIcon||s.iconSet.tabs[e.vertical===!0?"down":"right"],onMousedownPassive:Ce,onTouchstartPassive:Ce,onMouseupPassive:F,onMouseleavePassive:F,onTouchendPassive:F})])}}),Bt=()=>h("svg",{key:"svg",class:"q-radio__bg absolute non-selectable",viewBox:"0 0 24 24"},[h("path",{d:"M12,22a10,10 0 0 1 -10,-10a10,10 0 0 1 10,-10a10,10 0 0 1 10,10a10,10 0 0 1 -10,10m0,-22a12,12 0 0 0 -12,12a12,12 0 0 0 12,12a12,12 0 0 0 12,-12a12,12 0 0 0 -12,-12"}),h("path",{class:"q-radio__check",d:"M12,6a6,6 0 0 0 -6,6a6,6 0 0 0 6,6a6,6 0 0 0 6,-6a6,6 0 0 0 -6,-6"})]),Yt=ne({name:"QRadio",props:{...Fe,...ct,...bt,modelValue:{required:!0},val:{required:!0},label:String,leftLabel:Boolean,checkedIcon:String,uncheckedIcon:String,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},emits:["update:modelValue"],setup(e,{slots:D,emit:b}){const{proxy:v}=oe(),s=Ue(e,v.$q),R=dt(e,kt),I=y(null),{refocusTargetEl:V,refocusTarget:Y}=wt(e,I),S=d(()=>xe(e.modelValue)===xe(e.val)),Q=d(()=>"q-radio cursor-pointer no-outline row inline no-wrap items-center"+(e.disable===!0?" disabled":"")+(s.value===!0?" q-radio--dark":"")+(e.dense===!0?" q-radio--dense":"")+(e.leftLabel===!0?" reverse":"")),M=d(()=>{const i=e.color!==void 0&&(e.keepColor===!0||S.value===!0)?` text-${e.color}`:"";return`q-radio__inner relative-position q-radio__inner--${S.value===!0?"truthy":"falsy"}${i}`}),C=d(()=>(S.value===!0?e.checkedIcon:e.uncheckedIcon)||null),g=d(()=>e.disable===!0?-1:e.tabindex||0),o=d(()=>{const i={type:"radio"};return e.name!==void 0&&Object.assign(i,{".checked":S.value===!0,"^checked":S.value===!0?"checked":void 0,name:e.name,value:e.val}),i}),_=gt(o);function x(i){i!==void 0&&(ye(i),Y(i)),e.disable!==!0&&S.value!==!0&&b("update:modelValue",e.val,i)}function $(i){(i.keyCode===13||i.keyCode===32)&&ye(i)}function O(i){(i.keyCode===13||i.keyCode===32)&&x(i)}Object.assign(v,{set:x});const u=Bt();return()=>{const i=C.value!==null?[h("div",{key:"icon",class:"q-radio__icon-container absolute-full flex flex-center no-wrap"},[h(j,{class:"q-radio__icon",name:C.value})])]:[u];e.disable!==!0&&_(i,"unshift"," q-radio__native q-ma-none q-pa-none");const f=[h("div",{class:M.value,style:R.value,"aria-hidden":"true"},i)];V.value!==null&&f.push(V.value);const a=e.label!==void 0?Pe(D.default,[e.label]):Oe(D.default);return a!==void 0&&f.push(h("div",{class:"q-radio__label q-anchor--skip"},a)),h("div",{ref:I,class:Q.value,tabindex:g.value,role:"radio","aria-label":e.label,"aria-checked":S.value===!0?"true":"false","aria-disabled":e.disable===!0?"true":void 0,onClick:x,onKeydown:$,onKeyup:O},f)}}}),je={radio:Yt,checkbox:qt,toggle:Ct},$t=Object.keys(je);function he(e,D){if(typeof e=="function")return e;const b=e!==void 0?e:D;return v=>v[b]}const Be=ne({name:"QOptionGroup",props:{...Fe,modelValue:{required:!0},options:{type:Array,validator:e=>e.every(vt),default:()=>[]},optionValue:[Function,String],optionLabel:[Function,String],optionDisable:[Function,String],name:String,type:{type:String,default:"radio",validator:e=>$t.includes(e)},color:String,keepColor:Boolean,dense:Boolean,size:String,leftLabel:Boolean,inline:Boolean,disable:Boolean},emits:["update:modelValue"],setup(e,{emit:D,slots:b}){const{proxy:{$q:v}}=oe(),s=Array.isArray(e.modelValue);e.type==="radio"?s===!0&&console.error("q-option-group: model should not be array"):s===!1&&console.error("q-option-group: model should be array in your case");const R=Ue(e,v),I=d(()=>je[e.type]),V=d(()=>he(e.optionValue,"value")),Y=d(()=>he(e.optionLabel,"label")),S=d(()=>he(e.optionDisable,"disable")),Q=d(()=>e.options.map(o=>({val:V.value(o),name:o.name===void 0?e.name:o.name,disable:e.disable||S.value(o),leftLabel:o.leftLabel===void 0?e.leftLabel:o.leftLabel,color:o.color===void 0?e.color:o.color,checkedIcon:o.checkedIcon,uncheckedIcon:o.uncheckedIcon,dark:o.dark===void 0?R.value:o.dark,size:o.size===void 0?e.size:o.size,dense:e.dense,keepColor:o.keepColor===void 0?e.keepColor:o.keepColor}))),M=d(()=>"q-option-group q-gutter-x-sm"+(e.inline===!0?" q-option-group--inline":"")),C=d(()=>{const o={role:"group"};return e.type==="radio"&&(o.role="radiogroup",e.disable===!0&&(o["aria-disabled"]="true")),o});function g(o){D("update:modelValue",o)}return()=>h("div",{class:M.value,...C.value},e.options.map((o,_)=>{const x=b["label-"+_]!==void 0?()=>b["label-"+_](o):b.label!==void 0?()=>b.label(o):void 0;return h("div",[h(I.value,{label:x===void 0?Y.value(o):null,modelValue:e.modelValue,"onUpdate:modelValue":g,...Q.value[_]},x)])}))}}),At={class:"a-container-lg q-mb-xl"},Pt={class:"q-gutter-sm flex"},Ot={class:"q-pa-md q-col-gutter-md"},Ft={class:"row q-col-gutter-md"},Ut={class:"col-md-6 col-sm-12"},jt={class:"col-md-6 col-sm-12"},Et={class:"row q-col-gutter-md"},zt={class:"col-md-6 col-sm-12"},Ht={class:"col-md-6 col-sm-12"},Kt={class:"flex items-center justify-between",style:{gap:"10px"}},Nt={class:"label text-caption text-grey-7"},ba={__name:"DataPage",setup(e){const D=y(!1),b=y("range"),v=d(()=>({dense:D.value})),s=[{label:"Q1 (Jan-Mar)",value:"Q1"},{label:"Q2 (Apr-Jun)",value:"Q2"},{label:"Q3 (Jul-Sep)",value:"Q3"},{label:"Q4 (Oct-Dec)",value:"Q4"}],R=y("fine"),I=[{label:"Fine",value:"fine"},{label:"Coarse",value:"coarse"}],V=y("utc"),Y=[{label:"UTC",value:"utc"},{label:"Local",value:"local"},{label:"System",value:"system"}],S=y("unix"),Q=[{label:"Unix",value:"unix"},{label:"Local (YYYY-MM-DD HH:mm:ss)",value:"local"},{label:"System (YYYY-MM-DD HH:mm:ss)",value:"system"},{label:"UTC (YYYY-MM-DD HH:mm:ss)",value:"utc"}],M=y(null),C=y("2025-06-01"),g=y("2025-06-30"),o=y(null),_=y("Q1"),x=d(()=>{const f=new Date(M.value.t0).getFullYear(),a=new Date(M.value.t1).getFullYear();return Array.from({length:a-f+1},(n,L)=>({label:`${f+L}`,value:f+L})).reverse()});function $(){g.value=G().format("YYYY-MM-DD")}function O(){const a=["utc","system"].includes(V.value)?N=>G.utc(N):G,n=o.value,L={Q1:[0,2],Q2:[3,5],Q3:[6,8],Q4:[9,11]};let E,z;switch(b.value){case"year":E=a(`${n}-01-01`).startOf("day"),z=a(`${n}-12-31`).endOf("day");break;case"quarter":{const[N,re]=L[_.value];E=a(`${n}-${(N+1+"").padStart(2,"0")}-01`).startOf("day"),z=a(`${n}-${(re+1+"").padStart(2,"0")}-01`).endOf("month");break}case"range":default:E=a(C.value).startOf("day"),z=a(g.value).endOf("day")}return{t0:E.unix(),t1:z.unix()}}function u(){const f=document.createElement("a");f.href=`${Vt}/download?${new URLSearchParams({...O(),res:R.value,tFormat:S.value,anchor:V.value,...V.value==="local"&&{timezone:Intl.DateTimeFormat().resolvedOptions().timeZone}})}`,document.body.append(f),f.click(),f.remove()}function i(){St.get("/download/available").then(f=>{const a={t0:G.unix(f.data.t0).utc().format("YYYY-MM-DD"),t1:G.unix(f.data.t1).utc().format("YYYY-MM-DD")},n=new Date(a.t1).getFullYear();C.value=a.t0,g.value=a.t1,o.value=n,M.value=a})}return Ae(()=>{i()}),(f,a)=>(ae(),ft(Tt,null,{default:B(()=>[T("div",At,[k(Dt,{title:"Download Data",icon:"download"},{default:B(()=>[T("div",Pt,[k(It,{modelValue:b.value,"onUpdate:modelValue":a[0]||(a[0]=n=>b.value=n),"no-caps":"","outside-arrows":"","mobile-arrows":""},{default:B(()=>[k(ge,{name:"range",label:"Date Range"}),k(ge,{name:"year",label:"Calendar Year"}),k(ge,{name:"quarter",label:"Calendar Quarter"})]),_:1},8,["modelValue"]),k(_t,{round:"",flat:"",modelValue:D.value,"onUpdate:modelValue":a[1]||(a[1]=n=>D.value=n),"local-store-key":"downloadPageShowDense","default-value":!0,"color-true":"grey-5","color-false":"grey-5","icon-true":"compress","icon-false":"expand","tooltip-true":"Click to expand the form spacing","tooltip-false":"Click to make the form compact"},null,8,["modelValue"])])]),_:1}),T("div",Ot,[b.value==="range"?(ae(),ve(me,{key:0},[a[13]||(a[13]=T("div",{class:"text-h6"},"Date Range",-1)),T("div",Ft,[T("div",Ut,[k(Ie,A(v.value,{filled:"",label:"Start Date",modelValue:C.value,"onUpdate:modelValue":a[3]||(a[3]=n=>C.value=n),mask:"####-##-##"}),{append:B(()=>[k(j,{name:"event",class:"cursor-pointer"},{default:B(()=>[k(Le,null,{default:B(()=>[k(Re,{modelValue:C.value,"onUpdate:modelValue":a[2]||(a[2]=n=>C.value=n),mask:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},16,["modelValue"])]),T("div",jt,[k(Ie,A(v.value,{filled:"",label:"End Date",modelValue:g.value,"onUpdate:modelValue":a[5]||(a[5]=n=>g.value=n),mask:"####-##-##"}),{append:B(()=>[k(j,{name:"event",class:"cursor-pointer"},{default:B(()=>[k(Le,null,{default:B(()=>[k(Re,{modelValue:g.value,"onUpdate:modelValue":a[4]||(a[4]=n=>g.value=n),mask:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_:1})]),after:B(()=>[k(pe,A(v.value,{onClick:$,color:"primary"}),{default:B(()=>a[12]||(a[12]=[mt("Today")])),_:1,__:[12]},16)]),_:1},16,["modelValue"])])])],64)):fe("",!0),b.value==="year"?(ae(),ve(me,{key:1},[a[14]||(a[14]=T("div",{class:"text-h6"},"Calendar Year",-1)),k(le,A(v.value,{filled:"",label:"Select Year",modelValue:o.value,"onUpdate:modelValue":a[6]||(a[6]=n=>o.value=n),options:x.value,"emit-value":"","map-options":""}),null,16,["modelValue","options"])],64)):fe("",!0),b.value==="quarter"?(ae(),ve(me,{key:2},[a[15]||(a[15]=T("div",{class:"text-h6"},"Calendar Quarter",-1)),T("div",Et,[T("div",zt,[k(le,A(v.value,{filled:"",label:"Select Year",modelValue:o.value,"onUpdate:modelValue":a[7]||(a[7]=n=>o.value=n),options:x.value,"emit-value":"","map-options":""}),null,16,["modelValue","options"])]),T("div",Ht,[k(le,A(v.value,{filled:"",label:"Select Quarter",modelValue:_.value,"onUpdate:modelValue":a[8]||(a[8]=n=>_.value=n),options:s,"emit-value":"","map-options":""}),null,16,["modelValue"])])])],64)):fe("",!0),T("div",null,[a[16]||(a[16]=T("div",null,"Data Resolution",-1)),k(Be,A(v.value,{modelValue:R.value,"onUpdate:modelValue":a[9]||(a[9]=n=>R.value=n),options:I,type:"radio",inline:""}),null,16,["modelValue"])]),T("div",null,[a[17]||(a[17]=T("div",null,"Date Anchor",-1)),k(Be,A(v.value,{modelValue:V.value,"onUpdate:modelValue":a[10]||(a[10]=n=>V.value=n),options:Y,type:"radio",inline:""}),null,16,["modelValue"])]),T("div",null,[a[18]||(a[18]=T("div",{class:"text-subtitle2"},"Timestamp Format",-1)),k(le,A(v.value,{filled:"",modelValue:S.value,"onUpdate:modelValue":a[11]||(a[11]=n=>S.value=n),options:Q,"emit-value":"","map-options":""}),null,16,["modelValue"])]),T("div",Kt,[T("div",Nt," Data available from "+Me(M.value?.t0)+" to "+Me(M.value?.t1),1),k(pe,A(v.value,{label:"Download",color:"primary",onClick:u}),null,16)])])])]),_:1}))}};export{ba as default};
