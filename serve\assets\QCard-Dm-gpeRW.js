import{c as o,a as l,h as d,C as i,b as u,g as s}from"./index-CzmOWWdj.js";import{a as c,c as g}from"./use-key-composition-CoMUTTxZ.js";const b=["top","middle","bottom"],v=o({name:"QBadge",props:{color:String,textColor:String,floating:Boolean,transparent:Boolean,multiLine:Boolean,outline:Boolean,rounded:Boolean,label:[Number,String],align:{type:String,validator:e=>b.includes(e)}},setup(e,{slots:a}){const t=l(()=>e.align!==void 0?{verticalAlign:e.align}:null),r=l(()=>{const n=e.outline===!0&&e.color||e.textColor;return`q-badge flex inline items-center no-wrap q-badge--${e.multiLine===!0?"multi":"single"}-line`+(e.outline===!0?" q-badge--outline":e.color!==void 0?` bg-${e.color}`:"")+(n!==void 0?` text-${n}`:"")+(e.floating===!0?" q-badge--floating":"")+(e.rounded===!0?" q-badge--rounded":"")+(e.transparent===!0?" q-badge--transparent":"")});return()=>d("div",{class:r.value,style:t.value,role:"status","aria-label":e.label},i(a.default,e.label!==void 0?[e.label]:[]))}}),f=o({name:"QCardSection",props:{tag:{type:String,default:"div"},horizontal:Boolean},setup(e,{slots:a}){const t=l(()=>`q-card__section q-card__section--${e.horizontal===!0?"horiz row no-wrap":"vert"}`);return()=>d(e.tag,{class:t.value},u(a.default))}}),B=o({name:"QCard",props:{...c,tag:{type:String,default:"div"},square:Boolean,flat:Boolean,bordered:Boolean},setup(e,{slots:a}){const{proxy:{$q:t}}=s(),r=g(e,t),n=l(()=>"q-card"+(r.value===!0?" q-card--dark q-dark":"")+(e.bordered===!0?" q-card--bordered":"")+(e.square===!0?" q-card--square no-border-radius":"")+(e.flat===!0?" q-card--flat no-shadow":""));return()=>d(e.tag,{class:n.value},u(a.default))}});export{B as Q,f as a,v as b};
