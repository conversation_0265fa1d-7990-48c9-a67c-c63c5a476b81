import{Q as D}from"./use-key-composition-CoMUTTxZ.js";import{a as T,Q as $}from"./QSelect-BNWcW_ch.js";import{Q as V}from"./QInput-CCOJFSf5.js";import{Q as P}from"./QToggle-D9UX9nQt.js";import{r as _,w as A,o as E,a as I,f as M,D as i,F as a,G as d,X as l,H as u,T as f,E as v,S as p,J as g,I as U,M as k,Q as j,U as Y,Y as z}from"./index-CzmOWWdj.js";import{Q as G}from"./QPage-Disdm55v.js";import{_ as H}from"./PageHeading-CorBRYVa.js";import{r as J}from"./SaveButton-DQ7yxOUN.js";import{_ as L}from"./AqArrayForm-C93bdg5l.js";import{_ as x}from"./AqPersistToggleButton-BcqzKwP0.js";import{i as q}from"./Login-BVtZ5S7B.js";import{u as O}from"./info-store-rjwJi5x9.js";import{u as R}from"./use-quasar-Li8tSQ3f.js";import"./use-timeout-DeCFbuIx.js";import"./QDialog-Cuvxr_1w.js";import"./focusout-C-pmmZED.js";import"./QMenu-D3shCIOy.js";import"./use-checkbox-DDSE4paG.js";import"./QSpace-i2TdLNVM.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./QSeparator-D-fNGoQY.js";const X={class:"a-container-lg q-mb-xl"},K={class:"q-gutter-sm"},W={class:"col-fit"},Z={class:"text-center rounded-borders q-py-sm bg-highlight full-height",style:{"min-width":"3em"}},ee={class:"col-3"},te={class:"self-center full-width"},oe={class:"col-3"},le={class:"col-fit"},ae={class:"col"},se={key:0,class:"text-h6 q-ma-sm text-black fixed-bottom-right q-pa-md bg-warning rounded-borders",style:{opacity:"0.7"}},re={key:1,class:"q-mt-md"},S="/setup/settings",Ce={__name:"SettingsPage",setup(ne){const Q=O(),C=R(),F=[{label:"String",value:"text"},{label:"Number",value:"number"},{label:"Boolean",value:"boolean"}],r=new J({name:"example",value:null},"name");r.deleteFilter=t=>!!t.required,r.duplicateFunction=t=>(delete t.required,t);const y=_(!1),m=_(!0),s=_(!1);let c=null;A(r.duplicateError,t=>{t?c=C.notify({type:"negative",icon:"error_outline",iconSize:"2.5rem",timeout:0,html:!0,message:'There are duplicate parameters: <span class="q-notify-duplicate">'+t+"</span>",actions:[{icon:"close",color:"white",round:!0}]}):c&&(c(),c=null)}),E(()=>{c&&c()});const h=I(()=>({dense:m.value,outlined:s.value,readonly:!s.value,standout:!s.value}));M(()=>{console.log("SettingsPage mounted"),r.get(S)});function N(){console.log("postForm",r.value),r.post(S).then(t=>{console.log("postForm",t),Q.load()})}function w(t,o){t.type==="number"?t.value=isNaN(t.value)?0:Number(o):t.type==="text"?t.value=o+"":t.type==="boolean"?t.value=!!o:t.value=o}function B(t,o){t.type=o,w(t,t.value)}return(t,o)=>(a(),i(G,null,{default:d(()=>[l("div",X,[u(H,{title:"Settings",icon:"settings"},{default:d(()=>[l("div",K,[p(q)?(a(),i(x,{key:0,round:"",flat:"",modelValue:y.value,"onUpdate:modelValue":o[0]||(o[0]=e=>y.value=e),"local-store-key":"settingsShowCode","default-value":!1,"color-true":"primary","color-false":"grey-5","icon-true":"code","icon-false":"code_off","tooltip-true":"Click to hide the code","tooltip-false":"Click to show the code"},null,8,["modelValue"])):v("",!0),p(q)?(a(),i(x,{key:1,round:"",flat:"",modelValue:s.value,"onUpdate:modelValue":o[1]||(o[1]=e=>s.value=e),"local-store-key":"settingsEdit","default-value":!1,"color-true":"primary","color-false":"grey-5","icon-true":"edit","icon-false":"edit_off","tooltip-true":"Click to disable editing","tooltip-false":"Click to enable editing"},null,8,["modelValue"])):v("",!0),u(x,{round:"",flat:"",modelValue:m.value,"onUpdate:modelValue":o[2]||(o[2]=e=>m.value=e),"local-store-key":"settingsShowDense","default-value":!0,"color-true":"grey-5","color-false":"grey-5","icon-true":"compress","icon-false":"expand","tooltip-true":"Click to expand the form spacing","tooltip-false":"Click to make the form compact"},null,8,["modelValue"])])]),_:1}),u(L,{"reactive-array":p(r),onReset:o[3]||(o[3]=e=>p(r).reset()),onSubmit:o[4]||(o[4]=e=>N()),enable:s.value,class:"q-pt-sm"},{header:d(()=>o[5]||(o[5]=[l("div",{class:"col-fit"},[l("div",{class:"text-center rounded-borders q-py-sm bg-highlight",style:{"min-width":"3em"}}," # ")],-1),l("div",{class:"col-3"},[l("div",{class:"text-center rounded-borders q-py-sm bg-highlight"}," Parameter ")],-1),l("div",{class:"col-3"},[l("div",{class:"text-center rounded-borders q-py-sm bg-highlight"}," Value ")],-1),l("div",{class:"col-fit"},[l("div",{class:"text-center rounded-borders q-py-sm bg-highlight",style:{width:"7em"}}," Type ")],-1),l("div",{class:"col"},[l("div",{class:"text-center rounded-borders q-py-sm bg-highlight"}," Description ")],-1)])),default:d(({item:e,index:b})=>[l("div",W,[l("div",Z,g(b),1)]),l("div",ee,[e.required?(a(),i(T,{key:0,standout:"",dense:m.value},{control:d(()=>[l("div",te,g(e.name),1)]),default:d(()=>[u(D,{anchor:"center right",self:"center left",class:"text-body2","transition-show":"jump-right","transition-hide":"jump-left"},{default:d(()=>[U(g(e.name)+" is a required parameter ",1)]),_:2},1024)]),_:2},1032,["dense"])):(a(),i(V,k({key:1,modelValue:e.name,"onUpdate:modelValue":n=>e.name=n},h.value),null,16,["modelValue","onUpdate:modelValue"]))]),l("div",oe,[e.type==="boolean"?(a(),i(P,{key:0,modelValue:e.value,"onUpdate:modelValue":n=>e.value=n,label:e.value?"true":"false",disable:!s.value},null,8,["modelValue","onUpdate:modelValue","label","disable"])):(a(),i(V,k({key:1,"model-value":e.value,"onUpdate:modelValue":n=>w(e,n)},h.value,{type:e.type}),null,16,["model-value","onUpdate:modelValue","type"]))]),l("div",le,[u($,{"model-value":e.type||"text",options:F,"onUpdate:modelValue":n=>B(e,n),"emit-value":"","map-options":"",style:{width:"7em"},readonly:!s.value||e.required,standout:!s.value||e.required,outlined:s.value&&!e.required,dense:m.value},null,8,["model-value","onUpdate:modelValue","readonly","standout","outlined","dense"])]),l("div",ae,[u(V,k({modelValue:e.description,"onUpdate:modelValue":n=>e.description=n},h.value),null,16,["modelValue","onUpdate:modelValue"])])]),_:1},8,["reactive-array","enable"]),p(q)?v("",!0):(a(),f("div",se,[u(j,{name:"settings"}),o[6]||(o[6]=U(" You must be logged in as an administrator to edit the Settings "))])),y.value?(a(),f("div",re,[(a(!0),f(Y,null,z(p(r).value,(e,b)=>(a(),f("pre",{key:b},g(e),1))),128))])):v("",!0),o[7]||(o[7]=l("div",{style:{height:"100px"}},null,-1))])]),_:1}))}};export{Ce as default};
