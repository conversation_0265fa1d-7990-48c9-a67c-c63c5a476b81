const fs = require('fs')
const path = require('path')
const readline = require('readline')
const { postDataBatch } = require('./post_functions.js')

/**
 * Processes historical CSV data and posts it to server in batches
 * @param {number} dataTime - Start timestamp (Unix seconds)
 * @param {number} latestTimestamp - End timestamp (Unix seconds)
 * @param {Object} config - Configuration object
 * @returns {Promise<void>} - Resolves when all data is processed
 */
async function processHistoricalData(dataTime, latestTimestamp, config) {
    console.log(`[SVC] Starting CSV data processing from t0: ${dataTime} to t1: ${latestTimestamp}`)
    
    const { 
        projectFinePath, 
        datalogResolution: resolution, 
        batchSize, 
        serverUrl, 
        mac, 
        signal 
    } = config
    
    // Get files in date range
    const files = getFilesInRange(projectFinePath, resolution, dataTime, latestTimestamp)
    console.log(`[SVC] Found ${files.length} files to process`)
    
    // Process files sequentially
    let totalProcessed = 0
    for (const file of files) {
        const processedCount = await processFile(
            file, 
            dataTime, 
            latestTimestamp, 
            batchSize, 
            serverUrl, 
            mac, 
            signal
        )
        totalProcessed += processedCount
    }

    console.log(`[SVC] Total processed: ${totalProcessed} items`)
}

/**
 * Gets CSV files within the specified time range
 */
function getFilesInRange(projectFinePath, resolution, startTime, endTime) {
    // Check if projectFinePath already ends with the resolution to avoid duplication
    const datalogDirectory = projectFinePath.endsWith(resolution) 
        ? projectFinePath 
        : path.join(projectFinePath, resolution)
    console.log(`[SVC] Reading from directory: ${datalogDirectory}`)
    
    const startDay = Math.floor(startTime / 86400)
    const endDay = Math.floor(endTime / 86400)
    const prefix = resolution === 'fine' ? 'D' : 'DC'
    
    return fs.readdirSync(datalogDirectory)
        .filter(file => file.startsWith(prefix) && file.endsWith('.csv'))
        .map(file => {
            const day = parseInt(file.slice(prefix.length, -4), 10)
            return { file: path.join(datalogDirectory, file), day }
        })
        .filter(({ day }) => day >= startDay && day <= endDay)
        .sort((a, b) => a.day - b.day)
        .map(({ file }) => file)
}

/**
 * Processes a single CSV file and posts data in batches
 */
async function processFile(file, startTime, endTime, batchSize, serverUrl, mac, signal) {
    console.log(`[SVC] Processing file: ${file}`)
    
    const rl = readline.createInterface({
        input: fs.createReadStream(file),
        crlfDelay: Infinity
    })

    let batch = []
    let processedCount = 0

    for await (const line of rl) {
        const values = line.split(',')
        const timestamp = parseInt(values[0], 10)
        
        if (timestamp >= startTime && timestamp <= endTime) {
            // convert CSV array of numbers/null 
            const dataPoint = values.map(val => {
                const num = Number(val)
                return isNaN(num) ? null : num
            })
            
            batch.push(dataPoint)
            processedCount++

            if (batch.length >= batchSize) {
                await postBatch(batch, serverUrl, mac, signal)
                batch = []
            }
        }
    }

    // Post any remaining data
    if (batch.length > 0) {
        await postBatch(batch, serverUrl, mac, signal)
    }

    console.log(`[SVC] Finished processing file: ${file}`)
    return processedCount
}

/**
 * Posts a batch of data to the server
 */
async function postBatch(batch, serverUrl, mac, signal) {
    console.log(`[SVC] Posting batch of ${batch.length} items`)
    await postDataBatch([...batch], serverUrl, mac, signal)
}

module.exports = { processHistoricalData }