
console.log('========== DataPoster Mock Constants Test ==========\n')

// Mock the project settings
const mockProjectSettings = {
    CLOUD_SERVER_ENABLE: true,
    // CLOUD_SERVER_URL: 'http://localhost:9001/api/device/data',
    CLOUD_SERVER_URL: 'https://httpbin.org/delay/1',
    PROJECT_NAME: 'Test Project'
}

// Import the DataPoster module
const DataPoster = require('./data_poster/data_poster.js')
const path = require('path')

// Initialize with test configuration 
const testConfig = {
    fine: path.resolve(__dirname, '../project/Roxborough/datalog/fine'),
    metadata: path.resolve(__dirname, '../project/Roxborough/datalog/metadata.json'),
    serverUrl: mockProjectSettings.CLOUD_SERVER_URL,
    enable: mockProjectSettings.CLOUD_SERVER_ENABLE
}

console.log('1. Initializing DataPoster with:')
console.log(`   Enable: ${testConfig.enable}`)
console.log(`   URL: ${testConfig.serverUrl}`)
console.log(`   Fine path: ${testConfig.fine}`)
console.log(`   Metadata path: ${testConfig.metadata}`)
DataPoster.init(testConfig)

// Create sample data
const createSampleData = () => [Math.floor(Date.now() / 1000), 25.5, 30.2, null, 1.5]

// Simulate system timer calling DataPoster.svc with current settings
function simulateTimerTick() {
    console.log(`\nTimer tick with:`)
    console.log(`   Enable: ${mockProjectSettings.CLOUD_SERVER_ENABLE}`)
    console.log(`   URL: ${mockProjectSettings.CLOUD_SERVER_URL}`)
    
    DataPoster.svc({
        enable: mockProjectSettings.CLOUD_SERVER_ENABLE,
        serverUrl: mockProjectSettings.CLOUD_SERVER_URL
    }, createSampleData())
}

// Run test scenarios
async function runTest() {
    // Initial state
    simulateTimerTick()
    
    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 1000))

    console.log('\n2.  - changing URL while enabled')
    mockProjectSettings.CLOUD_SERVER_URL = 'https://httpbin.org/delay/0.5';
    simulateTimerTick()
    
    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Scenario 1: Disable cloud server
    console.log('\n3.  - disabling cloud server')
    mockProjectSettings.CLOUD_SERVER_ENABLE = false
    // console.log(mockProjectSettings)
    simulateTimerTick()
    
    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Scenario 2: Change URL but keep disabled
    console.log('\n4.  - changing URL while disabled')
    mockProjectSettings.CLOUD_SERVER_URL = 'https://httpbin.org/delay/0.9'
    // console.log(mockProjectSettings)
    simulateTimerTick()
    
    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Scenario 3: Re-enable with new URL
    console.log('\n5.  - re-enabling with new URL')
    mockProjectSettings.CLOUD_SERVER_ENABLE = true
    // console.log(mockProjectSettings)
    simulateTimerTick()
    
    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 2000))

    simulateTimerTick()
    
    // Finish test
    console.log('\n6. Test complete')
    // await DataPoster.shutdown()
}

// Run the test
runTest().catch(err => {
    console.error('Test error:', err)
    process.exit(1)
})