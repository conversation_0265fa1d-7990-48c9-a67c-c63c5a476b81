import{o as W,aE as O,aL as ue,c as L,a as n,h as u,b as U,an as ce,g as G,ao as re,k as de,aj as fe,Q as D,aM as me,a8 as ve,r as H,w as j,aa as A,aN as he}from"./index-CzmOWWdj.js";import{a as be,c as ge,u as ye,w as ke,b as qe,j as xe,x as Se,y as _e,d as Be,n as we}from"./use-key-composition-CoMUTTxZ.js";import{u as Ce}from"./use-timeout-DeCFbuIx.js";import{c as Te,d as Ee,r as K,e as V}from"./focusout-C-pmmZED.js";function Fe(e,s,i){let a;function r(){a!==void 0&&(O.remove(a),a=void 0)}return W(()=>{e.value===!0&&r()}),{removeFromHistory:r,addToHistory(){a={condition:()=>i.value===!0,handler:s},O.add(a)}}}function Re(){let e;return{preventBodyScroll(s){s!==e&&(e!==void 0||s===!0)&&(e=s,ue(s))}}}const Me=L({name:"QItemLabel",props:{overline:Boolean,caption:Boolean,header:Boolean,lines:[Number,String]},setup(e,{slots:s}){const i=n(()=>parseInt(e.lines,10)),a=n(()=>"q-item__label"+(e.overline===!0?" q-item__label--overline text-overline":"")+(e.caption===!0?" q-item__label--caption text-caption":"")+(e.header===!0?" q-item__label--header":"")+(i.value===1?" ellipsis":"")),r=n(()=>e.lines!==void 0&&i.value>1?{overflow:"hidden",display:"-webkit-box","-webkit-box-orient":"vertical","-webkit-line-clamp":i.value}:null);return()=>u("div",{style:r.value,class:a.value},U(s.default))}}),ze={xs:8,sm:10,md:14,lg:20,xl:24},$e=L({name:"QChip",props:{...be,...ce,dense:Boolean,icon:String,iconRight:String,iconRemove:String,iconSelected:String,label:[String,Number],color:String,textColor:String,modelValue:{type:Boolean,default:!0},selected:{type:Boolean,default:null},square:Boolean,outline:Boolean,clickable:Boolean,removable:Boolean,removeAriaLabel:String,tabindex:[String,Number],disable:Boolean,ripple:{type:[Boolean,Object],default:!0}},emits:["update:modelValue","update:selected","remove","click"],setup(e,{slots:s,emit:i}){const{proxy:{$q:a}}=G(),r=ge(e,a),d=re(e,ze),m=n(()=>e.selected===!0||e.icon!==void 0),v=n(()=>e.selected===!0?e.iconSelected||a.iconSet.chip.selected:e.icon),f=n(()=>e.iconRemove||a.iconSet.chip.remove),c=n(()=>e.disable===!1&&(e.clickable===!0||e.selected!==null)),h=n(()=>{const l=e.outline===!0&&e.color||e.textColor;return"q-chip row inline no-wrap items-center"+(e.outline===!1&&e.color!==void 0?` bg-${e.color}`:"")+(l?` text-${l} q-chip--colored`:"")+(e.disable===!0?" disabled":"")+(e.dense===!0?" q-chip--dense":"")+(e.outline===!0?" q-chip--outline":"")+(e.selected===!0?" q-chip--selected":"")+(c.value===!0?" q-chip--clickable cursor-pointer non-selectable q-hoverable":"")+(e.square===!0?" q-chip--square":"")+(r.value===!0?" q-chip--dark q-dark":"")}),b=n(()=>{const l=e.disable===!0?{tabindex:-1,"aria-disabled":"true"}:{tabindex:e.tabindex||0},q={...l,role:"button","aria-hidden":"false","aria-label":e.removeAriaLabel||a.lang.label.remove};return{chip:l,remove:q}});function S(l){l.keyCode===13&&y(l)}function y(l){e.disable||(i("update:selected",!e.selected),i("click",l))}function k(l){(l.keyCode===void 0||l.keyCode===13)&&(ve(l),e.disable===!1&&(i("update:modelValue",!1),i("remove")))}function C(){const l=[];c.value===!0&&l.push(u("div",{class:"q-focus-helper"})),m.value===!0&&l.push(u(D,{class:"q-chip__icon q-chip__icon--left",name:v.value}));const q=e.label!==void 0?[u("div",{class:"ellipsis"},[e.label])]:void 0;return l.push(u("div",{class:"q-chip__content col row no-wrap items-center q-anchor--skip"},me(s.default,q))),e.iconRight&&l.push(u(D,{class:"q-chip__icon q-chip__icon--right",name:e.iconRight})),e.removable===!0&&l.push(u(D,{class:"q-chip__icon q-chip__icon--remove cursor-pointer",name:f.value,...b.value.remove,onClick:k,onKeyup:k})),l}return()=>{if(e.modelValue===!1)return;const l={class:h.value,style:d.value};return c.value===!0&&Object.assign(l,b.value.chip,{onClick:y,onKeyup:S}),de("div",l,C(),"ripple",e.ripple!==!1&&e.disable!==!0,()=>[[fe,e.ripple]])}}});let w=0;const De={standard:"fixed-full flex-center",top:"fixed-top justify-center",bottom:"fixed-bottom justify-center",right:"fixed-right items-center",left:"fixed-left items-center"},N={standard:["scale","scale"],top:["slide-down","slide-up"],bottom:["slide-up","slide-down"],right:["slide-left","slide-right"],left:["slide-right","slide-left"]},Qe=L({name:"QDialog",inheritAttrs:!1,props:{...qe,...ke,transitionShow:String,transitionHide:String,persistent:Boolean,autoClose:Boolean,allowFocusOutside:Boolean,noEscDismiss:Boolean,noBackdropDismiss:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,noShake:Boolean,seamless:Boolean,maximized:Boolean,fullWidth:Boolean,fullHeight:Boolean,square:Boolean,backdropFilter:String,position:{type:String,default:"standard",validator:e=>["standard","top","bottom","left","right"].includes(e)}},emits:[...ye,"shake","click","escapeKey"],setup(e,{slots:s,emit:i,attrs:a}){const r=G(),d=H(null),m=H(!1),v=H(!1);let f=null,c=null,h,b;const S=n(()=>e.persistent!==!0&&e.noRouteDismiss!==!0&&e.seamless!==!0),{preventBodyScroll:y}=Re(),{registerTimeout:k}=Ce(),{registerTick:C,removeTick:l}=xe(),{transitionProps:q,transitionStyle:P}=Se(e,()=>N[e.position][0],()=>N[e.position][1]),J=n(()=>P.value+(e.backdropFilter!==void 0?`;backdrop-filter:${e.backdropFilter};-webkit-backdrop-filter:${e.backdropFilter}`:"")),{showPortal:I,hidePortal:M,portalIsAccessible:X,renderPortal:Y}=_e(r,d,se,"dialog"),{hide:_}=Be({showing:m,hideOnRouteChange:S,handleShow:ie,handleHide:oe,processOnMount:!0}),{addToHistory:Z,removeFromHistory:p}=Fe(m,_,S),ee=n(()=>`q-dialog__inner flex no-pointer-events q-dialog__inner--${e.maximized===!0?"maximized":"minimized"} q-dialog__inner--${e.position} ${De[e.position]}`+(v.value===!0?" q-dialog__inner--animating":"")+(e.fullWidth===!0?" q-dialog__inner--fullwidth":"")+(e.fullHeight===!0?" q-dialog__inner--fullheight":"")+(e.square===!0?" q-dialog__inner--square":"")),B=n(()=>m.value===!0&&e.seamless!==!0),te=n(()=>e.autoClose===!0?{onClick:ae}:{}),le=n(()=>[`q-dialog fullscreen no-pointer-events q-dialog--${B.value===!0?"modal":"seamless"}`,a.class]);j(()=>e.maximized,t=>{m.value===!0&&F(t)}),j(B,t=>{y(t),t===!0?(Te(R),Ee(E)):(K(R),V(E))});function ie(t){Z(),c=e.noRefocus===!1&&document.activeElement!==null?document.activeElement:null,F(e.maximized),I(),v.value=!0,e.noFocus!==!0?(document.activeElement?.blur(),C(x)):l(),k(()=>{if(r.proxy.$q.platform.is.ios===!0){if(e.seamless!==!0&&document.activeElement){const{top:o,bottom:g}=document.activeElement.getBoundingClientRect(),{innerHeight:Q}=window,z=window.visualViewport!==void 0?window.visualViewport.height:Q;o>0&&g>z/2&&(document.scrollingElement.scrollTop=Math.min(document.scrollingElement.scrollHeight-z,g>=Q?1/0:Math.ceil(document.scrollingElement.scrollTop+g-z/2))),document.activeElement.scrollIntoView()}b=!0,d.value.click(),b=!1}I(!0),v.value=!1,i("show",t)},e.transitionDuration)}function oe(t){l(),p(),$(!0),v.value=!0,M(),c!==null&&(((t?.type.indexOf("key")===0?c.closest('[tabindex]:not([tabindex^="-"])'):void 0)||c).focus(),c=null),k(()=>{M(!0),v.value=!1,i("hide",t)},e.transitionDuration)}function x(t){we(()=>{let o=d.value;if(o!==null){if(t!==void 0){const g=o.querySelector(t);if(g!==null){g.focus({preventScroll:!0});return}}o.contains(document.activeElement)!==!0&&(o=o.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||o.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||o.querySelector("[autofocus], [data-autofocus]")||o,o.focus({preventScroll:!0}))}})}function T(t){t&&typeof t.focus=="function"?t.focus({preventScroll:!0}):x(),i("shake");const o=d.value;o!==null&&(o.classList.remove("q-animate--scale"),o.classList.add("q-animate--scale"),f!==null&&clearTimeout(f),f=setTimeout(()=>{f=null,d.value!==null&&(o.classList.remove("q-animate--scale"),x())},170))}function E(){e.seamless!==!0&&(e.persistent===!0||e.noEscDismiss===!0?e.maximized!==!0&&e.noShake!==!0&&T():(i("escapeKey"),_()))}function $(t){f!==null&&(clearTimeout(f),f=null),(t===!0||m.value===!0)&&(F(!1),e.seamless!==!0&&(y(!1),K(R),V(E))),t!==!0&&(c=null)}function F(t){t===!0?h!==!0&&(w<1&&document.body.classList.add("q-body--dialog"),w++,h=!0):h===!0&&(w<2&&document.body.classList.remove("q-body--dialog"),w--,h=!1)}function ae(t){b!==!0&&(_(t),i("click",t))}function ne(t){e.persistent!==!0&&e.noBackdropDismiss!==!0?_(t):e.noShake!==!0&&T()}function R(t){e.allowFocusOutside!==!0&&X.value===!0&&he(d.value,t.target)!==!0&&x('[tabindex]:not([tabindex="-1"])')}Object.assign(r.proxy,{focus:x,shake:T,__updateRefocusTarget(t){c=t||null}}),W($);function se(){return u("div",{role:"dialog","aria-modal":B.value===!0?"true":"false",...a,class:le.value},[u(A,{name:"q-transition--fade",appear:!0},()=>B.value===!0?u("div",{class:"q-dialog__backdrop fixed-full",style:J.value,"aria-hidden":"true",tabindex:-1,onClick:ne}):null),u(A,q.value,()=>m.value===!0?u("div",{ref:d,class:ee.value,style:P.value,tabindex:-1,...te.value},U(s.default)):null)])}return Y}});export{Me as Q,Re as a,Qe as b,$e as c,Fe as u};
