const winston = require('winston');
const fs = require('fs');

console.log('Testing working rotation...');

// Clean up any existing test files
const testFiles = fs.readdirSync('./logs').filter(f => f.includes('working-test'));
testFiles.forEach(file => {
    fs.unlinkSync(`./logs/${file}`);
    console.log(`Removed ${file}`);
});

const logger = winston.createLogger({
    transports: [
        new winston.transports.File({
            filename: './logs/working-test.log',
            maxsize: 1000, // 1KB
            maxFiles: 2,
            tailable: true,
            format: winston.format.combine(
                winston.format.timestamp(),
                winston.format.printf(info => {
                    return `${info.timestamp} [${info.level}] ${info.message}`;
                })
            )
        }),
        new winston.transports.Console({
            format: winston.format.simple()
        })
    ]
});

// Function to log and wait
function logAndWait(message, delay = 100) {
    return new Promise(resolve => {
        logger.info(message);
        setTimeout(resolve, delay);
    });
}

// Test rotation step by step
async function testRotation() {
    console.log('\n=== Starting rotation test ===');
    
    // Log some messages
    for(let i = 0; i < 15; i++) {
        await logAndWait(`Message ${i}: This is a longer message to help trigger the file rotation when the size limit is reached. Adding more text to make it bigger.`);
        
        // Check files after each message
        const files = fs.readdirSync('./logs').filter(f => f.includes('working-test'));
        console.log(`After message ${i}: Files = [${files.join(', ')}]`);
        
        // Check file sizes
        files.forEach(file => {
            const stats = fs.statSync(`./logs/${file}`);
            console.log(`  ${file}: ${stats.size} bytes`);
        });
    }
    
    // Close logger and check final state
    logger.end();
    
    setTimeout(() => {
        console.log('\n=== Final state ===');
        const files = fs.readdirSync('./logs').filter(f => f.includes('working-test'));
        console.log('Final files:', files);
        
        files.forEach(file => {
            const content = fs.readFileSync(`./logs/${file}`, 'utf8');
            console.log(`\n${file} (${content.length} bytes):`);
            console.log(content.split('\n').slice(0, 3).join('\n') + (content.split('\n').length > 3 ? '\n...' : ''));
        });
    }, 1000);
}

testRotation().catch(console.error);
