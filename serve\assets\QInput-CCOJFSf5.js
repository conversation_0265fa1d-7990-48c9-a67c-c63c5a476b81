import{o as ae,e as ne,p as le,q as ie,r as X,s as re,t as ue,v as oe,n as se}from"./use-key-composition-CoMUTTxZ.js";import{r as G,w as I,n as q,ak as fe,a as T,c as de,g as ce,az as J,o as ge,f as me,h as U,as as ve}from"./index-CzmOWWdj.js";const p={date:"####/##/##",datetime:"####/##/## ##:##",time:"##:##",fulltime:"##:##:##",phone:"(###) ### - ####",card:"#### #### #### ####"},H={"#":{pattern:"[\\d]",negate:"[^\\d]"},S:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]"},N:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]"},A:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]",transform:e=>e.toLocaleUpperCase()},a:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]",transform:e=>e.toLocaleLowerCase()},X:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]",transform:e=>e.toLocaleUpperCase()},x:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]",transform:e=>e.toLocaleLowerCase()}},te=Object.keys(H);te.forEach(e=>{H[e].regex=new RegExp(H[e].pattern)});const he=new RegExp("\\\\([^.*+?^${}()|([\\]])|([.*+?^${}()|[\\]])|(["+te.join("")+"])|(.)","g"),ee=/[.*+?^${}()|[\]\\]/g,h="",ke={mask:String,reverseFillMask:Boolean,fillMask:[Boolean,String],unmaskedValue:Boolean};function Me(e,S,L,C){let d,g,V,E,N,M;const x=G(null),c=G(b());function Y(){return e.autogrow===!0||["textarea","text","search","url","tel","password"].includes(e.type)}I(()=>e.type+e.autogrow,z),I(()=>e.mask,l=>{if(l!==void 0)K(c.value,!0);else{const a=F(c.value);z(),e.modelValue!==a&&S("update:modelValue",a)}}),I(()=>e.fillMask+e.reverseFillMask,()=>{x.value===!0&&K(c.value,!0)}),I(()=>e.unmaskedValue,()=>{x.value===!0&&K(c.value)});function b(){if(z(),x.value===!0){const l=B(F(e.modelValue));return e.fillMask!==!1?$(l):l}return e.modelValue}function j(l){if(l<d.length)return d.slice(-l);let a="",i=d;const n=i.indexOf(h);if(n!==-1){for(let u=l-i.length;u>0;u--)a+=h;i=i.slice(0,n)+a+i.slice(n)}return i}function z(){if(x.value=e.mask!==void 0&&e.mask.length!==0&&Y(),x.value===!1){E=void 0,d="",g="";return}const l=p[e.mask]===void 0?e.mask:p[e.mask],a=typeof e.fillMask=="string"&&e.fillMask.length!==0?e.fillMask.slice(0,1):"_",i=a.replace(ee,"\\$&"),n=[],u=[],r=[];let v=e.reverseFillMask===!0,o="",s="";l.replace(he,(k,t,m,A,R)=>{if(A!==void 0){const w=H[A];r.push(w),s=w.negate,v===!0&&(u.push("(?:"+s+"+)?("+w.pattern+"+)?(?:"+s+"+)?("+w.pattern+"+)?"),v=!1),u.push("(?:"+s+"+)?("+w.pattern+")?")}else if(m!==void 0)o="\\"+(m==="\\"?"":m),r.push(m),n.push("([^"+o+"]+)?"+o+"?");else{const w=t!==void 0?t:R;o=w==="\\"?"\\\\\\\\":w.replace(ee,"\\\\$&"),r.push(w),n.push("([^"+o+"]+)?"+o+"?")}});const Z=new RegExp("^"+n.join("")+"("+(o===""?".":"[^"+o+"]")+"+)?"+(o===""?"":"["+o+"]*")+"$"),P=u.length-1,f=u.map((k,t)=>t===0&&e.reverseFillMask===!0?new RegExp("^"+i+"*"+k):t===P?new RegExp("^"+k+"("+(s===""?".":s)+"+)?"+(e.reverseFillMask===!0?"$":i+"*")):new RegExp("^"+k));V=r,E=k=>{const t=Z.exec(e.reverseFillMask===!0?k:k.slice(0,r.length+1));t!==null&&(k=t.slice(1).join(""));const m=[],A=f.length;for(let R=0,w=k;R<A;R++){const D=f[R].exec(w);if(D===null)break;w=w.slice(D.shift().length),m.push(...D)}return m.length!==0?m.join(""):k},d=r.map(k=>typeof k=="string"?k:h).join(""),g=d.split(h).join(a)}function K(l,a,i){const n=C.value,u=n.selectionEnd,r=n.value.length-u,v=F(l);a===!0&&z();const o=B(v),s=e.fillMask!==!1?$(o):o,Z=c.value!==s;n.value!==s&&(n.value=s),Z===!0&&(c.value=s),document.activeElement===n&&q(()=>{if(s===g){const f=e.reverseFillMask===!0?g.length:0;n.setSelectionRange(f,f,"forward");return}if(i==="insertFromPaste"&&e.reverseFillMask!==!0){const f=n.selectionEnd;let k=u-1;for(let t=N;t<=k&&t<f;t++)d[t]!==h&&k++;y.right(n,k);return}if(["deleteContentBackward","deleteContentForward"].indexOf(i)!==-1){const f=e.reverseFillMask===!0?u===0?s.length>o.length?1:0:Math.max(0,s.length-(s===g?0:Math.min(o.length,r)+1))+1:u;n.setSelectionRange(f,f,"forward");return}if(e.reverseFillMask===!0)if(Z===!0){const f=Math.max(0,s.length-(s===g?0:Math.min(o.length,r+1)));f===1&&u===1?n.setSelectionRange(f,f,"forward"):y.rightReverse(n,f)}else{const f=s.length-r;n.setSelectionRange(f,f,"backward")}else if(Z===!0){const f=Math.max(0,d.indexOf(h),Math.min(o.length,u)-1);y.right(n,f)}else{const f=u-1;y.right(n,f)}});const P=e.unmaskedValue===!0?F(s):s;String(e.modelValue)!==P&&(e.modelValue!==null||P!=="")&&L(P,!0)}function Q(l,a,i){const n=B(F(l.value));a=Math.max(0,d.indexOf(h),Math.min(n.length,a)),N=a,l.setSelectionRange(a,i,"forward")}const y={left(l,a){const i=d.slice(a-1).indexOf(h)===-1;let n=Math.max(0,a-1);for(;n>=0;n--)if(d[n]===h){a=n,i===!0&&a++;break}if(n<0&&d[a]!==void 0&&d[a]!==h)return y.right(l,0);a>=0&&l.setSelectionRange(a,a,"backward")},right(l,a){const i=l.value.length;let n=Math.min(i,a+1);for(;n<=i;n++)if(d[n]===h){a=n;break}else d[n-1]===h&&(a=n);if(n>i&&d[a-1]!==void 0&&d[a-1]!==h)return y.left(l,i);l.setSelectionRange(a,a,"forward")},leftReverse(l,a){const i=j(l.value.length);let n=Math.max(0,a-1);for(;n>=0;n--)if(i[n-1]===h){a=n;break}else if(i[n]===h&&(a=n,n===0))break;if(n<0&&i[a]!==void 0&&i[a]!==h)return y.rightReverse(l,0);a>=0&&l.setSelectionRange(a,a,"backward")},rightReverse(l,a){const i=l.value.length,n=j(i),u=n.slice(0,a+1).indexOf(h)===-1;let r=Math.min(i,a+1);for(;r<=i;r++)if(n[r-1]===h){a=r,a>0&&u===!0&&a--;break}if(r>i&&n[a-1]!==void 0&&n[a-1]!==h)return y.leftReverse(l,i);l.setSelectionRange(a,a,"forward")}};function W(l){S("click",l),M=void 0}function _(l){if(S("keydown",l),fe(l)===!0||l.altKey===!0)return;const a=C.value,i=a.selectionStart,n=a.selectionEnd;if(l.shiftKey||(M=void 0),l.keyCode===37||l.keyCode===39){l.shiftKey&&M===void 0&&(M=a.selectionDirection==="forward"?i:n);const u=y[(l.keyCode===39?"right":"left")+(e.reverseFillMask===!0?"Reverse":"")];if(l.preventDefault(),u(a,M===i?n:i),l.shiftKey){const r=a.selectionStart;a.setSelectionRange(Math.min(M,r),Math.max(M,r),"forward")}}else l.keyCode===8&&e.reverseFillMask!==!0&&i===n?(y.left(a,i),a.setSelectionRange(a.selectionStart,n,"backward")):l.keyCode===46&&e.reverseFillMask===!0&&i===n&&(y.rightReverse(a,n),a.setSelectionRange(i,a.selectionEnd,"forward"))}function B(l){if(l==null||l==="")return"";if(e.reverseFillMask===!0)return O(l);const a=V;let i=0,n="";for(let u=0;u<a.length;u++){const r=l[i],v=a[u];if(typeof v=="string")n+=v,r===v&&i++;else if(r!==void 0&&v.regex.test(r))n+=v.transform!==void 0?v.transform(r):r,i++;else return n}return n}function O(l){const a=V,i=d.indexOf(h);let n=l.length-1,u="";for(let r=a.length-1;r>=0&&n!==-1;r--){const v=a[r];let o=l[n];if(typeof v=="string")u=v+u,o===v&&n--;else if(o!==void 0&&v.regex.test(o))do u=(v.transform!==void 0?v.transform(o):o)+u,n--,o=l[n];while(i===r&&o!==void 0&&v.regex.test(o));else return u}return u}function F(l){return typeof l!="string"||E===void 0?typeof l=="number"?E(""+l):l:E(l)}function $(l){return g.length-l.length<=0?l:e.reverseFillMask===!0&&l.length!==0?g.slice(0,-l.length)+l:l+g.slice(l.length)}return{innerValue:c,hasMask:x,moveCursorForPaste:Q,updateMaskValue:K,onMaskedKeydown:_,onMaskedClick:W}}function we(e,S){function L(){const C=e.modelValue;try{const d="DataTransfer"in window?new DataTransfer:"ClipboardEvent"in window?new ClipboardEvent("").clipboardData:void 0;return Object(C)===C&&("length"in C?Array.from(C):[C]).forEach(g=>{d.items.add(g)}),{files:d.files}}catch{return{files:void 0}}}return T(()=>{if(e.type==="file")return L()})}const Ce=de({name:"QInput",inheritAttrs:!1,props:{...le,...ke,...ne,modelValue:[String,Number,FileList],shadowText:String,type:{type:String,default:"text"},debounce:[String,Number],autogrow:Boolean,inputClass:[Array,String,Object],inputStyle:[Array,String,Object]},emits:[...ae,"paste","change","keydown","click","animationend"],setup(e,{emit:S,attrs:L}){const{proxy:C}=ce(),{$q:d}=C,g={};let V=NaN,E,N,M=null,x;const c=G(null),Y=ie(e),{innerValue:b,hasMask:j,moveCursorForPaste:z,updateMaskValue:K,onMaskedKeydown:Q,onMaskedClick:y}=Me(e,S,o,c),W=we(e),_=T(()=>X(b.value)),B=ue(r),O=re({changeEvent:!0}),F=T(()=>e.type==="textarea"||e.autogrow===!0),$=T(()=>F.value===!0||["text","search","url","tel","password"].includes(e.type)),l=T(()=>{const t={...O.splitAttrs.listeners.value,onInput:r,onPaste:u,onChange:Z,onBlur:P,onFocus:J};return t.onCompositionstart=t.onCompositionupdate=t.onCompositionend=B,j.value===!0&&(t.onKeydown=Q,t.onClick=y),e.autogrow===!0&&(t.onAnimationend=v),t}),a=T(()=>{const t={tabindex:0,"data-autofocus":e.autofocus===!0||void 0,rows:e.type==="textarea"?6:void 0,"aria-label":e.label,name:Y.value,...O.splitAttrs.attributes.value,id:O.targetUid.value,maxlength:e.maxlength,disabled:e.disable===!0,readonly:e.readonly===!0};return F.value===!1&&(t.type=e.type),e.autogrow===!0&&(t.rows=1),t});I(()=>e.type,()=>{c.value&&(c.value.value=e.modelValue)}),I(()=>e.modelValue,t=>{if(j.value===!0){if(N===!0&&(N=!1,String(t)===V))return;K(t)}else b.value!==t&&(b.value=t,e.type==="number"&&g.hasOwnProperty("value")===!0&&(E===!0?E=!1:delete g.value));e.autogrow===!0&&q(s)}),I(()=>e.autogrow,t=>{t===!0?q(s):c.value!==null&&L.rows>0&&(c.value.style.height="auto")}),I(()=>e.dense,()=>{e.autogrow===!0&&q(s)});function i(){se(()=>{const t=document.activeElement;c.value!==null&&c.value!==t&&(t===null||t.id!==O.targetUid.value)&&c.value.focus({preventScroll:!0})})}function n(){c.value?.select()}function u(t){if(j.value===!0&&e.reverseFillMask!==!0){const m=t.target;z(m,m.selectionStart,m.selectionEnd)}S("paste",t)}function r(t){if(!t||!t.target)return;if(e.type==="file"){S("update:modelValue",t.target.files);return}const m=t.target.value;if(t.target.qComposing===!0){g.value=m;return}if(j.value===!0)K(m,!1,t.inputType);else if(o(m),$.value===!0&&t.target===document.activeElement){const{selectionStart:A,selectionEnd:R}=t.target;A!==void 0&&R!==void 0&&q(()=>{t.target===document.activeElement&&m.indexOf(t.target.value)===0&&t.target.setSelectionRange(A,R)})}e.autogrow===!0&&s()}function v(t){S("animationend",t),s()}function o(t,m){x=()=>{M=null,e.type!=="number"&&g.hasOwnProperty("value")===!0&&delete g.value,e.modelValue!==t&&V!==t&&(V=t,m===!0&&(N=!0),S("update:modelValue",t),q(()=>{V===t&&(V=NaN)})),x=void 0},e.type==="number"&&(E=!0,g.value=t),e.debounce!==void 0?(M!==null&&clearTimeout(M),g.value=t,M=setTimeout(x,e.debounce)):x()}function s(){requestAnimationFrame(()=>{const t=c.value;if(t!==null){const m=t.parentNode.style,{scrollTop:A}=t,{overflowY:R,maxHeight:w}=d.platform.is.firefox===!0?{}:window.getComputedStyle(t),D=R!==void 0&&R!=="scroll";D===!0&&(t.style.overflowY="hidden"),m.marginBottom=t.scrollHeight-1+"px",t.style.height="1px",t.style.height=t.scrollHeight+"px",D===!0&&(t.style.overflowY=parseInt(w,10)<t.scrollHeight?"auto":"hidden"),m.marginBottom="",t.scrollTop=A}})}function Z(t){B(t),M!==null&&(clearTimeout(M),M=null),x?.(),S("change",t.target.value)}function P(t){t!==void 0&&J(t),M!==null&&(clearTimeout(M),M=null),x?.(),E=!1,N=!1,delete g.value,e.type!=="file"&&setTimeout(()=>{c.value!==null&&(c.value.value=b.value!==void 0?b.value:"")})}function f(){return g.hasOwnProperty("value")===!0?g.value:b.value!==void 0?b.value:""}ge(()=>{P()}),me(()=>{e.autogrow===!0&&s()}),Object.assign(O,{innerValue:b,fieldClass:T(()=>`q-${F.value===!0?"textarea":"input"}`+(e.autogrow===!0?" q-textarea--autogrow":"")),hasShadow:T(()=>e.type!=="file"&&typeof e.shadowText=="string"&&e.shadowText.length!==0),inputRef:c,emitValue:o,hasValue:_,floatingLabel:T(()=>_.value===!0&&(e.type!=="number"||isNaN(b.value)===!1)||X(e.displayValue)),getControl:()=>U(F.value===!0?"textarea":"input",{ref:c,class:["q-field__native q-placeholder",e.inputClass],style:e.inputStyle,...a.value,...l.value,...e.type!=="file"?{value:f()}:W.value}),getShadowControl:()=>U("div",{class:"q-field__native q-field__shadow absolute-bottom no-pointer-events"+(F.value===!0?"":" text-no-wrap")},[U("span",{class:"invisible"},f()),U("span",e.shadowText)])});const k=oe(O);return Object.assign(C,{focus:i,select:n,getNativeElement:()=>c.value}),ve(C,"nativeEl",()=>c.value),k}});export{Ce as Q};
