import{r as c,w as i}from"./index-CzmOWWdj.js";//!============================================================================================
//! COPYRIGHT © 2024-, <PERSON> and Pravista Inc. - All Rights Reserved
//! NOTICE: All information contained herein is, and remains the property of <PERSON>,
//! Pravista Inc., and their suppliers, if any. The intellectual and technical concepts
//! contained herein are proprietary to <PERSON>, Pravista Inc., and their suppliers
//! and are protected by trade secret or copyright law. Dissemination of this information or
//! reproduction of this material is strictly forbidden unless prior written permission is
//! obtained from Michael <PERSON> and Pravista Inc.
//!============================================================================================
function n(t,o){const r=localStorage.getItem(t);let e;try{e=r?JSON.parse(r):o}catch(s){console.warn(`Failed to parse localStorage for key "${t}". Using default value.`,s),e=o}console.log("++> usePersistObject init: ",t,e);const a=c(e);return i(a,s=>{console.log("<++ usePersistObject watch: ",t,s),localStorage.setItem(t,JSON.stringify(s))}),a}export{n as u};
