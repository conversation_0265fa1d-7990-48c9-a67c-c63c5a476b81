const fs = require('fs');

// Import the logger constructor
const winston = require('winston');
const { format } = winston;
const path = require('path');

// Logger constructor 
function loggerConstructor(options = {}) {
    const {
        size = 100, // Max number of memory entries
        filename = 'logger', // Base filename 
        filesize = 5 * 1024 , // 5kb default file size
        persist = false, // Load from file on startup
        storeOnly = [], // Level to store in file
        logDirectory = './logs',
        verboseStack = true, // Show complete stack trace
        enableConsole = true // Log to console
    } = options;

    const transports = [];

    if (filename) {
        transports.push(new winston.transports.File({
            filename: `${logDirectory}/${filename}.log`,
            maxsize: filesize,
            maxFiles: 2,
            tailable: true, // This ensures proper rotation with .1, .2 naming
            format: format.combine(
                format.timestamp({ format: 'HH:mm:ss' }),
                format.printf((info) => {
                    const logObj = {
                        timestamp: info.timestamp,
                        level: info.level,
                        message: info.message
                    };

                    if (info.data) { logObj.data = info.data; }
                    if (info.stack) { logObj.stack = info.stack; }

                    return JSON.stringify(logObj);
                })
            ),
            level: storeOnly.includes('info') ? 'info' : storeOnly.includes('warn') ? 'warn' : storeOnly.includes('error') ? 'error' : 'info',
        }))
    }

    if (enableConsole) {
        transports.push(new winston.transports.Console({
            level: 'info',
            format: format.combine(
                format.colorize(),
                format.timestamp({ format: 'HH:mm:ss' }),
                format.printf(({ timestamp, level, message, data, stack }) => {
                    let output = `${timestamp} [${level}] ${message}`;
                    if (data) output += ` | Data: ${data}`;
                    if (stack) output += `\n   Error: ${stack}`;
                    return output;
                })
            ),
        }));
    }

    const logger = transports.length > 0 ? winston.createLogger({ transports }) : null;

    return {
        _log(level, msg, data) {
            const logEntry = {
                timestamp: Date.now(),
                level,
                message: typeof msg === 'string' ? msg : msg.message,
            };
            if (data !== undefined) {
                logEntry.data = data;
            }
            if (logger) {
                logger.log(logEntry);
            }
        },
        log(msg, data) {
            this._log('info', msg, data);
        },
        warn(msg, data) {
            this._log('warn', msg, data);
        },
        error(msg, data) {
            this._log('error', msg, data);
        }
    };
}

// Test rotation
console.log('Starting rotation test...');

const testLogger = loggerConstructor({
    size: 100,
    filename: 'test-rotation-debug',
    filesize: 300, // Very small for testing
    persist: false,
    storeOnly: ['info'],
    verboseStack: false,
    enableConsole: true,
});

// Generate enough logs to trigger rotation
for(let i = 0; i < 20; i++) {
    testLogger.log(`Test message ${i} - this is a longer message to trigger rotation faster and see how the files are created`);
}

console.log('Test completed. Check logs directory for files.');

// Wait a bit for async file operations to complete
setTimeout(() => {
    console.log('Files in logs directory:', fs.readdirSync('./logs').filter(f => f.includes('test-rotation-debug')));
}, 1000);
