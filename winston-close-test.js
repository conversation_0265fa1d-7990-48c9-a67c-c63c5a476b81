const winston = require('winston');
const fs = require('fs');

console.log('Testing Winston file transport with proper closing...');

const logger = winston.createLogger({
    transports: [
        new winston.transports.File({
            filename: './logs/close-test.log',
            maxsize: 500,
            maxFiles: 2,
            tailable: true,
            format: winston.format.combine(
                winston.format.timestamp(),
                winston.format.json()
            )
        }),
        new winston.transports.Console()
    ]
});

// Test logging
for(let i = 0; i < 10; i++) {
    logger.info(`Test message ${i} - this is a longer message to trigger rotation faster and see how the files are created`);
}

// Properly close the logger
logger.on('finish', () => {
    console.log('Logger finished writing');
    
    setTimeout(() => {
        console.log('Files created:', fs.readdirSync('./logs').filter(f => f.includes('close-test')));
        
        // Check file contents
        const files = fs.readdirSync('./logs').filter(f => f.includes('close-test'));
        files.forEach(file => {
            const content = fs.readFileSync(`./logs/${file}`, 'utf8');
            console.log(`\n${file} content (${content.length} bytes):`);
            console.log(content.substring(0, 200) + (content.length > 200 ? '...' : ''));
        });
    }, 500);
});

// Close the logger
logger.end();
