/**
 * @license
 * Copyright 2011 <PERSON> (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var _t=function(e,t,a,r,i,n){var s=function(l){return l==="logscale"?!1:r(l)};return te(e,t,a,s,i,n)},te=function(e,t,a,r,i,n){var s=r("pixelsPerLabel"),l=[],o,h,f,u;if(n)for(o=0;o<n.length;o++)l.push({v:n[o]});else{if(r("logscale")){u=Math.floor(a/s);var d=Se(e,ye,1),c=Se(t,ye,-1);d==-1&&(d=0),c==-1&&(c=ye.length-1);var p=null;if(c-d>=u/4){for(var v=c;v>=d;v--){var _=ye[v],y=Math.log(_/e)/Math.log(t/e)*a,m={v:_};p===null?p={tickValue:_,pixel_coord:y}:Math.abs(y-p.pixel_coord)>=s?p={tickValue:_,pixel_coord:y}:m.label="",l.push(m)}l.reverse()}}if(l.length===0){var b=r("labelsKMG2"),w,L;b?(w=[1,2,4,8,16,32,64,128,256],L=16):(w=[1,2,5,10,20,50,100],L=10);var R=Math.ceil(a/s),A=Math.abs(t-e)/R,x=Math.floor(Math.log(A)/Math.log(L)),D=Math.pow(L,x),S,M,I,Z;for(h=0;h<w.length&&(S=D*w[h],M=Math.floor(e/S)*S,I=Math.ceil(t/S)*S,u=Math.abs(I-M)/S,Z=a/u,!(Z>s));h++);for(M>I&&(S*=-1),o=0;o<=u;o++)f=M+o*S,l.push({v:f})}}var j=r("axisLabelFormatter");for(o=0;o<l.length;o++)l[o].label===void 0&&(l[o].label=j.call(i,l[o].v,0,r,i));return l},ue=function(e,t,a,r,i,n){var s=yt(e,t,a,r);return s>=0?nt(e,t,s,r,i):[]},O={MILLISECONDLY:0,TWO_MILLISECONDLY:1,FIVE_MILLISECONDLY:2,TEN_MILLISECONDLY:3,FIFTY_MILLISECONDLY:4,HUNDRED_MILLISECONDLY:5,FIVE_HUNDRED_MILLISECONDLY:6,SECONDLY:7,TWO_SECONDLY:8,FIVE_SECONDLY:9,TEN_SECONDLY:10,THIRTY_SECONDLY:11,MINUTELY:12,TWO_MINUTELY:13,FIVE_MINUTELY:14,TEN_MINUTELY:15,THIRTY_MINUTELY:16,HOURLY:17,TWO_HOURLY:18,SIX_HOURLY:19,DAILY:20,TWO_DAILY:21,WEEKLY:22,MONTHLY:23,QUARTERLY:24,BIANNUAL:25,ANNUAL:26,DECADAL:27,CENTENNIAL:28,NUM_GRANULARITIES:29},T={DATEFIELD_Y:0,DATEFIELD_M:1,DATEFIELD_D:2,DATEFIELD_HH:3,DATEFIELD_MM:4,DATEFIELD_SS:5,DATEFIELD_MS:6,NUM_DATEFIELDS:7},C=[];C[O.MILLISECONDLY]={datefield:T.DATEFIELD_MS,step:1,spacing:1};C[O.TWO_MILLISECONDLY]={datefield:T.DATEFIELD_MS,step:2,spacing:2};C[O.FIVE_MILLISECONDLY]={datefield:T.DATEFIELD_MS,step:5,spacing:5};C[O.TEN_MILLISECONDLY]={datefield:T.DATEFIELD_MS,step:10,spacing:10};C[O.FIFTY_MILLISECONDLY]={datefield:T.DATEFIELD_MS,step:50,spacing:50};C[O.HUNDRED_MILLISECONDLY]={datefield:T.DATEFIELD_MS,step:100,spacing:100};C[O.FIVE_HUNDRED_MILLISECONDLY]={datefield:T.DATEFIELD_MS,step:500,spacing:500};C[O.SECONDLY]={datefield:T.DATEFIELD_SS,step:1,spacing:1e3*1};C[O.TWO_SECONDLY]={datefield:T.DATEFIELD_SS,step:2,spacing:1e3*2};C[O.FIVE_SECONDLY]={datefield:T.DATEFIELD_SS,step:5,spacing:1e3*5};C[O.TEN_SECONDLY]={datefield:T.DATEFIELD_SS,step:10,spacing:1e3*10};C[O.THIRTY_SECONDLY]={datefield:T.DATEFIELD_SS,step:30,spacing:1e3*30};C[O.MINUTELY]={datefield:T.DATEFIELD_MM,step:1,spacing:1e3*60};C[O.TWO_MINUTELY]={datefield:T.DATEFIELD_MM,step:2,spacing:1e3*60*2};C[O.FIVE_MINUTELY]={datefield:T.DATEFIELD_MM,step:5,spacing:1e3*60*5};C[O.TEN_MINUTELY]={datefield:T.DATEFIELD_MM,step:10,spacing:1e3*60*10};C[O.THIRTY_MINUTELY]={datefield:T.DATEFIELD_MM,step:30,spacing:1e3*60*30};C[O.HOURLY]={datefield:T.DATEFIELD_HH,step:1,spacing:1e3*3600};C[O.TWO_HOURLY]={datefield:T.DATEFIELD_HH,step:2,spacing:1e3*3600*2};C[O.SIX_HOURLY]={datefield:T.DATEFIELD_HH,step:6,spacing:1e3*3600*6};C[O.DAILY]={datefield:T.DATEFIELD_D,step:1,spacing:1e3*86400};C[O.TWO_DAILY]={datefield:T.DATEFIELD_D,step:2,spacing:1e3*86400*2};C[O.WEEKLY]={datefield:T.DATEFIELD_D,step:7,spacing:1e3*604800};C[O.MONTHLY]={datefield:T.DATEFIELD_M,step:1,spacing:1e3*7200*365.2425};C[O.QUARTERLY]={datefield:T.DATEFIELD_M,step:3,spacing:1e3*21600*365.2425};C[O.BIANNUAL]={datefield:T.DATEFIELD_M,step:6,spacing:1e3*43200*365.2425};C[O.ANNUAL]={datefield:T.DATEFIELD_Y,step:1,spacing:1e3*86400*365.2425};C[O.DECADAL]={datefield:T.DATEFIELD_Y,step:10,spacing:1e3*864e3*365.2425};C[O.CENTENNIAL]={datefield:T.DATEFIELD_Y,step:100,spacing:1e3*864e4*365.2425};var ye=function(){for(var e=[],t=-39;t<=39;t++)for(var a=Math.pow(10,t),r=1;r<=9;r++){var i=a*r;e.push(i)}return e}(),yt=function(e,t,a,r){for(var i=r("pixelsPerLabel"),n=0;n<O.NUM_GRANULARITIES;n++){var s=mt(e,t,n);if(a/s>=i)return n}return-1},mt=function(e,t,a){var r=C[a].spacing;return Math.round(1*(t-e)/r)},nt=function(e,t,a,r,i){var n=r("axisLabelFormatter"),s=r("labelsUTC"),l=s?Ge:Be,o=C[a].datefield,h=C[a].step,f=C[a].spacing,u=new Date(e),d=[];d[T.DATEFIELD_Y]=l.getFullYear(u),d[T.DATEFIELD_M]=l.getMonth(u),d[T.DATEFIELD_D]=l.getDate(u),d[T.DATEFIELD_HH]=l.getHours(u),d[T.DATEFIELD_MM]=l.getMinutes(u),d[T.DATEFIELD_SS]=l.getSeconds(u),d[T.DATEFIELD_MS]=l.getMilliseconds(u);var c=d[o]%h;a==O.WEEKLY&&(c=l.getDay(u)),d[o]-=c;for(var p=o+1;p<T.NUM_DATEFIELDS;p++)d[p]=p===T.DATEFIELD_D?1:0;var v=[],_=l.makeDate.apply(null,d),y=_.getTime();if(a<=O.HOURLY)for(y<e&&(y+=f,_=new Date(y));y<=t;)v.push({v:y,label:n.call(i,_,a,r,i)}),y+=f,_=new Date(y);else for(y<e&&(d[o]+=h,_=l.makeDate.apply(null,d),y=_.getTime());y<=t;)(a>=O.DAILY||l.getHours(_)%h===0)&&v.push({v:y,label:n.call(i,_,a,r,i)}),d[o]+=h,_=l.makeDate.apply(null,d),y=_.getTime();return v};/**
 * @license
 * Copyright 2011 Dan Vanderkam (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var ie=10,bt=Math.log(ie),F=function(e){return Math.log(e)/bt},be=function(e,t,a){var r=F(e),i=F(t),n=r+a*(i-r),s=Math.pow(ie,n);return s},wt=[7,3],At=[7,2,2,2],we=1,Ae=2,xe=function(e){return e.getContext("2d")},le=function(t,a,r){t.addEventListener(a,r,!1)};function V(e,t,a){e.removeEventListener(t,a,!1)}function $(e){return e=e||window.event,e.stopPropagation&&e.stopPropagation(),e.preventDefault&&e.preventDefault(),e.cancelBubble=!0,e.cancel=!0,e.returnValue=!1,!1}function xt(e,t,a){var r,i,n;if(t===0)r=a,i=a,n=a;else{var s=Math.floor(e*6),l=e*6-s,o=a*(1-t),h=a*(1-t*l),f=a*(1-t*(1-l));switch(s){case 1:r=h,i=a,n=o;break;case 2:r=o,i=a,n=f;break;case 3:r=o,i=h,n=a;break;case 4:r=f,i=o,n=a;break;case 5:r=a,i=o,n=h;break;case 6:case 0:r=a,i=f,n=o;break}}return r=Math.floor(255*r+.5),i=Math.floor(255*i+.5),n=Math.floor(255*n+.5),"rgb("+r+","+i+","+n+")"}function fe(e){var t=e.getBoundingClientRect(),a=window,r=document.documentElement;return{x:t.left+(a.pageXOffset||r.scrollLeft),y:t.top+(a.pageYOffset||r.scrollTop)}}function Ee(e){return!e.pageX||e.pageX<0?0:e.pageX}function Oe(e){return!e.pageY||e.pageY<0?0:e.pageY}function Te(e,t){return Ee(e)-t.px}function Ce(e,t){return Oe(e)-t.py}function St(e){return!!e&&!isNaN(e)}function he(e,t){return!(!e||e.yval===null||e.x===null||e.x===void 0||e.y===null||e.y===void 0||isNaN(e.x)||!t&&isNaN(e.y))}function st(e,t){var a=Math.min(Math.max(1,t||2),21);return Math.abs(e)<.001&&e!==0?e.toExponential(a-1):e.toPrecision(a)}function ee(e){return e<10?"0"+e:""+e}var Be={getFullYear:e=>e.getFullYear(),getMonth:e=>e.getMonth(),getDate:e=>e.getDate(),getHours:e=>e.getHours(),getMinutes:e=>e.getMinutes(),getSeconds:e=>e.getSeconds(),getMilliseconds:e=>e.getMilliseconds(),getDay:e=>e.getDay(),makeDate:function(e,t,a,r,i,n,s){return new Date(e,t,a,r,i,n,s)}},Ge={getFullYear:e=>e.getUTCFullYear(),getMonth:e=>e.getUTCMonth(),getDate:e=>e.getUTCDate(),getHours:e=>e.getUTCHours(),getMinutes:e=>e.getUTCMinutes(),getSeconds:e=>e.getUTCSeconds(),getMilliseconds:e=>e.getUTCMilliseconds(),getDay:e=>e.getUTCDay(),makeDate:function(e,t,a,r,i,n,s){return new Date(Date.UTC(e,t,a,r,i,n,s))}};function Ie(e,t,a,r){var i=ee(e)+":"+ee(t);if(a&&(i+=":"+ee(a),r)){var n=""+r;i+="."+("000"+n).substring(n.length)}return i}function ot(e,t){var a=t?Ge:Be,r=new Date(e),i=a.getFullYear(r),n=a.getMonth(r),s=a.getDate(r),l=a.getHours(r),o=a.getMinutes(r),h=a.getSeconds(r),f=a.getMilliseconds(r),u=""+i,d=ee(n+1),c=ee(s),p=l*3600+o*60+h+.001*f,v=u+"/"+d+"/"+c;return p&&(v+=" "+Ie(l,o,h,f)),v}function ke(e,t){var a=Math.pow(10,t);return Math.round(e*a)/a}function Se(e,t,a,r,i){if((r==null||i===null||i===void 0)&&(r=0,i=t.length-1),r>i)return-1;a==null&&(a=0);var n=function(h){return h>=0&&h<t.length},s=parseInt((r+i)/2,10),l=t[s],o;return l==e?s:l>e?a>0&&(o=s-1,n(o)&&t[o]<e)?s:Se(e,t,a,r,s-1):l<e?a<0&&(o=s+1,n(o)&&t[o]>e)?s:Se(e,t,a,s+1,i):-1}function lt(e){var t,a;if((e.search("-")==-1||e.search("T")!=-1||e.search("Z")!=-1)&&(a=He(e),a&&!isNaN(a)))return a;if(e.search("-")!=-1){for(t=e.replace("-","/","g");t.search("-")!=-1;)t=t.replace("-","/");a=He(t)}else a=He(e);return(!a||isNaN(a))&&console.error("Couldn't parse "+e+" as a date"),a}function He(e){return new Date(e).getTime()}function U(e,t){if(typeof t<"u"&&t!==null)for(var a in t)t.hasOwnProperty(a)&&(e[a]=t[a]);return e}var Lt=typeof Node<"u"&&Node!==null&&typeof Node=="object"?function(t){return t instanceof Node}:function(t){return typeof t=="object"&&typeof t.nodeType=="number"&&typeof t.nodeName=="string"};function Ve(e,t){if(typeof t<"u"&&t!==null){for(var a in t)if(t.hasOwnProperty(a)){const r=t[a];r===null?e[a]=null:de(r)?e[a]=r.slice():Lt(r)?e[a]=r:typeof r=="object"?((typeof e[a]!="object"||e[a]===null)&&(e[a]={}),Ve(e[a],r)):e[a]=r}}return e}function Dt(e){if(e===null)return"null";const t=typeof e;return(t==="object"||t==="function"&&typeof e.item=="function")&&typeof e.length=="number"&&e.nodeType!==3&&e.nodeType!==4?"array":t}function de(e){const t=typeof e;return e!==null&&(t==="object"||t==="function"&&typeof e.item=="function")&&typeof e.length=="number"&&e.nodeType!==3&&e.nodeType!==4}function ht(e){return e!==null&&typeof e=="object"&&typeof e.getTime=="function"}function ut(e){for(var t=[],a=0;a<e.length;a++)de(e[a])?t.push(ut(e[a])):t.push(e[a]);return t}function Le(){return document.createElement("canvas")}function Xe(e){try{var t=window.devicePixelRatio,a=e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1;return t!==void 0?t/a:1}catch{return 1}}function ft(e,t,a,r){t=t||0,a=a||e.length,this.hasNext=!0,this.peek=null,this.start_=t,this.array_=e,this.predicate_=r,this.end_=Math.min(e.length,t+a),this.nextIdx_=t-1,this.next()}ft.prototype.next=function(){if(!this.hasNext)return null;for(var e=this.peek,t=this.nextIdx_+1,a=!1;t<this.end_;){if(!this.predicate_||this.predicate_(this.array_,t)){this.peek=this.array_[t],a=!0;break}t++}return this.nextIdx_=t,a||(this.hasNext=!1,this.peek=null),e};function Ue(e,t,a,r){return new ft(e,t,a,r)}var Et=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function dt(e,t,a,r){var i=0,n,s=new Date().getTime();if(e(i),t==1){r();return}var l=t-1;(function o(){i>=t||Et.call(window,function(){var h=new Date().getTime(),f=h-s;n=i,i=Math.floor(f/a);var u=i-n,d=i+u>l;d||i>=l?(e(l),r()):(u!==0&&e(i),o())})})()}var qe={annotationClickHandler:!0,annotationDblClickHandler:!0,annotationMouseOutHandler:!0,annotationMouseOverHandler:!0,axisLineColor:!0,axisLineWidth:!0,clickCallback:!0,drawCallback:!0,drawHighlightPointCallback:!0,drawPoints:!0,drawPointCallback:!0,drawGrid:!0,fillAlpha:!0,gridLineColor:!0,gridLineWidth:!0,hideOverlayOnMouseOut:!0,highlightCallback:!0,highlightCircleSize:!0,interactionModel:!0,labelsDiv:!0,labelsKMB:!0,labelsKMG2:!0,labelsSeparateLines:!0,labelsShowZeroValues:!0,legend:!0,panEdgeFraction:!0,pixelsPerYLabel:!0,pointClickCallback:!0,pointSize:!0,rangeSelectorPlotFillColor:!0,rangeSelectorPlotFillGradientColor:!0,rangeSelectorPlotStrokeColor:!0,rangeSelectorBackgroundStrokeColor:!0,rangeSelectorBackgroundLineWidth:!0,rangeSelectorPlotLineWidth:!0,rangeSelectorForegroundStrokeColor:!0,rangeSelectorForegroundLineWidth:!0,rangeSelectorAlpha:!0,showLabelsOnHighlight:!0,showRoller:!0,strokeWidth:!0,underlayCallback:!0,unhighlightCallback:!0,zoomCallback:!0};function Ot(e,t){var a={};if(e)for(var r=1;r<e.length;r++)a[e[r]]=!0;var i=function(o){for(var h in o)if(o.hasOwnProperty(h)&&!qe[h])return!0;return!1};for(var n in t)if(t.hasOwnProperty(n)){if(n=="highlightSeriesOpts"||a[n]&&!t.series){if(i(t[n]))return!0}else if(n=="series"||n=="axes"){var s=t[n];for(var l in s)if(s.hasOwnProperty(l)&&i(s[l]))return!0}else if(!qe[n])return!0}return!1}var ze={DEFAULT:function(e,t,a,r,i,n,s){a.beginPath(),a.fillStyle=n,a.arc(r,i,s,0,2*Math.PI,!1),a.fill()}};function ct(e){for(var t=0;t<e.length;t++){var a=e.charAt(t);if(a==="\r")return t+1<e.length&&e.charAt(t+1)===`
`?`\r
`:a;if(a===`
`)return t+1<e.length&&e.charAt(t+1)==="\r"?`
\r`:a}return null}function Qe(e,t){if(t===null||e===null)return!1;for(var a=e;a&&a!==t;)a=a.parentNode;return a===t}function Je(e,t){return t<0?1/Math.pow(e,-t):Math.pow(e,t)}var Tt=/^#([0-9A-Fa-f]{2})([0-9A-Fa-f]{2})([0-9A-Fa-f]{2})([0-9A-Fa-f]{2})?$/,Ct=/^rgba?\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})(?:,\s*([01](?:\.\d+)?))?\)$/;function je(e){var t,a,r,i,n=null;if(t=Tt.exec(e))a=parseInt(t[1],16),r=parseInt(t[2],16),i=parseInt(t[3],16),t[4]&&(n=parseInt(t[4],16));else if(t=Ct.exec(e))a=parseInt(t[1],10),r=parseInt(t[2],10),i=parseInt(t[3],10),t[4]&&(n=parseFloat(t[4]));else return null;return n!==null?{r:a,g:r,b:i,a:n}:{r:a,g:r,b:i}}function Me(e){var t=je(e);if(t)return t;var a=document.createElement("div");a.style.backgroundColor=e,a.style.visibility="hidden",document.body.appendChild(a);var r=window.getComputedStyle(a,null).backgroundColor;return document.body.removeChild(a),je(r)}function Mt(e){try{var t=e||document.createElement("canvas");t.getContext("2d")}catch{return!1}return!0}function q(e,t,a){var r=parseFloat(e);if(!isNaN(r))return r;if(/^ *$/.test(e))return null;if(/^ *nan *$/i.test(e))return NaN;var i="Unable to parse '"+e+"' as a number";return a!==void 0&&t!==void 0&&(i+=" on line "+(1+(t||0))+" ('"+a+"') of CSV."),console.error(i),null}var Nt=["k","M","G","T","P","E","Z","Y"],vt=["m","µ","n","p","f","a","z","y"],Pt=["Ki","Mi","Gi","Ti","Pi","Ei","Zi","Yi"],Rt=["p-10","p-20","p-30","p-40","p-50","p-60","p-70","p-80"],Ft=["K","M","G","T","P","E","Z","Y"],kt=vt;function Ze(e,t){var a=t("sigFigs");if(a!==null)return st(e,a);if(e===0)return"0";var r=t("digitsAfterDecimal"),i=t("maxNumberWidth"),n=t("labelsKMB"),s=t("labelsKMG2"),l,o=Math.abs(e);if(n||s){var h,f=[],u=[];n&&(h=1e3,f=Nt,u=vt),s&&(h=1024,f=Pt,u=Rt,n&&(f=Ft,u=kt));var d,c;if(o>=h){for(c=f.length;c>0;)if(d=Je(h,c),--c,o>=d)return o/d>=Math.pow(10,i)?l=e.toExponential(r):l=ke(e/d,r)+f[c],l}else if(o<1){for(c=0;c<u.length&&(++c,d=Je(h,c),!(o*d>=1)););return o*d<Math.pow(10,-r)?l=e.toExponential(r):l=ke(e*d,r)+u[c-1],l}}return o>=Math.pow(10,i)||o<Math.pow(10,-r)?l=e.toExponential(r):l=""+ke(e,r),l}function We(e,t,a){return Ze.call(this,e,a)}var et=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function ce(e,t,a){var r=a("labelsUTC"),i=r?Ge:Be,n=i.getFullYear(e),s=i.getMonth(e),l=i.getDate(e),o=i.getHours(e),h=i.getMinutes(e),f=i.getSeconds(e),u=i.getMilliseconds(e);if(t>=O.DECADAL)return""+n;if(t>=O.MONTHLY)return et[s]+"&#160;"+n;var d=o*3600+h*60+f+.001*u;if(d===0||t>=O.DAILY)return ee(l)+"&#160;"+et[s];if(t<O.SECONDLY){var c=""+u;return ee(f)+"."+("000"+c).substring(c.length)}else return t>O.MINUTELY?Ie(o,h,f,0):Ie(o,h,f,u)}function Ne(e,t){return ot(e,t("labelsUTC"))}var me=[],tt=!1;function oe(e){return typeof e=="function"&&e(),!0}function Ht(e){if(typeof document<"u"){const t=function(){if(!tt){tt=!0,e.onDOMready=oe,document.removeEventListener("DOMContentLoaded",t,!1),window.removeEventListener("load",t,!1);for(let r=0;r<me.length;++r)me[r]();me=null}};e.onDOMready=function(r){if(document.readyState==="complete")return e.onDOMready=oe,oe(r);const i=function(s){return typeof s=="function"&&me.push(s),!1};return e.onDOMready=i,document.addEventListener("DOMContentLoaded",t,!1),window.addEventListener("load",t,!1),document.readyState==="complete"?(t(),e.onDOMready=oe,oe(r)):i(r)}}}/**
 * @license
 * Copyright 2011 Dan Vanderkam (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var H=function(e){this.dygraph_=e,this.points=[],this.setNames=[],this.annotations=[],this.yAxes_=null,this.xTicks_=null,this.yTicks_=null};H.prototype.addDataset=function(e,t){this.points.push(t),this.setNames.push(e)};H.prototype.getPlotArea=function(){return this.area_};H.prototype.computePlotArea=function(){var e={x:0,y:0};e.w=this.dygraph_.width_-e.x-this.dygraph_.getOption("rightGap"),e.h=this.dygraph_.height_;var t={chart_div:this.dygraph_.graphDiv,reserveSpaceLeft:function(a){var r={x:e.x,y:e.y,w:a,h:e.h};return e.x+=a,e.w-=a,r},reserveSpaceRight:function(a){var r={x:e.x+e.w-a,y:e.y,w:a,h:e.h};return e.w-=a,r},reserveSpaceTop:function(a){var r={x:e.x,y:e.y,w:e.w,h:a};return e.y+=a,e.h-=a,r},reserveSpaceBottom:function(a){var r={x:e.x,y:e.y+e.h-a,w:e.w,h:a};return e.h-=a,r},chartRect:function(){return{x:e.x,y:e.y,w:e.w,h:e.h}}};this.dygraph_.cascadeEvents_("layout",t),this.area_=e};H.prototype.setAnnotations=function(e){this.annotations=[];for(var t=this.dygraph_.getOption("xValueParser")||function(i){return i},a=0;a<e.length;a++){var r={};if(!e[a].xval&&e[a].x===void 0){console.error("Annotations must have an 'x' property");return}if(e[a].icon&&!(e[a].hasOwnProperty("width")&&e[a].hasOwnProperty("height"))){console.error("Must set width and height when setting annotation.icon property");return}U(r,e[a]),r.xval||(r.xval=t(r.x)),this.annotations.push(r)}};H.prototype.setXTicks=function(e){this.xTicks_=e};H.prototype.setYAxes=function(e){this.yAxes_=e};H.prototype.evaluate=function(){this._xAxis={},this._evaluateLimits(),this._evaluateLineCharts(),this._evaluateLineTicks(),this._evaluateAnnotations()};H.prototype._evaluateLimits=function(){var e=this.dygraph_.xAxisRange();this._xAxis.minval=e[0],this._xAxis.maxval=e[1];var t=e[1]-e[0];this._xAxis.scale=t!==0?1/t:1,this.dygraph_.getOptionForAxis("logscale","x")&&(this._xAxis.xlogrange=F(this._xAxis.maxval)-F(this._xAxis.minval),this._xAxis.xlogscale=this._xAxis.xlogrange!==0?1/this._xAxis.xlogrange:1);for(var a=0;a<this.yAxes_.length;a++){var r=this.yAxes_[a];r.minyval=r.computedValueRange[0],r.maxyval=r.computedValueRange[1],r.yrange=r.maxyval-r.minyval,r.yscale=r.yrange!==0?1/r.yrange:1,(this.dygraph_.getOption("logscale")||r.logscale)&&(r.ylogrange=F(r.maxyval)-F(r.minyval),r.ylogscale=r.ylogrange!==0?1/r.ylogrange:1,(!isFinite(r.ylogrange)||isNaN(r.ylogrange))&&console.error("axis "+a+" of graph at "+r.g+" can't be displayed in log scale for range ["+r.minyval+" - "+r.maxyval+"]"))}};H.calcXNormal_=function(e,t,a){return a?(F(e)-F(t.minval))*t.xlogscale:(e-t.minval)*t.scale};H.calcYNormal_=function(e,t,a){if(a){var r=1-(F(t)-F(e.minyval))*e.ylogscale;return isFinite(r)?r:NaN}else return 1-(t-e.minyval)*e.yscale};H.prototype._evaluateLineCharts=function(){for(var e=this.dygraph_.getOption("stackedGraph"),t=this.dygraph_.getOptionForAxis("logscale","x"),a=0;a<this.points.length;a++){for(var r=this.points[a],i=this.setNames[a],n=this.dygraph_.getOption("connectSeparatedPoints",i),s=this.dygraph_.axisPropertiesForSeries(i),l=this.dygraph_.attributes_.getForSeries("logscale",i),o=0;o<r.length;o++){var h=r[o];h.x=H.calcXNormal_(h.xval,this._xAxis,t);var f=h.yval;e&&(h.y_stacked=H.calcYNormal_(s,h.yval_stacked,l),f!==null&&!isNaN(f)&&(f=h.yval_stacked)),f===null&&(f=NaN,n||(h.yval=NaN)),h.y=H.calcYNormal_(s,f,l)}this.dygraph_.dataHandler_.onLineEvaluated(r,s,l)}};H.prototype._evaluateLineTicks=function(){var e,t,a,r,i,n;for(this.xticks=[],e=0;e<this.xTicks_.length;e++)t=this.xTicks_[e],a=t.label,n=!("label_v"in t),i=n?t.v:t.label_v,r=this.dygraph_.toPercentXCoord(i),r>=0&&r<1&&this.xticks.push({pos:r,label:a,has_tick:n});for(this.yticks=[],e=0;e<this.yAxes_.length;e++)for(var s=this.yAxes_[e],l=0;l<s.ticks.length;l++)t=s.ticks[l],a=t.label,n=!("label_v"in t),i=n?t.v:t.label_v,r=this.dygraph_.toPercentYCoord(i,e),r>0&&r<=1&&this.yticks.push({axis:e,pos:r,label:a,has_tick:n})};H.prototype._evaluateAnnotations=function(){var e,t={};for(e=0;e<this.annotations.length;e++){var a=this.annotations[e];t[a.xval+","+a.series]=a}if(this.annotated_points=[],!(!this.annotations||!this.annotations.length))for(var r=0;r<this.points.length;r++){var i=this.points[r];for(e=0;e<i.length;e++){var n=i[e],s=n.xval+","+n.name;s in t&&(n.annotation=t[s],this.annotated_points.push(n),delete t[s])}}};H.prototype.removeAllDatasets=function(){delete this.points,delete this.setNames,delete this.setPointsLengths,delete this.setPointsOffsets,this.points=[],this.setNames=[],this.setPointsLengths=[],this.setPointsOffsets=[]};/**
 * @license
 * Copyright 2006 Dan Vanderkam (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var N=function(e,t,a,r){if(this.dygraph_=e,this.layout=r,this.element=t,this.elementContext=a,this.height=e.height_,this.width=e.width_,!Mt(this.element))throw"Canvas is not supported.";this.area=r.getPlotArea();var i=this.dygraph_.canvas_ctx_;i.beginPath(),i.rect(this.area.x,this.area.y,this.area.w,this.area.h),i.clip(),i=this.dygraph_.hidden_ctx_,i.beginPath(),i.rect(this.area.x,this.area.y,this.area.w,this.area.h),i.clip()};N.prototype.clear=function(){this.elementContext.clearRect(0,0,this.width,this.height)};N.prototype.render=function(){this._updatePoints(),this._renderLineChart()};N._getIteratorPredicate=function(e){return e?N._predicateThatSkipsEmptyPoints:null};N._predicateThatSkipsEmptyPoints=function(e,t){return e[t].yval!==null};N._drawStyledLine=function(e,t,a,r,i,n,s){var l=e.dygraph,o=l.getBooleanOption("stepPlot",e.setName);de(r)||(r=null);var h=l.getBooleanOption("drawGapEdgePoints",e.setName),f=e.points,u=e.setName,d=Ue(f,0,f.length,N._getIteratorPredicate(l.getBooleanOption("connectSeparatedPoints",u))),c=r&&r.length>=2,p=e.drawingContext;p.save(),c&&p.setLineDash&&p.setLineDash(r);var v=N._drawSeries(e,d,a,s,i,h,o,t);N._drawPointsOnLine(e,v,n,t,s),c&&p.setLineDash&&p.setLineDash([]),p.restore()};N._drawSeries=function(e,t,a,r,i,n,s,l){var o=null,h=null,f=null,u,d,c=[],p=!0,v=e.drawingContext;v.beginPath(),v.strokeStyle=l,v.lineWidth=a;for(var _=t.array_,y=t.end_,m=t.predicate_,b=t.start_;b<y;b++){if(d=_[b],m){for(;b<y&&!m(_,b);)b++;if(b==y)break;d=_[b]}if(d.canvasy===null||d.canvasy!=d.canvasy)s&&o!==null&&(v.moveTo(o,h),v.lineTo(d.canvasx,h)),o=h=null;else{if(u=!1,n||o===null){t.nextIdx_=b,t.next(),f=t.hasNext?t.peek.canvasy:null;var w=f===null||f!=f;u=o===null&&w,n&&(!p&&o===null||t.hasNext&&w)&&(u=!0)}o!==null?a&&(s&&(v.moveTo(o,h),v.lineTo(d.canvasx,h)),v.lineTo(d.canvasx,d.canvasy)):v.moveTo(d.canvasx,d.canvasy),(i||u)&&c.push([d.canvasx,d.canvasy,d.idx]),o=d.canvasx,h=d.canvasy}p=!1}return v.stroke(),c};N._drawPointsOnLine=function(e,t,a,r,i){for(var n=e.drawingContext,s=0;s<t.length;s++){var l=t[s];n.save(),a.call(e.dygraph,e.dygraph,e.setName,n,l[0],l[1],r,i,l[2]),n.restore()}};N.prototype._updatePoints=function(){for(var e=this.layout.points,t=e.length;t--;)for(var a=e[t],r=a.length;r--;){var i=a[r];i.canvasx=this.area.w*i.x+this.area.x,i.canvasy=this.area.h*i.y+this.area.y}};N.prototype._renderLineChart=function(e,t){var a=t||this.elementContext,r,i=this.layout.points,n=this.layout.setNames,s;this.colors=this.dygraph_.colorsMap_;var l=this.dygraph_.getOption("plotter"),o=l;de(o)||(o=[o]);var h={};for(r=0;r<n.length;r++){s=n[r];var f=this.dygraph_.getOption("plotter",s);f!=l&&(h[s]=f)}for(r=0;r<o.length;r++)for(var u=o[r],d=r==o.length-1,c=0;c<i.length;c++)if(s=n[c],!(e&&s!=e)){var p=i[c],v=u;if(s in h)if(d)v=h[s];else continue;var _=this.colors[s],y=this.dygraph_.getOption("strokeWidth",s);a.save(),a.strokeStyle=_,a.lineWidth=y,v({points:p,setName:s,drawingContext:a,color:_,strokeWidth:y,dygraph:this.dygraph_,axis:this.dygraph_.axisPropertiesForSeries(s),plotArea:this.area,seriesIndex:c,seriesCount:i.length,singleSeriesName:e,allSeriesPoints:i}),a.restore()}};N._Plotters={linePlotter:function(e){N._linePlotter(e)},fillPlotter:function(e){N._fillPlotter(e)},errorPlotter:function(e){N._errorPlotter(e)}};N._linePlotter=function(e){var t=e.dygraph,a=e.setName,r=e.strokeWidth,i=t.getNumericOption("strokeBorderWidth",a),n=t.getOption("drawPointCallback",a)||ze.DEFAULT,s=t.getOption("strokePattern",a),l=t.getBooleanOption("drawPoints",a),o=t.getNumericOption("pointSize",a);i&&r&&N._drawStyledLine(e,t.getOption("strokeBorderColor",a),r+2*i,s,l,n,o),N._drawStyledLine(e,e.color,r,s,l,n,o)};N._errorPlotter=function(e){var t=e.dygraph,a=e.setName,r=t.getBooleanOption("errorBars")||t.getBooleanOption("customBars");if(r){var i=t.getBooleanOption("fillGraph",a);i&&console.warn("Can't use fillGraph option with customBars or errorBars option");var n=e.drawingContext,s=e.color,l=t.getNumericOption("fillAlpha",a),o=t.getBooleanOption("stepPlot",a),h=e.points,f=Ue(h,0,h.length,N._getIteratorPredicate(t.getBooleanOption("connectSeparatedPoints",a))),u,d=NaN,c=NaN,p=[-1,-1],v=Me(s),_="rgba("+v.r+","+v.g+","+v.b+","+l+")";n.fillStyle=_,n.beginPath();for(var y=function(b){return b==null||isNaN(b)};f.hasNext;){var m=f.next();if(!o&&y(m.y)||o&&!isNaN(c)&&y(c)){d=NaN;continue}u=[m.y_bottom,m.y_top],o&&(c=m.y),isNaN(u[0])&&(u[0]=m.y),isNaN(u[1])&&(u[1]=m.y),u[0]=e.plotArea.h*u[0]+e.plotArea.y,u[1]=e.plotArea.h*u[1]+e.plotArea.y,isNaN(d)||(o?(n.moveTo(d,p[0]),n.lineTo(m.canvasx,p[0]),n.lineTo(m.canvasx,p[1])):(n.moveTo(d,p[0]),n.lineTo(m.canvasx,u[0]),n.lineTo(m.canvasx,u[1])),n.lineTo(d,p[1]),n.closePath()),p=u,d=m.canvasx}n.fill()}};N._fastCanvasProxy=function(e){var t=[],a=null,r=null,i=1,n=2,s=0,l=function(f){if(!(t.length<=1)){for(var u=t.length-1;u>0;u--){var d=t[u];if(d[0]==n){var c=t[u-1];c[1]==d[1]&&c[2]==d[2]&&t.splice(u,1)}}for(var u=0;u<t.length-1;){var d=t[u];d[0]==n&&t[u+1][0]==n?t.splice(u,1):u++}if(t.length>2&&!f){var p=0;t[0][0]==n&&p++;for(var v=null,_=null,u=p;u<t.length;u++){var d=t[u];if(d[0]==i)if(v===null&&_===null)v=u,_=u;else{var y=d[2];y<t[v][2]?v=u:y>t[_][2]&&(_=u)}}var m=t[v],b=t[_];t.splice(p,t.length-p),v<_?(t.push(m),t.push(b)):(v>_&&t.push(b),t.push(m))}}},o=function(f){l(f);for(var u=0,d=t.length;u<d;u++){var c=t[u];c[0]==i?e.lineTo(c[1],c[2]):c[0]==n&&e.moveTo(c[1],c[2])}t.length&&(r=t[t.length-1][1]),s+=t.length,t=[]},h=function(f,u,d){var c=Math.round(u);if(a===null||c!=a){var p=a-r>1,v=c-a>1,_=p||v;o(_),a=c}t.push([f,u,d])};return{moveTo:function(f,u){h(n,f,u)},lineTo:function(f,u){h(i,f,u)},stroke:function(){o(!0),e.stroke()},fill:function(){o(!0),e.fill()},beginPath:function(){o(!0),e.beginPath()},closePath:function(){o(!0),e.closePath()},_count:function(){return s}}};N._fillPlotter=function(e){if(!e.singleSeriesName&&e.seriesIndex===0){for(var t=e.dygraph,a=t.getLabels().slice(1),r=a.length;r>=0;r--)t.visibility()[r]||a.splice(r,1);var i=function(){for(var G=0;G<a.length;G++)if(t.getBooleanOption("fillGraph",a[G]))return!0;return!1}();if(i)for(var n=e.plotArea,s=e.allSeriesPoints,l=s.length,o=t.getBooleanOption("stackedGraph"),h=t.getColors(),f={},u,d,c=function(G,pt,gt,Ke){if(G.lineTo(pt,gt),o)for(var Fe=Ke.length-1;Fe>=0;Fe--){var $e=Ke[Fe];G.lineTo($e[0],$e[1])}},p=l-1;p>=0;p--){var v=e.drawingContext,_=a[p];if(t.getBooleanOption("fillGraph",_)){var y=t.getNumericOption("fillAlpha",_),m=t.getBooleanOption("stepPlot",_),b=h[p],w=t.axisPropertiesForSeries(_),L=1+w.minyval*w.yscale;L<0?L=0:L>1&&(L=1),L=n.h*L+n.y;var R=s[p],A=Ue(R,0,R.length,N._getIteratorPredicate(t.getBooleanOption("connectSeparatedPoints",_))),x=NaN,D=[-1,-1],S,M=Me(b),I="rgba("+M.r+","+M.g+","+M.b+","+y+")";v.fillStyle=I,v.beginPath();var Z,j=!0;(R.length>2*t.width_||g.FORCE_FAST_PROXY)&&(v=N._fastCanvasProxy(v));for(var B=[],P;A.hasNext;){if(P=A.next(),!St(P.y)&&!m){c(v,x,D[1],B),B=[],x=NaN,P.y_stacked!==null&&!isNaN(P.y_stacked)&&(f[P.canvasx]=n.h*P.y_stacked+n.y);continue}if(o){if(!j&&Z==P.xval)continue;j=!1,Z=P.xval,u=f[P.canvasx];var re;u===void 0?re=L:d?re=u[0]:re=u,S=[P.canvasy,re],m?D[0]===-1?f[P.canvasx]=[P.canvasy,L]:f[P.canvasx]=[P.canvasy,D[0]]:f[P.canvasx]=P.canvasy}else isNaN(P.canvasy)&&m?S=[n.y+n.h,L]:S=[P.canvasy,L];isNaN(x)?(v.moveTo(P.canvasx,S[1]),v.lineTo(P.canvasx,S[0])):(m&&v.lineTo(P.canvasx,D[0]),v.lineTo(P.canvasx,S[0]),o&&(B.push([x,D[1]]),d&&u?B.push([P.canvasx,u[1]]):B.push([P.canvasx,S[1]]))),D=S,x=P.canvasx}d=m,S&&P&&(c(v,P.canvasx,S[1],B),B=[]),v.fill()}}}};/**
 * @license
 * Copyright 2011 Robert Konigsberg (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var It=100,E={};E.maybeTreatMouseOpAsClick=function(e,t,a){a.dragEndX=Te(e,a),a.dragEndY=Ce(e,a);var r=Math.abs(a.dragEndX-a.dragStartX),i=Math.abs(a.dragEndY-a.dragStartY);r<2&&i<2&&t.lastx_!==void 0&&t.lastx_!==null&&E.treatMouseOpAsClick(t,e,a),a.regionWidth=r,a.regionHeight=i};E.startPan=function(e,t,a){var r,i;a.isPanning=!0;var n=t.xAxisRange();if(t.getOptionForAxis("logscale","x")?(a.initialLeftmostDate=F(n[0]),a.dateRange=F(n[1])-F(n[0])):(a.initialLeftmostDate=n[0],a.dateRange=n[1]-n[0]),a.xUnitsPerPixel=a.dateRange/(t.plotter_.area.w-1),t.getNumericOption("panEdgeFraction")){var s=t.width_*t.getNumericOption("panEdgeFraction"),l=t.xAxisExtremes(),o=t.toDomXCoord(l[0])-s,h=t.toDomXCoord(l[1])+s,f=t.toDataXCoord(o),u=t.toDataXCoord(h);a.boundedDates=[f,u];var d=[],c=t.height_*t.getNumericOption("panEdgeFraction");for(r=0;r<t.axes_.length;r++){i=t.axes_[r];var p=i.extremeRange,v=t.toDomYCoord(p[0],r)+c,_=t.toDomYCoord(p[1],r)-c,y=t.toDataYCoord(v,r),m=t.toDataYCoord(_,r);d[r]=[y,m]}a.boundedValues=d}else a.boundedDates=null,a.boundedValues=null;for(a.is2DPan=!1,a.axes=[],r=0;r<t.axes_.length;r++){i=t.axes_[r];var b={},w=t.yAxisRange(r),L=t.attributes_.getForAxis("logscale",r);L?(b.initialTopValue=F(w[1]),b.dragValueRange=F(w[1])-F(w[0])):(b.initialTopValue=w[1],b.dragValueRange=w[1]-w[0]),b.unitsPerPixel=b.dragValueRange/(t.plotter_.area.h-1),a.axes.push(b),i.valueRange&&(a.is2DPan=!0)}};E.movePan=function(e,t,a){a.dragEndX=Te(e,a),a.dragEndY=Ce(e,a);var r=a.initialLeftmostDate-(a.dragEndX-a.dragStartX)*a.xUnitsPerPixel;a.boundedDates&&(r=Math.max(r,a.boundedDates[0]));var i=r+a.dateRange;if(a.boundedDates&&i>a.boundedDates[1]&&(r=r-(i-a.boundedDates[1]),i=r+a.dateRange),t.getOptionForAxis("logscale","x")?t.dateWindow_=[Math.pow(ie,r),Math.pow(ie,i)]:t.dateWindow_=[r,i],a.is2DPan)for(var n=a.dragEndY-a.dragStartY,s=0;s<t.axes_.length;s++){var l=t.axes_[s],o=a.axes[s],h=n*o.unitsPerPixel,f=a.boundedValues?a.boundedValues[s]:null,u=o.initialTopValue+h;f&&(u=Math.min(u,f[1]));var d=u-o.dragValueRange;f&&d<f[0]&&(u=u-(d-f[0]),d=u-o.dragValueRange),t.attributes_.getForAxis("logscale",s)?l.valueRange=[Math.pow(ie,d),Math.pow(ie,u)]:l.valueRange=[d,u]}t.drawGraph_(!1)};E.endPan=E.maybeTreatMouseOpAsClick;E.startZoom=function(e,t,a){a.isZooming=!0,a.zoomMoved=!1};E.moveZoom=function(e,t,a){a.zoomMoved=!0,a.dragEndX=Te(e,a),a.dragEndY=Ce(e,a);var r=Math.abs(a.dragStartX-a.dragEndX),i=Math.abs(a.dragStartY-a.dragEndY);a.dragDirection=r<i/2?Ae:we,t.drawZoomRect_(a.dragDirection,a.dragStartX,a.dragEndX,a.dragStartY,a.dragEndY,a.prevDragDirection,a.prevEndX,a.prevEndY),a.prevEndX=a.dragEndX,a.prevEndY=a.dragEndY,a.prevDragDirection=a.dragDirection};E.treatMouseOpAsClick=function(e,t,a){for(var r=e.getFunctionOption("clickCallback"),i=e.getFunctionOption("pointClickCallback"),n=null,s=-1,l=Number.MAX_VALUE,o=0;o<e.selPoints_.length;o++){var h=e.selPoints_[o],f=Math.pow(h.canvasx-a.dragEndX,2)+Math.pow(h.canvasy-a.dragEndY,2);!isNaN(f)&&(s==-1||f<l)&&(l=f,s=o)}var u=e.getNumericOption("highlightCircleSize")+2;if(l<=u*u&&(n=e.selPoints_[s]),n){var c={cancelable:!0,point:n,canvasx:a.dragEndX,canvasy:a.dragEndY},d=e.cascadeEvents_("pointClick",c);if(d)return;i&&i.call(e,t,n)}var c={cancelable:!0,xval:e.lastx_,pts:e.selPoints_,canvasx:a.dragEndX,canvasy:a.dragEndY};e.cascadeEvents_("click",c)||r&&r.call(e,t,e.lastx_,e.selPoints_)};E.endZoom=function(e,t,a){t.clearZoomRect_(),a.isZooming=!1,E.maybeTreatMouseOpAsClick(e,t,a);var r=t.getArea();if(a.regionWidth>=10&&a.dragDirection==we){var i=Math.min(a.dragStartX,a.dragEndX),n=Math.max(a.dragStartX,a.dragEndX);i=Math.max(i,r.x),n=Math.min(n,r.x+r.w),i<n&&t.doZoomX_(i,n),a.cancelNextDblclick=!0}else if(a.regionHeight>=10&&a.dragDirection==Ae){var s=Math.min(a.dragStartY,a.dragEndY),l=Math.max(a.dragStartY,a.dragEndY);s=Math.max(s,r.y),l=Math.min(l,r.y+r.h),s<l&&t.doZoomY_(s,l),a.cancelNextDblclick=!0}a.dragStartX=null,a.dragStartY=null};E.startTouch=function(e,t,a){e.preventDefault(),e.touches.length>1&&(a.startTimeForDoubleTapMs=null);for(var r=[],i=0;i<e.touches.length;i++){var n=e.touches[i],s=n.target.getBoundingClientRect();r.push({pageX:n.pageX,pageY:n.pageY,dataX:t.toDataXCoord(n.clientX-s.left),dataY:t.toDataYCoord(n.clientY-s.top)})}if(a.initialTouches=r,r.length==1)a.initialPinchCenter=r[0],a.touchDirections={x:!0,y:!0};else if(r.length>=2){a.initialPinchCenter={pageX:.5*(r[0].pageX+r[1].pageX),pageY:.5*(r[0].pageY+r[1].pageY),dataX:.5*(r[0].dataX+r[1].dataX),dataY:.5*(r[0].dataY+r[1].dataY)};var l=180/Math.PI*Math.atan2(a.initialPinchCenter.pageY-r[0].pageY,r[0].pageX-a.initialPinchCenter.pageX);l=Math.abs(l),l>90&&(l=90-l),a.touchDirections={x:l<90-45/2,y:l>45/2}}a.initialRange={x:t.xAxisRange(),y:t.yAxisRange()}};E.moveTouch=function(e,t,a){a.startTimeForDoubleTapMs=null;var r,i=[];for(r=0;r<e.touches.length;r++){var n=e.touches[r];i.push({pageX:n.pageX,pageY:n.pageY})}var s=a.initialTouches,l,o=a.initialPinchCenter;i.length==1?l=i[0]:l={pageX:.5*(i[0].pageX+i[1].pageX),pageY:.5*(i[0].pageY+i[1].pageY)};var h={pageX:l.pageX-o.pageX,pageY:l.pageY-o.pageY},f=a.initialRange.x[1]-a.initialRange.x[0],u=a.initialRange.y[0]-a.initialRange.y[1];h.dataX=h.pageX/t.plotter_.area.w*f,h.dataY=h.pageY/t.plotter_.area.h*u;var d,c;if(i.length==1)d=1,c=1;else if(i.length>=2){var p=s[1].pageX-o.pageX;d=(i[1].pageX-l.pageX)/p;var v=s[1].pageY-o.pageY;c=(i[1].pageY-l.pageY)/v}d=Math.min(8,Math.max(.125,d)),c=Math.min(8,Math.max(.125,c));var _=!1;if(a.touchDirections.x){var y=o.dataX-h.dataX/d;t.dateWindow_=[y+(a.initialRange.x[0]-o.dataX)/d,y+(a.initialRange.x[1]-o.dataX)/d],_=!0}if(a.touchDirections.y)for(r=0;r<1;r++){var m=t.axes_[r],b=t.attributes_.getForAxis("logscale",r);if(!b){var y=o.dataY-h.dataY/c;m.valueRange=[y+(a.initialRange.y[0]-o.dataY)/c,y+(a.initialRange.y[1]-o.dataY)/c],_=!0}}if(t.drawGraph_(!1),_&&i.length>1&&t.getFunctionOption("zoomCallback")){var w=t.xAxisRange();t.getFunctionOption("zoomCallback").call(t,w[0],w[1],t.yAxisRanges())}};E.endTouch=function(e,t,a){if(e.touches.length!==0)E.startTouch(e,t,a);else if(e.changedTouches.length==1){var r=new Date().getTime(),i=e.changedTouches[0];a.startTimeForDoubleTapMs&&r-a.startTimeForDoubleTapMs<500&&a.doubleTapX&&Math.abs(a.doubleTapX-i.screenX)<50&&a.doubleTapY&&Math.abs(a.doubleTapY-i.screenY)<50?t.resetZoom():(a.startTimeForDoubleTapMs=r,a.doubleTapX=i.screenX,a.doubleTapY=i.screenY)}};var at=function(e,t,a){return e<t?t-e:e>a?e-a:0},Xt=function(e,t){var a=fe(t.canvas_),r={left:a.x,right:a.x+t.canvas_.offsetWidth,top:a.y,bottom:a.y+t.canvas_.offsetHeight},i={x:Ee(e),y:Oe(e)},n=at(i.x,r.left,r.right),s=at(i.y,r.top,r.bottom);return Math.max(n,s)};E.defaultModel={mousedown:function(e,t,a){if(!(e.button&&e.button==2)){a.initializeMouseDown(e,t,a),e.altKey||e.shiftKey?E.startPan(e,t,a):E.startZoom(e,t,a);var r=function(n){if(a.isZooming){var s=Xt(n,t);s<It?E.moveZoom(n,t,a):a.dragEndX!==null&&(a.dragEndX=null,a.dragEndY=null,t.clearZoomRect_())}else a.isPanning&&E.movePan(n,t,a)},i=function(n){a.isZooming?a.dragEndX!==null?E.endZoom(n,t,a):E.maybeTreatMouseOpAsClick(n,t,a):a.isPanning&&E.endPan(n,t,a),V(document,"mousemove",r),V(document,"mouseup",i),a.destroy()};t.addAndTrackEvent(document,"mousemove",r),t.addAndTrackEvent(document,"mouseup",i)}},willDestroyContextMyself:!0,touchstart:function(e,t,a){E.startTouch(e,t,a)},touchmove:function(e,t,a){E.moveTouch(e,t,a)},touchend:function(e,t,a){E.endTouch(e,t,a)},dblclick:function(e,t,a){if(a.cancelNextDblclick){a.cancelNextDblclick=!1;return}var r={canvasx:a.dragEndX,canvasy:a.dragEndY,cancelable:!0};t.cascadeEvents_("dblclick",r)||e.altKey||e.shiftKey||t.resetZoom()}};E.nonInteractiveModel_={mousedown:function(e,t,a){a.initializeMouseDown(e,t,a)},mouseup:E.maybeTreatMouseOpAsClick};E.dragIsPanInteractionModel={mousedown:function(e,t,a){a.initializeMouseDown(e,t,a),E.startPan(e,t,a)},mousemove:function(e,t,a){a.isPanning&&E.movePan(e,t,a)},mouseup:function(e,t,a){a.isPanning&&E.endPan(e,t,a)}};var De={highlightCircleSize:3,highlightSeriesOpts:null,highlightSeriesBackgroundAlpha:.5,highlightSeriesBackgroundColor:"rgb(255, 255, 255)",labelsSeparateLines:!1,labelsShowZeroValues:!0,labelsKMB:!1,labelsKMG2:!1,showLabelsOnHighlight:!0,digitsAfterDecimal:2,maxNumberWidth:6,sigFigs:null,strokeWidth:1,strokeBorderWidth:0,strokeBorderColor:"white",axisTickSize:3,axisLabelFontSize:14,rightGap:5,showRoller:!1,xValueParser:void 0,delimiter:",",sigma:2,errorBars:!1,fractions:!1,wilsonInterval:!0,customBars:!1,fillGraph:!1,fillAlpha:.15,connectSeparatedPoints:!1,stackedGraph:!1,stackedGraphNaNFill:"all",hideOverlayOnMouseOut:!0,resizable:"no",legend:"onmouseover",legendFollowOffsetX:50,legendFollowOffsetY:-50,stepPlot:!1,xRangePad:0,yRangePad:null,drawAxesAtZero:!1,titleHeight:28,xLabelHeight:18,yLabelWidth:18,axisLineColor:"black",axisLineWidth:.3,gridLineWidth:.3,axisLabelWidth:50,gridLineColor:"rgb(128,128,128)",interactionModel:E.defaultModel,animatedZooms:!1,animateBackgroundFade:!0,showRangeSelector:!1,rangeSelectorHeight:40,rangeSelectorPlotStrokeColor:"#808FAB",rangeSelectorPlotFillGradientColor:"white",rangeSelectorPlotFillColor:"#A7B1C4",rangeSelectorBackgroundStrokeColor:"gray",rangeSelectorBackgroundLineWidth:1,rangeSelectorPlotLineWidth:1.5,rangeSelectorForegroundStrokeColor:"black",rangeSelectorForegroundLineWidth:1,rangeSelectorAlpha:.6,showInRangeSelector:null,plotter:[N._fillPlotter,N._errorPlotter,N._linePlotter],plugins:[],axes:{x:{pixelsPerLabel:70,axisLabelWidth:60,axisLabelFormatter:ce,valueFormatter:Ne,drawGrid:!0,drawAxis:!0,independentTicks:!0,ticker:ue},y:{axisLabelWidth:50,pixelsPerLabel:30,valueFormatter:Ze,axisLabelFormatter:We,drawGrid:!0,drawAxis:!0,independentTicks:!0,ticker:te},y2:{axisLabelWidth:50,pixelsPerLabel:30,valueFormatter:Ze,axisLabelFormatter:We,drawAxis:!0,drawGrid:!1,independentTicks:!1,ticker:te}}},X=function(e){this.dygraph_=e,this.yAxes_=[],this.xAxis_={},this.series_={},this.global_=this.dygraph_.attrs_,this.user_=this.dygraph_.user_attrs_||{},this.labels_=[],this.highlightSeries_=this.get("highlightSeriesOpts")||{},this.reparseSeries()};X.AXIS_STRING_MAPPINGS_={y:0,Y:0,y1:0,Y1:0,y2:1,Y2:1};X.axisToIndex_=function(e){if(typeof e=="string"){if(X.AXIS_STRING_MAPPINGS_.hasOwnProperty(e))return X.AXIS_STRING_MAPPINGS_[e];throw"Unknown axis : "+e}if(typeof e=="number"){if(e===0||e===1)return e;throw"Dygraphs only supports two y-axes, indexed from 0-1."}if(e)throw"Unknown axis : "+e;return 0};X.prototype.reparseSeries=function(){var e=this.get("labels");if(e){this.labels_=e.slice(1),this.yAxes_=[{series:[],options:{}}],this.xAxis_={options:{}},this.series_={};for(var t=this.user_.series||{},a=0;a<this.labels_.length;a++){var r=this.labels_[a],i=t[r]||{},n=X.axisToIndex_(i.axis);this.series_[r]={idx:a,yAxis:n,options:i},this.yAxes_[n]?this.yAxes_[n].series.push(r):this.yAxes_[n]={series:[r],options:{}}}var s=this.user_.axes||{};U(this.yAxes_[0].options,s.y||{}),this.yAxes_.length>1&&U(this.yAxes_[1].options,s.y2||{}),U(this.xAxis_.options,s.x||{})}};X.prototype.get=function(e){var t=this.getGlobalUser_(e);return t!==null?t:this.getGlobalDefault_(e)};X.prototype.getGlobalUser_=function(e){return this.user_.hasOwnProperty(e)?this.user_[e]:null};X.prototype.getGlobalDefault_=function(e){return this.global_.hasOwnProperty(e)?this.global_[e]:De.hasOwnProperty(e)?De[e]:null};X.prototype.getForAxis=function(e,t){var a,r;if(typeof t=="number")a=t,r=a===0?"y":"y2";else{if(t=="y1"&&(t="y"),t=="y")a=0;else if(t=="y2")a=1;else if(t=="x")a=-1;else throw"Unknown axis "+t;r=t}var i=a==-1?this.xAxis_:this.yAxes_[a];if(i){var n=i.options;if(n.hasOwnProperty(e))return n[e]}if(!(t==="x"&&e==="logscale")){var s=this.getGlobalUser_(e);if(s!==null)return s}var l=De.axes[r];return l.hasOwnProperty(e)?l[e]:this.getGlobalDefault_(e)};X.prototype.getForSeries=function(e,t){if(t===this.dygraph_.getHighlightSeries()&&this.highlightSeries_.hasOwnProperty(e))return this.highlightSeries_[e];if(!this.series_.hasOwnProperty(t))throw"Unknown series: "+t;var a=this.series_[t],r=a.options;return r.hasOwnProperty(e)?r[e]:this.getForAxis(e,a.yAxis)};X.prototype.numAxes=function(){return this.yAxes_.length};X.prototype.axisForSeries=function(e){return this.series_[e].yAxis};X.prototype.axisOptions=function(e){return this.yAxes_[e].options};X.prototype.seriesForAxis=function(e){return this.yAxes_[e].series};X.prototype.seriesNames=function(){return this.labels_};function Pe(){this.tarps=[]}Pe.prototype.cover=function(){for(var e=document.getElementsByTagName("iframe"),t=0;t<e.length;t++){var a=e[t],r=fe(a),i=r.x,n=r.y,s=a.offsetWidth,l=a.offsetHeight,o=document.createElement("div");o.style.position="absolute",o.style.left=i+"px",o.style.top=n+"px",o.style.width=s+"px",o.style.height=l+"px",o.style.zIndex=999,document.body.appendChild(o),this.tarps.push(o)}};Pe.prototype.uncover=function(){for(var e=0;e<this.tarps.length;e++)this.tarps[e].parentNode.removeChild(this.tarps[e]);this.tarps=[]};/**
 * @license
 * Copyright 2013 David Eberlein (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var ne=function(){},Y=ne;Y.X=0;Y.Y=1;Y.EXTRAS=2;Y.prototype.extractSeries=function(e,t,a){};Y.prototype.seriesToPoints=function(e,t,a){for(var r=[],i=0;i<e.length;++i){var n=e[i],s=n[1],l=s===null?null:Y.parseFloat(s),o={x:NaN,y:NaN,xval:Y.parseFloat(n[0]),yval:l,name:t,idx:i+a,canvasx:NaN,canvasy:NaN};r.push(o)}return this.onPointsCreated_(e,r),r};Y.prototype.onPointsCreated_=function(e,t){};Y.prototype.rollingAverage=function(e,t,a,r){};Y.prototype.getExtremeYValues=function(e,t,a){};Y.prototype.onLineEvaluated=function(e,t,a){};Y.parseFloat=function(e){return e===null?NaN:e};/**
 * @license
 * Copyright 2013 David Eberlein (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var ae=function(){};ae.prototype=new ne;ae.prototype.extractSeries=function(e,t,a){var r=[];const i=a.get("labels")[t],n=a.getForSeries("logscale",i);for(var s=0;s<e.length;s++){var l=e[s][0],o=e[s][t];n&&o<=0&&(o=null),r.push([l,o])}return r};ae.prototype.rollingAverage=function(e,t,a,n){t=Math.min(t,e.length);var i=[],n,s,l,o,h;if(t==1)return e;for(n=0;n<e.length;n++){for(o=0,h=0,s=Math.max(0,n-t+1);s<n+1;s++)l=e[s][1],!(l===null||isNaN(l))&&(h++,o+=e[s][1]);h?i[n]=[e[n][0],o/h]:i[n]=[e[n][0],null]}return i};ae.prototype.getExtremeYValues=function(t,a,r){for(var i=null,n=null,s,l=0,o=t.length-1,h=l;h<=o;h++)s=t[h][1],!(s===null||isNaN(s))&&((n===null||s>n)&&(n=s),(i===null||s<i)&&(i=s));return[i,n]};/**
 * @license
 * Copyright 2013 David Eberlein (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var z=function(){ne.call(this)};z.prototype=new ne;z.prototype.extractSeries=function(e,t,a){};z.prototype.rollingAverage=function(e,t,a,r){};z.prototype.onPointsCreated_=function(e,t){for(var a=0;a<e.length;++a){var r=e[a],i=t[a];i.y_top=NaN,i.y_bottom=NaN,i.yval_minus=ne.parseFloat(r[2][0]),i.yval_plus=ne.parseFloat(r[2][1])}};z.prototype.getExtremeYValues=function(e,t,a){for(var r=null,i=null,n,s=0,l=e.length-1,o=s;o<=l;o++)if(n=e[o][1],!(n===null||isNaN(n))){var h=e[o][2][0],f=e[o][2][1];h>n&&(h=n),f<n&&(f=n),(i===null||f>i)&&(i=f),(r===null||h<r)&&(r=h)}return[r,i]};z.prototype.onLineEvaluated=function(e,t,a){for(var r,i=0;i<e.length;i++)r=e[i],r.y_top=H.calcYNormal_(t,r.yval_minus,a),r.y_bottom=H.calcYNormal_(t,r.yval_plus,a)};/**
 * @license
 * Copyright 2013 David Eberlein (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var ve=function(){};ve.prototype=new z;ve.prototype.extractSeries=function(e,t,a){var r=[],i,n,s,l;const o=a.get("labels")[t],h=a.getForSeries("logscale",o),f=a.getForSeries("sigma",o);for(var u=0;u<e.length;u++)i=e[u][0],l=e[u][t],h&&l!==null&&(l[0]<=0||l[0]-f*l[1]<=0)&&(l=null),l!==null?(n=l[0],n!==null&&!isNaN(n)?(s=f*l[1],r.push([i,n,[n-s,n+s,l[1]]])):r.push([i,n,[n,n,n]])):r.push([i,null,[null,null,null]]);return r};ve.prototype.rollingAverage=function(e,t,a,l){t=Math.min(t,e.length);var i=[];const n=a.get("labels")[l],s=a.getForSeries("sigma",n);var l,o,h,f,u,d,c,p,v;for(l=0;l<e.length;l++){for(u=0,p=0,d=0,o=Math.max(0,l-t+1);o<l+1;o++)h=e[o][1],!(h===null||isNaN(h))&&(d++,u+=h,p+=Math.pow(e[o][2][2],2));d?(c=Math.sqrt(p)/d,v=u/d,i[l]=[e[l][0],v,[v-s*c,v+s*c]]):(f=t==1?e[l][1]:null,i[l]=[e[l][0],f,[f,f]])}return i};/**
 * @license
 * Copyright 2013 David Eberlein (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var pe=function(){};pe.prototype=new z;pe.prototype.extractSeries=function(e,t,a){var r=[],i,n,s;const l=a.get("labels")[t],o=a.getForSeries("logscale",l);for(var h=0;h<e.length;h++)i=e[h][0],s=e[h][t],o&&s!==null&&(s[0]<=0||s[1]<=0||s[2]<=0)&&(s=null),s!==null?(n=s[1],n!==null&&!isNaN(n)?r.push([i,n,[s[0],s[2]]]):r.push([i,n,[n,n]])):r.push([i,null,[null,null]]);return r};pe.prototype.rollingAverage=function(e,t,a,f){t=Math.min(t,e.length);var i=[],n,s,l,o,h,f,u;for(s=0,o=0,l=0,h=0,f=0;f<e.length;f++){if(n=e[f][1],u=e[f][2],i[f]=e[f],n!==null&&!isNaN(n)&&(s+=u[0],o+=n,l+=u[1],h+=1),f-t>=0){var d=e[f-t];d[1]!==null&&!isNaN(d[1])&&(s-=d[2][0],o-=d[1],l-=d[2][1],h-=1)}h?i[f]=[e[f][0],1*o/h,[1*s/h,1*l/h]]:i[f]=[e[f][0],null,[null,null]]}return i};/**
 * @license
 * Copyright 2013 David Eberlein (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var ge=function(){};ge.prototype=new ae;ge.prototype.extractSeries=function(e,t,a){var r=[],i,n,s,l,o,h,f=100;const u=a.get("labels")[t],d=a.getForSeries("logscale",u);for(var c=0;c<e.length;c++)i=e[c][0],s=e[c][t],d&&s!==null&&(s[0]<=0||s[1]<=0)&&(s=null),s!==null?(l=s[0],o=s[1],l!==null&&!isNaN(l)?(h=o?l/o:0,n=f*h,r.push([i,n,[l,o]])):r.push([i,l,[l,o]])):r.push([i,null,[null,null]]);return r};ge.prototype.rollingAverage=function(e,t,a,n){t=Math.min(t,e.length);var i=[],n,s=0,l=0,o=100;for(n=0;n<e.length;n++){s+=e[n][2][0],l+=e[n][2][1],n-t>=0&&(s-=e[n-t][2][0],l-=e[n-t][2][1]);var h=e[n][0],f=l?s/l:0;i[n]=[h,o*f]}return i};/**
 * @license
 * Copyright 2013 David Eberlein (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var _e=function(){};_e.prototype=new z;_e.prototype.extractSeries=function(e,t,a){var r=[],i,n,s,l,o,h,f,u,d=100;const c=a.get("labels")[t],p=a.getForSeries("logscale",c),v=a.getForSeries("sigma",c);for(var _=0;_<e.length;_++)i=e[_][0],s=e[_][t],p&&s!==null&&(s[0]<=0||s[1]<=0)&&(s=null),s!==null?(l=s[0],o=s[1],l!==null&&!isNaN(l)?(h=o?l/o:0,f=o?v*Math.sqrt(h*(1-h)/o):1,u=d*f,n=d*h,r.push([i,n,[n-u,n+u,l,o]])):r.push([i,l,[l,l,l,o]])):r.push([i,null,[null,null,null,null]]);return r};_e.prototype.rollingAverage=function(e,t,a,f){t=Math.min(t,e.length);var i=[];const n=a.get("labels")[f],s=a.getForSeries("sigma",n),l=a.getForSeries("wilsonInterval",n);var o,h,f,u,d=0,c=0,p=100;for(f=0;f<e.length;f++){d+=e[f][2][2],c+=e[f][2][3],f-t>=0&&(d-=e[f-t][2][2],c-=e[f-t][2][3]);var v=e[f][0],_=c?d/c:0;if(l)if(c){var y=_<0?0:_,m=c,b=s*Math.sqrt(y*(1-y)/m+s*s/(4*m*m)),w=1+s*s/c;o=(y+s*s/(2*c)-b)/w,h=(y+s*s/(2*c)+b)/w,i[f]=[v,y*p,[o*p,h*p]]}else i[f]=[v,0,[0,0]];else u=c?s*Math.sqrt(_*(1-_)/c):1,i[f]=[v,p*_,[p*(_-u),p*(_+u)]]}return i};/**
 * @license
 * Copyright 2012 Dan Vanderkam (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var Q=function(){this.annotations_=[]};Q.prototype.toString=function(){return"Annotations Plugin"};Q.prototype.activate=function(e){return{clearChart:this.clearChart,didDrawChart:this.didDrawChart}};Q.prototype.detachLabels=function(){for(var e=0;e<this.annotations_.length;e++){var t=this.annotations_[e];t.parentNode&&t.parentNode.removeChild(t),this.annotations_[e]=null}this.annotations_=[]};Q.prototype.clearChart=function(e){this.detachLabels()};Q.prototype.didDrawChart=function(e){var t=e.dygraph,a=t.layout_.annotated_points;if(!(!a||a.length===0))for(var r=e.canvas.parentNode,i=function(w,L,R){return function(A){var x=R.annotation;x.hasOwnProperty(w)?x[w](x,R,t,A):t.getOption(L)&&t.getOption(L)(x,R,t,A)}},n=e.dygraph.getArea(),s={},l=0;l<a.length;l++){var o=a[l];if(!(o.canvasx<n.x||o.canvasx>n.x+n.w||o.canvasy<n.y||o.canvasy>n.y+n.h)){var h=o.annotation,f=6;h.hasOwnProperty("tickHeight")&&(f=h.tickHeight);var u=document.createElement("div");u.style.fontSize=t.getOption("axisLabelFontSize")+"px";var d="dygraph-annotation";h.hasOwnProperty("icon")||(d+=" dygraphDefaultAnnotation dygraph-default-annotation"),h.hasOwnProperty("cssClass")&&(d+=" "+h.cssClass),u.className=d;var c=h.hasOwnProperty("width")?h.width:16,p=h.hasOwnProperty("height")?h.height:16;if(h.hasOwnProperty("icon")){var v=document.createElement("img");v.src=h.icon,v.width=c,v.height=p,u.appendChild(v)}else o.annotation.hasOwnProperty("shortText")&&u.appendChild(document.createTextNode(o.annotation.shortText));var _=o.canvasx-c/2;u.style.left=_+"px";var y=0;if(h.attachAtBottom){var m=n.y+n.h-p-f;s[_]?m-=s[_]:s[_]=0,s[_]+=f+p,y=m}else y=o.canvasy-p-f;u.style.top=y+"px",u.style.width=c+"px",u.style.height=p+"px",u.title=o.annotation.text,u.style.color=t.colorsMap_[o.name],u.style.borderColor=t.colorsMap_[o.name],h.div=u,t.addAndTrackEvent(u,"click",i("clickHandler","annotationClickHandler",o)),t.addAndTrackEvent(u,"mouseover",i("mouseOverHandler","annotationMouseOverHandler",o)),t.addAndTrackEvent(u,"mouseout",i("mouseOutHandler","annotationMouseOutHandler",o)),t.addAndTrackEvent(u,"dblclick",i("dblClickHandler","annotationDblClickHandler",o)),r.appendChild(u),this.annotations_.push(u);var b=e.drawingContext;if(b.save(),b.strokeStyle=h.hasOwnProperty("tickColor")?h.tickColor:t.colorsMap_[o.name],b.lineWidth=h.hasOwnProperty("tickWidth")?h.tickWidth:t.getOption("strokeWidth"),b.beginPath(),!h.attachAtBottom)b.moveTo(o.canvasx,o.canvasy),b.lineTo(o.canvasx,o.canvasy-2-f);else{var m=y+p;b.moveTo(o.canvasx,m),b.lineTo(o.canvasx,m+f)}b.closePath(),b.stroke(),b.restore()}}};Q.prototype.destroy=function(){this.detachLabels()};/**
 * @license
 * Copyright 2012 Dan Vanderkam (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var J=function(){this.xlabels_=[],this.ylabels_=[]};J.prototype.toString=function(){return"Axes Plugin"};J.prototype.activate=function(e){return{layout:this.layout,clearChart:this.clearChart,willDrawChart:this.willDrawChart}};J.prototype.layout=function(e){var t=e.dygraph;if(t.getOptionForAxis("drawAxis","y")){var a=t.getOptionForAxis("axisLabelWidth","y")+2*t.getOptionForAxis("axisTickSize","y");e.reserveSpaceLeft(a)}if(t.getOptionForAxis("drawAxis","x")){var r;t.getOption("xAxisHeight")?r=t.getOption("xAxisHeight"):r=t.getOptionForAxis("axisLabelFontSize","x")+2*t.getOptionForAxis("axisTickSize","x"),e.reserveSpaceBottom(r)}if(t.numAxes()==2){if(t.getOptionForAxis("drawAxis","y2")){var a=t.getOptionForAxis("axisLabelWidth","y2")+2*t.getOptionForAxis("axisTickSize","y2");e.reserveSpaceRight(a)}}else t.numAxes()>2&&t.error("Only two y-axes are supported at this time. (Trying to use "+t.numAxes()+")")};J.prototype.detachLabels=function(){function e(t){for(var a=0;a<t.length;a++){var r=t[a];r.parentNode&&r.parentNode.removeChild(r)}}e(this.xlabels_),e(this.ylabels_),this.xlabels_=[],this.ylabels_=[]};J.prototype.clearChart=function(e){this.detachLabels()};J.prototype.willDrawChart=function(e){var t=e.dygraph;if(!t.getOptionForAxis("drawAxis","x")&&!t.getOptionForAxis("drawAxis","y")&&!t.getOptionForAxis("drawAxis","y2"))return;function a(x){return Math.round(x)+.5}function r(x){return Math.round(x)-.5}var i=e.drawingContext,n=e.canvas.parentNode,s=t.width_,l=t.height_,o,h,f,u=function(x){return{position:"absolute",fontSize:t.getOptionForAxis("axisLabelFontSize",x)+"px",width:t.getOptionForAxis("axisLabelWidth",x)+"px"}},d={x:u("x"),y:u("y"),y2:u("y2")},c=function(x,D,S){var M=document.createElement("div"),I=d[S=="y2"?"y2":D];U(M.style,I);var Z=document.createElement("div");return Z.className="dygraph-axis-label dygraph-axis-label-"+D+(S?" dygraph-axis-label-"+S:""),Z.innerHTML=x,M.appendChild(Z),M};i.save();var p=t.layout_,v=e.dygraph.plotter_.area,_=function(x){return function(D){return t.getOptionForAxis(D,x)}};const y=this;if(t.getOptionForAxis("drawAxis","y")||t.numAxes()==2&&t.getOptionForAxis("drawAxis","y2")){if(p.yticks&&p.yticks.length>0){var m=t.numAxes(),b=[_("y"),_("y2")];p.yticks.forEach(function(x){if(x.label!==void 0){h=v.x;var D="y1",S=b[0];if(x.axis==1&&(h=v.x+v.w,D="y2",S=b[1]),!!S("drawAxis")){var M=S("axisLabelFontSize");f=v.y+x.pos*v.h,o=c(x.label,"y",m==2?D:null);var I=f-M/2;I<0&&(I=0),I+M+3>l?o.style.bottom="0":o.style.top=Math.min(I,l-2*M)+"px",x.axis===0?(o.style.left=v.x-S("axisLabelWidth")-S("axisTickSize")+"px",o.style.textAlign="right"):x.axis==1&&(o.style.left=v.x+v.w+S("axisTickSize")+"px",o.style.textAlign="left"),o.style.width=S("axisLabelWidth")+"px",n.appendChild(o),y.ylabels_.push(o)}}})}var w;if(t.getOption("drawAxesAtZero")){var L=t.toPercentXCoord(0);(L>1||L<0||isNaN(L))&&(L=0),w=a(v.x+L*v.w)}else w=a(v.x);i.strokeStyle=t.getOptionForAxis("axisLineColor","y"),i.lineWidth=t.getOptionForAxis("axisLineWidth","y"),i.beginPath(),i.moveTo(w,r(v.y)),i.lineTo(w,r(v.y+v.h)),i.closePath(),i.stroke(),t.numAxes()==2&&t.getOptionForAxis("drawAxis","y2")&&(i.strokeStyle=t.getOptionForAxis("axisLineColor","y2"),i.lineWidth=t.getOptionForAxis("axisLineWidth","y2"),i.beginPath(),i.moveTo(r(v.x+v.w),r(v.y)),i.lineTo(r(v.x+v.w),r(v.y+v.h)),i.closePath(),i.stroke())}if(t.getOptionForAxis("drawAxis","x")){if(p.xticks){var R=_("x");p.xticks.forEach(function(x){if(x.label!==void 0){h=v.x+x.pos*v.w,f=v.y+v.h,o=c(x.label,"x"),o.style.textAlign="center",o.style.top=f+R("axisTickSize")+"px";var D=h-R("axisLabelWidth")/2;D+R("axisLabelWidth")>s&&(D=s-R("axisLabelWidth"),o.style.textAlign="right"),D<0&&(D=0,o.style.textAlign="left"),o.style.left=D+"px",o.style.width=R("axisLabelWidth")+"px",n.appendChild(o),y.xlabels_.push(o)}})}i.strokeStyle=t.getOptionForAxis("axisLineColor","x"),i.lineWidth=t.getOptionForAxis("axisLineWidth","x"),i.beginPath();var A;if(t.getOption("drawAxesAtZero")){var L=t.toPercentYCoord(0,0);(L>1||L<0)&&(L=1),A=r(v.y+L*v.h)}else A=r(v.y+v.h);i.moveTo(a(v.x),A),i.lineTo(a(v.x+v.w),A),i.closePath(),i.stroke()}i.restore()};/**
 * @license
 * Copyright 2012 Dan Vanderkam (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var K=function(){this.title_div_=null,this.xlabel_div_=null,this.ylabel_div_=null,this.y2label_div_=null};K.prototype.toString=function(){return"ChartLabels Plugin"};K.prototype.activate=function(e){return{layout:this.layout,didDrawChart:this.didDrawChart}};var rt=function(e){var t=document.createElement("div");return t.style.position="absolute",t.style.left=e.x+"px",t.style.top=e.y+"px",t.style.width=e.w+"px",t.style.height=e.h+"px",t};K.prototype.detachLabels_=function(){for(var e=[this.title_div_,this.xlabel_div_,this.ylabel_div_,this.y2label_div_],t=0;t<e.length;t++){var a=e[t];a&&a.parentNode&&a.parentNode.removeChild(a)}this.title_div_=null,this.xlabel_div_=null,this.ylabel_div_=null,this.y2label_div_=null};var it=function(e,t,a,r,i){var n=document.createElement("div");n.style.position="absolute",a==1?n.style.left="0px":n.style.left=t.x+"px",n.style.top=t.y+"px",n.style.width=t.w+"px",n.style.height=t.h+"px",n.style.fontSize=e.getOption("yLabelWidth")-2+"px";var s=document.createElement("div");s.style.position="absolute",s.style.width=t.h+"px",s.style.height=t.w+"px",s.style.top=t.h/2-t.w/2+"px",s.style.left=t.w/2-t.h/2+"px",s.className="dygraph-label-rotate-"+(a==1?"right":"left");var l=document.createElement("div");return l.className=r,l.innerHTML=i,s.appendChild(l),n.appendChild(s),n};K.prototype.layout=function(e){this.detachLabels_();var t=e.dygraph,a=e.chart_div;if(t.getOption("title")){var r=e.reserveSpaceTop(t.getOption("titleHeight"));this.title_div_=rt(r),this.title_div_.style.fontSize=t.getOption("titleHeight")-8+"px";var i=document.createElement("div");i.className="dygraph-label dygraph-title",i.innerHTML=t.getOption("title"),this.title_div_.appendChild(i),a.appendChild(this.title_div_)}if(t.getOption("xlabel")){var n=e.reserveSpaceBottom(t.getOption("xLabelHeight"));this.xlabel_div_=rt(n),this.xlabel_div_.style.fontSize=t.getOption("xLabelHeight")-2+"px";var i=document.createElement("div");i.className="dygraph-label dygraph-xlabel",i.innerHTML=t.getOption("xlabel"),this.xlabel_div_.appendChild(i),a.appendChild(this.xlabel_div_)}if(t.getOption("ylabel")){var s=e.reserveSpaceLeft(0);this.ylabel_div_=it(t,s,1,"dygraph-label dygraph-ylabel",t.getOption("ylabel")),a.appendChild(this.ylabel_div_)}if(t.getOption("y2label")&&t.numAxes()==2){var l=e.reserveSpaceRight(0);this.y2label_div_=it(t,l,2,"dygraph-label dygraph-y2label",t.getOption("y2label")),a.appendChild(this.y2label_div_)}};K.prototype.didDrawChart=function(e){var t=e.dygraph;this.title_div_&&(this.title_div_.children[0].innerHTML=t.getOption("title")),this.xlabel_div_&&(this.xlabel_div_.children[0].innerHTML=t.getOption("xlabel")),this.ylabel_div_&&(this.ylabel_div_.children[0].children[0].innerHTML=t.getOption("ylabel")),this.y2label_div_&&(this.y2label_div_.children[0].children[0].innerHTML=t.getOption("y2label"))};K.prototype.clearChart=function(){};K.prototype.destroy=function(){this.detachLabels_()};/**
 * @license
 * Copyright 2012 Dan Vanderkam (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var se=function(){};se.prototype.toString=function(){return"Gridline Plugin"};se.prototype.activate=function(e){return{willDrawChart:this.willDrawChart}};se.prototype.willDrawChart=function(e){var t=e.dygraph,a=e.drawingContext,r=t.layout_,i=e.dygraph.plotter_.area;function n(y){return Math.round(y)+.5}function s(y){return Math.round(y)-.5}var l,o,h,f;if(t.getOptionForAxis("drawGrid","y")){for(var u=["y","y2"],d=[],c=[],p=[],v=[],_=[],h=0;h<u.length;h++)p[h]=t.getOptionForAxis("drawGrid",u[h]),p[h]&&(d[h]=t.getOptionForAxis("gridLineColor",u[h]),c[h]=t.getOptionForAxis("gridLineWidth",u[h]),_[h]=t.getOptionForAxis("gridLinePattern",u[h]),v[h]=_[h]&&_[h].length>=2);f=r.yticks,a.save(),f.forEach(y=>{if(y.has_tick){var m=y.axis;p[m]&&(a.save(),v[m]&&a.setLineDash&&a.setLineDash(_[m]),a.strokeStyle=d[m],a.lineWidth=c[m],l=n(i.x),o=s(i.y+y.pos*i.h),a.beginPath(),a.moveTo(l,o),a.lineTo(l+i.w,o),a.stroke(),a.restore())}}),a.restore()}if(t.getOptionForAxis("drawGrid","x")){f=r.xticks,a.save();var _=t.getOptionForAxis("gridLinePattern","x"),v=_&&_.length>=2;v&&a.setLineDash&&a.setLineDash(_),a.strokeStyle=t.getOptionForAxis("gridLineColor","x"),a.lineWidth=t.getOptionForAxis("gridLineWidth","x"),f.forEach(b=>{b.has_tick&&(l=n(i.x+b.pos*i.w),o=s(i.y+i.h),a.beginPath(),a.moveTo(l,o),a.lineTo(l,i.y),a.stroke())}),v&&a.setLineDash&&a.setLineDash([]),a.restore()}};se.prototype.destroy=function(){};/**
 * @license
 * Copyright 2012 Dan Vanderkam (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var W=function(){this.legend_div_=null,this.is_generated_div_=!1};W.prototype.toString=function(){return"Legend Plugin"};W.prototype.activate=function(e){var t,a=e.getOption("labelsDiv");return a&&a!==null?typeof a=="string"||a instanceof String?t=document.getElementById(a):t=a:(t=document.createElement("div"),t.className="dygraph-legend",e.graphDiv.appendChild(t),this.is_generated_div_=!0),this.legend_div_=t,this.one_em_width_=10,{select:this.select,deselect:this.deselect,predraw:this.predraw,didDrawChart:this.didDrawChart}};var Zt=function(e){var t=document.createElement("span");t.setAttribute("style","margin: 0; padding: 0 0 0 1em; border: 0;"),e.appendChild(t);var a=t.offsetWidth;return e.removeChild(t),a},Wt=function(e){return e.replace(/&/g,"&amp;").replace(/"/g,"&#34;").replace(/</g,"&lt;").replace(/>/g,"&gt;")};W.prototype.select=function(e){var t=e.selectedX,a=e.selectedPoints,r=e.selectedRow,i=e.dygraph.getOption("legend");if(i==="never"){this.legend_div_.style.display="none";return}var n=W.generateLegendHTML(e.dygraph,t,a,this.one_em_width_,r);if(n instanceof Node&&n.nodeType===Node.DOCUMENT_FRAGMENT_NODE?(this.legend_div_.innerHTML="",this.legend_div_.appendChild(n)):this.legend_div_.innerHTML=n,this.legend_div_.style.display="",i==="follow"){var s=e.dygraph.plotter_.area,l=this.legend_div_.offsetWidth,o=e.dygraph.getOptionForAxis("axisLabelWidth","y"),h=e.dygraph.getHighlightSeries(),f;h?(f=a.find(v=>v.name===h),f||(f=a[0])):f=a[0];const c=e.dygraph.getNumericOption("legendFollowOffsetX"),p=e.dygraph.getNumericOption("legendFollowOffsetY");var u=f.x*s.w+c,d=f.y*s.h+p;u+l+1>s.w&&(u=u-2*c-l-(o-s.x)),this.legend_div_.style.left=o+u+"px",this.legend_div_.style.top=d+"px"}else if(i==="onmouseover"&&this.is_generated_div_){var s=e.dygraph.plotter_.area,l=this.legend_div_.offsetWidth;this.legend_div_.style.left=s.x+s.w-l-1+"px",this.legend_div_.style.top=s.y+"px"}};W.prototype.deselect=function(e){var t=e.dygraph.getOption("legend");t!=="always"&&(this.legend_div_.style.display="none");var a=Zt(this.legend_div_);this.one_em_width_=a;var r=W.generateLegendHTML(e.dygraph,void 0,void 0,a,null);r instanceof Node&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?(this.legend_div_.innerHTML="",this.legend_div_.appendChild(r)):this.legend_div_.innerHTML=r};W.prototype.didDrawChart=function(e){this.deselect(e)};W.prototype.predraw=function(e){if(this.is_generated_div_){e.dygraph.graphDiv.appendChild(this.legend_div_);var t=e.dygraph.plotter_.area,a=this.legend_div_.offsetWidth;this.legend_div_.style.left=t.x+t.w-a-1+"px",this.legend_div_.style.top=t.y+"px"}};W.prototype.destroy=function(){this.legend_div_=null};W.generateLegendHTML=function(e,t,a,r,i){var n={dygraph:e,x:t,i,series:[]},s={},l=e.getLabels();if(l)for(var o=1;o<l.length;o++){var h=e.getPropertiesForSeries(l[o]),f=e.getOption("strokePattern",l[o]),u={dashHTML:Yt(f,h.color,r),label:l[o],labelHTML:Wt(l[o]),isVisible:h.visible,color:h.color};n.series.push(u),s[l[o]]=u}if(typeof t<"u"){var d=e.optionsViewForAxis_("x"),c=d("valueFormatter");n.xHTML=c.call(e,t,d,l[0],e,i,0);for(var p=[],v=e.numAxes(),o=0;o<v;o++)p[o]=e.optionsViewForAxis_("y"+(o?1+o:""));var _=e.getOption("labelsShowZeroValues"),y=e.getHighlightSeries();for(o=0;o<a.length;o++){var m=a[o],u=s[m.name];if(u.y=m.yval,m.yval===0&&!_||isNaN(m.canvasy)){u.isVisible=!1;continue}var h=e.getPropertiesForSeries(m.name),b=p[h.axis-1],w=b("valueFormatter"),L=w.call(e,m.yval,b,m.name,e,i,l.indexOf(m.name));U(u,{yHTML:L}),m.name==y&&(u.isHighlighted=!0)}}var R=e.getOption("legendFormatter")||W.defaultFormatter;return R.call(e,n)};W.defaultFormatter=function(e){var t=e.dygraph;if(t.getOption("showLabelsOnHighlight")!==!0)return"";var a=t.getOption("labelsSeparateLines"),r;if(typeof e.x>"u"){if(t.getOption("legend")!="always")return"";r="";for(var i=0;i<e.series.length;i++){var n=e.series[i];n.isVisible&&(r!==""&&(r+=a?"<br />":" "),r+=`<span style='font-weight: bold; color: ${n.color};'>${n.dashHTML} ${n.labelHTML}</span>`)}return r}r=e.xHTML+":";for(var i=0;i<e.series.length;i++){var n=e.series[i];if(!(!n.y&&!n.yHTML)&&n.isVisible){a&&(r+="<br>");var s=n.isHighlighted?' class="highlight"':"";r+=`<span${s}> <b><span style='color: ${n.color};'>${n.labelHTML}</span></b>:&#160;${n.yHTML}</span>`}}return r};function Yt(e,t,a){if(!e||e.length<=1)return`<div class="dygraph-legend-line" style="border-bottom-color: ${t};"></div>`;var r,i,n,s,l=0,o=0,h=[],f;for(r=0;r<=e.length;r++)l+=e[r%e.length];if(f=Math.floor(a/(l-e[0])),f>1){for(r=0;r<e.length;r++)h[r]=e[r]/a;o=h.length}else{for(f=1,r=0;r<e.length;r++)h[r]=e[r]/l;o=h.length+1}var u="";for(i=0;i<f;i++)for(r=0;r<o;r+=2)n=h[r%h.length],r<e.length?s=h[(r+1)%h.length]:s=0,u+=`<div class="dygraph-legend-dash" style="margin-right: ${s}em; padding-left: ${n}em;"></div>`;return u}/**
 * @license
 * Copyright 2011 Paul Felix (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var k=function(){this.hasTouchInterface_=typeof TouchEvent<"u",this.isMobileDevice_=/mobile|android/gi.test(navigator.appVersion),this.interfaceCreated_=!1};k.prototype.toString=function(){return"RangeSelector Plugin"};k.prototype.activate=function(e){return this.dygraph_=e,this.getOption_("showRangeSelector")&&this.createInterface_(),{layout:this.reserveSpace_,predraw:this.renderStaticLayer_,didDrawChart:this.renderInteractiveLayer_}};k.prototype.destroy=function(){this.bgcanvas_=null,this.fgcanvas_=null,this.leftZoomHandle_=null,this.rightZoomHandle_=null};k.prototype.getOption_=function(e,t){return this.dygraph_.getOption(e,t)};k.prototype.setDefaultOption_=function(e,t){this.dygraph_.attrs_[e]=t};k.prototype.createInterface_=function(){this.createCanvases_(),this.createZoomHandles_(),this.initInteraction_(),this.getOption_("animatedZooms")&&(console.warn("Animated zooms and range selector are not compatible; disabling animatedZooms."),this.dygraph_.updateOptions({animatedZooms:!1},!0)),this.interfaceCreated_=!0,this.addToGraph_()};k.prototype.addToGraph_=function(){var e=this.graphDiv_=this.dygraph_.graphDiv;e.appendChild(this.bgcanvas_),e.appendChild(this.fgcanvas_),e.appendChild(this.leftZoomHandle_),e.appendChild(this.rightZoomHandle_)};k.prototype.removeFromGraph_=function(){var e=this.graphDiv_;e.removeChild(this.bgcanvas_),e.removeChild(this.fgcanvas_),e.removeChild(this.leftZoomHandle_),e.removeChild(this.rightZoomHandle_),this.graphDiv_=null};k.prototype.reserveSpace_=function(e){this.getOption_("showRangeSelector")&&e.reserveSpaceBottom(this.getOption_("rangeSelectorHeight")+4)};k.prototype.renderStaticLayer_=function(){this.updateVisibility_()&&(this.resize_(),this.drawStaticLayer_())};k.prototype.renderInteractiveLayer_=function(){!this.updateVisibility_()||this.isChangingRange_||(this.placeZoomHandles_(),this.drawInteractiveLayer_())};k.prototype.updateVisibility_=function(){var e=this.getOption_("showRangeSelector");if(e)this.interfaceCreated_?(!this.graphDiv_||!this.graphDiv_.parentNode)&&this.addToGraph_():this.createInterface_();else if(this.graphDiv_){this.removeFromGraph_();var t=this.dygraph_;setTimeout(function(){t.width_=0,t.resize()},1)}return e};k.prototype.resize_=function(){function e(i,n,s,l){var o=l||Xe(n);i.style.top=s.y+"px",i.style.left=s.x+"px",i.width=s.w*o,i.height=s.h*o,i.style.width=s.w+"px",i.style.height=s.h+"px",o!=1&&n.scale(o,o)}var t=this.dygraph_.layout_.getPlotArea(),a=0;this.dygraph_.getOptionForAxis("drawAxis","x")&&(a=this.getOption_("xAxisHeight")||this.getOption_("axisLabelFontSize")+2*this.getOption_("axisTickSize")),this.canvasRect_={x:t.x,y:t.y+t.h+a+4,w:t.w,h:this.getOption_("rangeSelectorHeight")};var r=this.dygraph_.getNumericOption("pixelRatio");e(this.bgcanvas_,this.bgcanvas_ctx_,this.canvasRect_,r),e(this.fgcanvas_,this.fgcanvas_ctx_,this.canvasRect_,r)};k.prototype.createCanvases_=function(){this.bgcanvas_=Le(),this.bgcanvas_.className="dygraph-rangesel-bgcanvas",this.bgcanvas_.style.position="absolute",this.bgcanvas_.style.zIndex=9,this.bgcanvas_ctx_=xe(this.bgcanvas_),this.fgcanvas_=Le(),this.fgcanvas_.className="dygraph-rangesel-fgcanvas",this.fgcanvas_.style.position="absolute",this.fgcanvas_.style.zIndex=9,this.fgcanvas_.style.cursor="default",this.fgcanvas_ctx_=xe(this.fgcanvas_)};k.prototype.createZoomHandles_=function(){var e=new Image;e.className="dygraph-rangesel-zoomhandle",e.style.position="absolute",e.style.zIndex=10,e.style.visibility="hidden",e.style.cursor="col-resize",e.width=9,e.height=16,e.src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAAQCAYAAADESFVDAAAAAXNSR0IArs4c6QAAAAZiS0dEANAAzwDP4Z7KegAAAAlwSFlzAAAOxAAADsQBlSsOGwAAAAd0SU1FB9sHGw0cMqdt1UwAAAAZdEVYdENvbW1lbnQAQ3JlYXRlZCB3aXRoIEdJTVBXgQ4XAAAAaElEQVQoz+3SsRFAQBCF4Z9WJM8KCDVwownl6YXsTmCUsyKGkZzcl7zkz3YLkypgAnreFmDEpHkIwVOMfpdi9CEEN2nGpFdwD03yEqDtOgCaun7sqSTDH32I1pQA2Pb9sZecAxc5r3IAb21d6878xsAAAAAASUVORK5CYII=",this.isMobileDevice_&&(e.width*=2,e.height*=2),this.leftZoomHandle_=e,this.rightZoomHandle_=e.cloneNode(!1)};k.prototype.initInteraction_=function(){var e=this,t=document,a=0,r=null,i=!1,n=!1,s=!this.isMobileDevice_,l=new Pe,o,h,f,u,d,c,p,v,_,y,m,b,w,L;o=function(A){var x=e.dygraph_.xAxisExtremes(),D=(x[1]-x[0])/e.canvasRect_.w,S=x[0]+(A.leftHandlePos-e.canvasRect_.x)*D,M=x[0]+(A.rightHandlePos-e.canvasRect_.x)*D;return[S,M]},h=function(A){return $(A),i=!0,a=A.clientX,r=A.target?A.target:A.srcElement,(A.type==="mousedown"||A.type==="dragstart")&&(le(t,"mousemove",f),le(t,"mouseup",u)),e.fgcanvas_.style.cursor="col-resize",l.cover(),!0},f=function(A){if(!i)return!1;$(A);var x=A.clientX-a;if(Math.abs(x)<4)return!0;a=A.clientX;var D=e.getZoomHandleStatus_(),S;r==e.leftZoomHandle_?(S=D.leftHandlePos+x,S=Math.min(S,D.rightHandlePos-r.width-3),S=Math.max(S,e.canvasRect_.x)):(S=D.rightHandlePos+x,S=Math.min(S,e.canvasRect_.x+e.canvasRect_.w),S=Math.max(S,D.leftHandlePos+r.width+3));var M=r.width/2;return r.style.left=S-M+"px",e.drawInteractiveLayer_(),s&&d(),!0},u=function(A){return i?(i=!1,l.uncover(),V(t,"mousemove",f),V(t,"mouseup",u),e.fgcanvas_.style.cursor="default",s||d(),!0):!1},d=function(){try{var A=e.getZoomHandleStatus_();if(e.isChangingRange_=!0,!A.isZoomed)e.dygraph_.resetZoom();else{var x=o(A);e.dygraph_.doZoomXDates_(x[0],x[1])}}finally{e.isChangingRange_=!1}},c=function(A){var x=e.leftZoomHandle_.getBoundingClientRect(),D=x.left+x.width/2;x=e.rightZoomHandle_.getBoundingClientRect();var S=x.left+x.width/2;return A.clientX>D&&A.clientX<S},p=function(A){return!n&&c(A)&&e.getZoomHandleStatus_().isZoomed?($(A),n=!0,a=A.clientX,A.type==="mousedown"&&(le(t,"mousemove",v),le(t,"mouseup",_)),!0):!1},v=function(A){if(!n)return!1;$(A);var x=A.clientX-a;if(Math.abs(x)<4)return!0;a=A.clientX;var D=e.getZoomHandleStatus_(),S=D.leftHandlePos,M=D.rightHandlePos,I=M-S;S+x<=e.canvasRect_.x?(S=e.canvasRect_.x,M=S+I):M+x>=e.canvasRect_.x+e.canvasRect_.w?(M=e.canvasRect_.x+e.canvasRect_.w,S=M-I):(S+=x,M+=x);var Z=e.leftZoomHandle_.width/2;return e.leftZoomHandle_.style.left=S-Z+"px",e.rightZoomHandle_.style.left=M-Z+"px",e.drawInteractiveLayer_(),s&&y(),!0},_=function(A){return n?(n=!1,V(t,"mousemove",v),V(t,"mouseup",_),s||y(),!0):!1},y=function(){try{e.isChangingRange_=!0,e.dygraph_.dateWindow_=o(e.getZoomHandleStatus_()),e.dygraph_.drawGraph_(!1)}finally{e.isChangingRange_=!1}},m=function(A){if(!(i||n)){var x=c(A)?"move":"default";x!=e.fgcanvas_.style.cursor&&(e.fgcanvas_.style.cursor=x)}},b=function(A){A.type=="touchstart"&&A.targetTouches.length==1?h(A.targetTouches[0])&&$(A):A.type=="touchmove"&&A.targetTouches.length==1?f(A.targetTouches[0])&&$(A):u(A)},w=function(A){A.type=="touchstart"&&A.targetTouches.length==1?p(A.targetTouches[0])&&$(A):A.type=="touchmove"&&A.targetTouches.length==1?v(A.targetTouches[0])&&$(A):_(A)},L=function(A,x){for(var D=["touchstart","touchend","touchmove","touchcancel"],S=0;S<D.length;S++)e.dygraph_.addAndTrackEvent(A,D[S],x)},this.setDefaultOption_("interactionModel",E.dragIsPanInteractionModel),this.setDefaultOption_("panEdgeFraction",1e-4);var R=window.opera?"mousedown":"dragstart";this.dygraph_.addAndTrackEvent(this.leftZoomHandle_,R,h),this.dygraph_.addAndTrackEvent(this.rightZoomHandle_,R,h),this.dygraph_.addAndTrackEvent(this.fgcanvas_,"mousedown",p),this.dygraph_.addAndTrackEvent(this.fgcanvas_,"mousemove",m),this.hasTouchInterface_&&(L(this.leftZoomHandle_,b),L(this.rightZoomHandle_,b),L(this.fgcanvas_,w))};k.prototype.drawStaticLayer_=function(){var e=this.bgcanvas_ctx_;e.clearRect(0,0,this.canvasRect_.w,this.canvasRect_.h);try{this.drawMiniPlot_()}catch(a){console.warn(a)}var t=.5;this.bgcanvas_ctx_.lineWidth=this.getOption_("rangeSelectorBackgroundLineWidth"),e.strokeStyle=this.getOption_("rangeSelectorBackgroundStrokeColor"),e.beginPath(),e.moveTo(t,t),e.lineTo(t,this.canvasRect_.h-t),e.lineTo(this.canvasRect_.w-t,this.canvasRect_.h-t),e.lineTo(this.canvasRect_.w-t,t),e.stroke()};k.prototype.drawMiniPlot_=function(){var e=this.getOption_("rangeSelectorPlotFillColor"),t=this.getOption_("rangeSelectorPlotFillGradientColor"),a=this.getOption_("rangeSelectorPlotStrokeColor");if(!(!e&&!a)){var r=this.getOption_("stepPlot"),i=this.computeCombinedSeriesAndLimits_(),n=i.yMax-i.yMin,s=this.bgcanvas_ctx_,l=.5,o=this.dygraph_.xAxisExtremes(),h=Math.max(o[1]-o[0],1e-30),f=(this.canvasRect_.w-l)/h,u=(this.canvasRect_.h-l)/n,d=this.canvasRect_.w-l,c=this.canvasRect_.h-l,p=null,v=null;s.beginPath(),s.moveTo(l,c);for(var _=0;_<i.data.length;_++){var y=i.data[_],m=y[0]!==null?(y[0]-o[0])*f:NaN,b=y[1]!==null?c-(y[1]-i.yMin)*u:NaN;!r&&p!==null&&Math.round(m)==Math.round(p)||(isFinite(m)&&isFinite(b)?(p===null?s.lineTo(m,c):r&&s.lineTo(m,v),s.lineTo(m,b),p=m,v=b):(p!==null&&(r?(s.lineTo(m,v),s.lineTo(m,c)):s.lineTo(p,c)),p=v=null))}if(s.lineTo(d,c),s.closePath(),e){var w=this.bgcanvas_ctx_.createLinearGradient(0,0,0,c);t&&w.addColorStop(0,t),w.addColorStop(1,e),this.bgcanvas_ctx_.fillStyle=w,s.fill()}a&&(this.bgcanvas_ctx_.strokeStyle=a,this.bgcanvas_ctx_.lineWidth=this.getOption_("rangeSelectorPlotLineWidth"),s.stroke())}};k.prototype.computeCombinedSeriesAndLimits_=function(){var e=this.dygraph_,t=this.getOption_("logscale"),a,r=e.numColumns(),i=e.getLabels(),n=new Array(r),s=!1,l=e.visibility(),o=[];for(a=1;a<r;a++){var h=this.getOption_("showInRangeSelector",i[a]);o.push(h),h!==null&&(s=!0)}if(s)for(a=1;a<r;a++)n[a]=o[a-1];else for(a=1;a<r;a++)n[a]=l[a-1];var f=[],u=e.dataHandler_,d=e.attributes_;for(a=1;a<e.numColumns();a++)if(n[a]){var c=u.extractSeries(e.rawData_,a,d);e.rollPeriod()>1&&(c=u.rollingAverage(c,e.rollPeriod(),d,a)),f.push(c)}var p=[];for(a=0;a<f[0].length;a++){for(var v=0,_=0,y=0;y<f.length;y++){var m=f[y][a][1];m===null||isNaN(m)||(_++,v+=m)}p.push([f[0][a][0],v/_])}var b=Number.MAX_VALUE,w=-Number.MAX_VALUE;for(a=0;a<p.length;a++){var L=p[a][1];L!==null&&isFinite(L)&&(!t||L>0)&&(b=Math.min(b,L),w=Math.max(w,L))}var R=.25;if(t)for(w=F(w),w+=w*R,b=F(b),a=0;a<p.length;a++)p[a][1]=F(p[a][1]);else{var A,x=w-b;x<=Number.MIN_VALUE?A=w*R:A=x*R,w+=A,b-=A}return{data:p,yMin:b,yMax:w}};k.prototype.placeZoomHandles_=function(){var e=this.dygraph_.xAxisExtremes(),t=this.dygraph_.xAxisRange(),a=e[1]-e[0],r=Math.max(0,(t[0]-e[0])/a),i=Math.max(0,(e[1]-t[1])/a),n=this.canvasRect_.x+this.canvasRect_.w*r,s=this.canvasRect_.x+this.canvasRect_.w*(1-i),l=Math.max(this.canvasRect_.y,this.canvasRect_.y+(this.canvasRect_.h-this.leftZoomHandle_.height)/2),o=this.leftZoomHandle_.width/2;this.leftZoomHandle_.style.left=n-o+"px",this.leftZoomHandle_.style.top=l+"px",this.rightZoomHandle_.style.left=s-o+"px",this.rightZoomHandle_.style.top=this.leftZoomHandle_.style.top,this.leftZoomHandle_.style.visibility="visible",this.rightZoomHandle_.style.visibility="visible"};k.prototype.drawInteractiveLayer_=function(){var e=this.fgcanvas_ctx_;e.clearRect(0,0,this.canvasRect_.w,this.canvasRect_.h);var t=1,a=this.canvasRect_.w-t,r=this.canvasRect_.h-t,i=this.getZoomHandleStatus_();if(e.strokeStyle=this.getOption_("rangeSelectorForegroundStrokeColor"),e.lineWidth=this.getOption_("rangeSelectorForegroundLineWidth"),!i.isZoomed)e.beginPath(),e.moveTo(t,t),e.lineTo(t,r),e.lineTo(a,r),e.lineTo(a,t),e.stroke();else{var n=Math.max(t,i.leftHandlePos-this.canvasRect_.x),s=Math.min(a,i.rightHandlePos-this.canvasRect_.x);const l=this.getOption_("rangeSelectorVeilColour");e.fillStyle=l||"rgba(240, 240, 240, "+this.getOption_("rangeSelectorAlpha").toString()+")",e.fillRect(0,0,n,this.canvasRect_.h),e.fillRect(s,0,this.canvasRect_.w-s,this.canvasRect_.h),e.beginPath(),e.moveTo(t,t),e.lineTo(n,t),e.lineTo(n,r),e.lineTo(s,r),e.lineTo(s,t),e.lineTo(a,t),e.stroke()}};k.prototype.getZoomHandleStatus_=function(){var e=this.leftZoomHandle_.width/2,t=parseFloat(this.leftZoomHandle_.style.left)+e,a=parseFloat(this.rightZoomHandle_.style.left)+e;return{leftHandlePos:t,rightHandlePos:a,isZoomed:t-1>this.canvasRect_.x||a+1<this.canvasRect_.x+this.canvasRect_.w}};/**
 * @license
 * Copyright 2011 Dan Vanderkam (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var Re=function(e){this.container=e};Re.prototype.draw=function(e,t){this.container.innerHTML="",typeof this.date_graph<"u"&&this.date_graph.destroy(),this.date_graph=new g(this.container,e,t)};Re.prototype.setSelection=function(e){var t=!1;e.length&&(t=e[0].row),this.date_graph.setSelection(t)};Re.prototype.getSelection=function(){var e=[],t=this.date_graph.getSelection();if(t<0)return e;for(var a=this.date_graph.layout_.points,r=0;r<a.length;++r)e.push({row:t,column:r+1});return e};/**
 * @license
 * Copyright 2006 Dan Vanderkam (<EMAIL>)
 * MIT-licenced: https://opensource.org/licenses/MIT
 */var g=function(t,a,r){this.__init__(t,a,r)};g.NAME="Dygraph";g.VERSION="2.2.1";var Ye={};g._require=function(t){return t in Ye?Ye[t]:g._require._b(t)};g._require._b=null;g._require.add=function(t,a){Ye[t]=a};g.DEFAULT_ROLL_PERIOD=1;g.DEFAULT_WIDTH=480;g.DEFAULT_HEIGHT=320;g.ANIMATION_STEPS=12;g.ANIMATION_DURATION=200;g.Plotters=N._Plotters;g.addedAnnotationCSS=!1;g.prototype.__init__=function(e,t,a){if(this.is_initial_draw_=!0,this.readyFns_=[],a==null&&(a={}),a=g.copyUserAttrs_(a),typeof e=="string"&&(e=document.getElementById(e)),!e)throw new Error("Constructing dygraph with a non-existent div!");this.maindiv_=e,this.file_=t,this.rollPeriod_=a.rollPeriod||g.DEFAULT_ROLL_PERIOD,this.previousVerticalX_=-1,this.fractions_=a.fractions||!1,this.dateWindow_=a.dateWindow||null,this.annotations_=[],e.innerHTML="";const r=window.getComputedStyle(e,null);(r.paddingLeft!=="0px"||r.paddingRight!=="0px"||r.paddingTop!=="0px"||r.paddingBottom!=="0px")&&console.error("Main div contains padding; graph will misbehave"),e.style.width===""&&a.width&&(e.style.width=a.width+"px"),e.style.height===""&&a.height&&(e.style.height=a.height+"px"),e.style.height===""&&e.clientHeight===0&&(e.style.height=g.DEFAULT_HEIGHT+"px",e.style.width===""&&(e.style.width=g.DEFAULT_WIDTH+"px")),this.width_=e.clientWidth||a.width||0,this.height_=e.clientHeight||a.height||0,a.stackedGraph&&(a.fillGraph=!0),this.user_attrs_={},U(this.user_attrs_,a),this.attrs_={},Ve(this.attrs_,De),this.boundaryIds_=[],this.setIndexByName_={},this.datasetIndex_=[],this.registeredEvents_=[],this.eventListeners_={},this.attributes_=new X(this),this.createInterface_(),this.plugins_=[];for(var i=g.PLUGINS.concat(this.getOption("plugins")),n=0;n<i.length;n++){var s=i[n],l;typeof s.activate<"u"?l=s:l=new s;var o={plugin:l,events:{},options:{},pluginOptions:{}},h=l.activate(this);for(var f in h)h.hasOwnProperty(f)&&(o.events[f]=h[f]);this.plugins_.push(o)}for(var n=0;n<this.plugins_.length;n++){var u=this.plugins_[n];for(var f in u.events)if(u.events.hasOwnProperty(f)){var d=u.events[f],c=[u.plugin,d];f in this.eventListeners_?this.eventListeners_[f].push(c):this.eventListeners_[f]=[c]}}this.createDragInterface_(),this.start_()};g.prototype.cascadeEvents_=function(e,t){if(!(e in this.eventListeners_))return!1;var a={dygraph:this,cancelable:!1,defaultPrevented:!1,preventDefault:function(){if(!a.cancelable)throw"Cannot call preventDefault on non-cancelable event.";a.defaultPrevented=!0},propagationStopped:!1,stopPropagation:function(){a.propagationStopped=!0}};U(a,t);var r=this.eventListeners_[e];if(r)for(var i=r.length-1;i>=0;i--){var n=r[i][0],s=r[i][1];if(s.call(n,a),a.propagationStopped)break}return a.defaultPrevented};g.prototype.getPluginInstance_=function(e){for(var t=0;t<this.plugins_.length;t++){var a=this.plugins_[t];if(a.plugin instanceof e)return a.plugin}return null};g.prototype.isZoomed=function(e){const t=!!this.dateWindow_;if(e==="x")return t;const a=this.axes_.map(r=>!!r.valueRange).indexOf(!0)>=0;if(e==null)return t||a;if(e==="y")return a;throw new Error(`axis parameter is [${e}] must be null, 'x' or 'y'.`)};g.prototype.toString=function(){var e=this.maindiv_,t=e&&e.id?e.id:e;return"[Dygraph "+t+"]"};g.prototype.attr_=function(e,t){return t?this.attributes_.getForSeries(e,t):this.attributes_.get(e)};g.prototype.getOption=function(e,t){return this.attr_(e,t)};g.prototype.getNumericOption=function(e,t){return this.getOption(e,t)};g.prototype.getStringOption=function(e,t){return this.getOption(e,t)};g.prototype.getBooleanOption=function(e,t){return this.getOption(e,t)};g.prototype.getFunctionOption=function(e,t){return this.getOption(e,t)};g.prototype.getOptionForAxis=function(e,t){return this.attributes_.getForAxis(e,t)};g.prototype.optionsViewForAxis_=function(e){var t=this;return function(a){var r=t.user_attrs_.axes;return r&&r[e]&&r[e].hasOwnProperty(a)?r[e][a]:e==="x"&&a==="logscale"?!1:typeof t.user_attrs_[a]<"u"?t.user_attrs_[a]:(r=t.attrs_.axes,r&&r[e]&&r[e].hasOwnProperty(a)?r[e][a]:e=="y"&&t.axes_[0].hasOwnProperty(a)?t.axes_[0][a]:e=="y2"&&t.axes_[1].hasOwnProperty(a)?t.axes_[1][a]:t.attr_(a))}};g.prototype.rollPeriod=function(){return this.rollPeriod_};g.prototype.xAxisRange=function(){return this.dateWindow_?this.dateWindow_:this.xAxisExtremes()};g.prototype.xAxisExtremes=function(){var e=this.getNumericOption("xRangePad")/this.plotter_.area.w;if(this.numRows()===0)return[0-e,1+e];var t=this.rawData_[0][0],a=this.rawData_[this.rawData_.length-1][0];if(e){var r=a-t;t-=r*e,a+=r*e}return[t,a]};g.prototype.yAxisExtremes=function(){const e=this.gatherDatasets_(this.rolledSeries_,null),{extremes:t}=e,a=this.axes_;this.computeYAxisRanges_(t);const r=this.axes_;return this.axes_=a,r.map(i=>i.extremeRange)};g.prototype.yAxisRange=function(e){if(typeof e>"u"&&(e=0),e<0||e>=this.axes_.length)return null;var t=this.axes_[e];return[t.computedValueRange[0],t.computedValueRange[1]]};g.prototype.yAxisRanges=function(){for(var e=[],t=0;t<this.axes_.length;t++)e.push(this.yAxisRange(t));return e};g.prototype.toDomCoords=function(e,t,a){return[this.toDomXCoord(e),this.toDomYCoord(t,a)]};g.prototype.toDomXCoord=function(e){if(e===null)return null;var t=this.plotter_.area,a=this.xAxisRange();return t.x+(e-a[0])/(a[1]-a[0])*t.w};g.prototype.toDomYCoord=function(e,t){var a=this.toPercentYCoord(e,t);if(a===null)return null;var r=this.plotter_.area;return r.y+a*r.h};g.prototype.toDataCoords=function(e,t,a){return[this.toDataXCoord(e),this.toDataYCoord(t,a)]};g.prototype.toDataXCoord=function(e){if(e===null)return null;var t=this.plotter_.area,a=this.xAxisRange();if(this.attributes_.getForAxis("logscale","x")){var r=(e-t.x)/t.w;return be(a[0],a[1],r)}else return a[0]+(e-t.x)/t.w*(a[1]-a[0])};g.prototype.toDataYCoord=function(e,t){if(e===null)return null;var a=this.plotter_.area,r=this.yAxisRange(t);if(typeof t>"u"&&(t=0),this.attributes_.getForAxis("logscale",t)){var i=(e-a.y)/a.h;return be(r[1],r[0],i)}else return r[0]+(a.y+a.h-e)/a.h*(r[1]-r[0])};g.prototype.toPercentYCoord=function(e,t){if(e===null)return null;typeof t>"u"&&(t=0);var a=this.yAxisRange(t),r,i=this.attributes_.getForAxis("logscale",t);if(i){var n=F(a[0]),s=F(a[1]);r=(s-F(e))/(s-n)}else r=(a[1]-e)/(a[1]-a[0]);return r};g.prototype.toPercentXCoord=function(e){if(e===null)return null;var t=this.xAxisRange(),a,r=this.attributes_.getForAxis("logscale","x");if(r===!0){var i=F(t[0]),n=F(t[1]);a=(F(e)-i)/(n-i)}else a=(e-t[0])/(t[1]-t[0]);return a};g.prototype.numColumns=function(){return this.rawData_?this.rawData_[0]?this.rawData_[0].length:this.attr_("labels").length:0};g.prototype.numRows=function(){return this.rawData_?this.rawData_.length:0};g.prototype.getValue=function(e,t){return e<0||e>=this.rawData_.length||t<0||t>=this.rawData_[e].length?null:this.rawData_[e][t]};g.prototype.createInterface_=function(){var e=this.maindiv_;this.graphDiv=document.createElement("div"),this.graphDiv.style.textAlign="left",this.graphDiv.style.position="relative",e.appendChild(this.graphDiv),this.canvas_=Le(),this.canvas_.style.position="absolute",this.canvas_.style.top=0,this.canvas_.style.left=0,this.hidden_=this.createPlotKitCanvas_(this.canvas_),this.canvas_ctx_=xe(this.canvas_),this.hidden_ctx_=xe(this.hidden_),this.resizeElements_(),this.graphDiv.appendChild(this.hidden_),this.graphDiv.appendChild(this.canvas_),this.mouseEventElement_=this.createMouseEventElement_(),this.layout_=new H(this);var t=this;if(this.mouseMoveHandler_=function(r){t.mouseMove_(r)},this.mouseOutHandler_=function(r){var i=r.target||r.fromElement,n=r.relatedTarget||r.toElement;Qe(i,t.graphDiv)&&!Qe(n,t.graphDiv)&&t.mouseOut_(r)},this.addAndTrackEvent(window,"mouseout",this.mouseOutHandler_),this.addAndTrackEvent(this.mouseEventElement_,"mousemove",this.mouseMoveHandler_),!this.resizeHandler_){this.resizeHandler_=function(r){t.resize()},this.addAndTrackEvent(window,"resize",this.resizeHandler_),this.resizeObserver_=null;var a=this.getStringOption("resizable");typeof ResizeObserver>"u"&&a!=="no"&&(console.error("ResizeObserver unavailable; ignoring resizable property"),a="no"),a==="horizontal"||a==="vertical"||a==="both"?e.style.resize=a:a!=="passive"&&(a="no"),a!=="no"&&(window.getComputedStyle(e).overflow,window.getComputedStyle(e).overflow==="visible"&&(e.style.overflow="hidden"),this.resizeObserver_=new ResizeObserver(this.resizeHandler_),this.resizeObserver_.observe(e))}};g.prototype.resizeElements_=function(){this.graphDiv.style.width=this.width_+"px",this.graphDiv.style.height=this.height_+"px";var e=this.getNumericOption("pixelRatio"),t=e||Xe(this.canvas_ctx_);this.canvas_.width=this.width_*t,this.canvas_.height=this.height_*t,this.canvas_.style.width=this.width_+"px",this.canvas_.style.height=this.height_+"px",t!==1&&this.canvas_ctx_.scale(t,t);var a=e||Xe(this.hidden_ctx_);this.hidden_.width=this.width_*a,this.hidden_.height=this.height_*a,this.hidden_.style.width=this.width_+"px",this.hidden_.style.height=this.height_+"px",a!==1&&this.hidden_ctx_.scale(a,a)};g.prototype.destroy=function(){this.canvas_ctx_.restore(),this.hidden_ctx_.restore();for(var e=this.plugins_.length-1;e>=0;e--){var t=this.plugins_.pop();t.plugin.destroy&&t.plugin.destroy()}var a=function(i){for(;i.hasChildNodes();)a(i.firstChild),i.removeChild(i.firstChild)};this.removeTrackedEvents_(),V(window,"mouseout",this.mouseOutHandler_),V(this.mouseEventElement_,"mousemove",this.mouseMoveHandler_),this.resizeObserver_&&(this.resizeObserver_.disconnect(),this.resizeObserver_=null),V(window,"resize",this.resizeHandler_),this.resizeHandler_=null,a(this.maindiv_);var r=function(n){for(var s in n)typeof n[s]=="object"&&(n[s]=null)};r(this.layout_),r(this.plotter_),r(this)};g.prototype.createPlotKitCanvas_=function(e){var t=Le();return t.style.position="absolute",t.style.top=e.style.top,t.style.left=e.style.left,t.width=this.width_,t.height=this.height_,t.style.width=this.width_+"px",t.style.height=this.height_+"px",t};g.prototype.createMouseEventElement_=function(){return this.canvas_};g.prototype.setColors_=function(){var e=this.getLabels(),t=e.length-1;this.colors_=[],this.colorsMap_={};for(var a=this.getNumericOption("colorSaturation")||1,r=this.getNumericOption("colorValue")||.5,i=Math.ceil(t/2),n=this.getOption("colors"),s=this.visibility(),l=0;l<t;l++)if(s[l]){var o=e[l+1],h=this.attributes_.getForSeries("color",o);if(!h)if(n)h=n[l%n.length];else{var f=l%2?i+(l+1)/2:Math.ceil((l+1)/2),u=1*f/(1+t);h=xt(u,a,r)}this.colors_.push(h),this.colorsMap_[o]=h}};g.prototype.getColors=function(){return this.colors_};g.prototype.getPropertiesForSeries=function(e){for(var t=-1,a=this.getLabels(),r=1;r<a.length;r++)if(a[r]==e){t=r;break}return t==-1?null:{name:e,column:t,visible:this.visibility()[t-1],color:this.colorsMap_[e],axis:1+this.attributes_.axisForSeries(e)}};g.prototype.createRollInterface_=function(){var e=this.roller_;e||(this.roller_=e=document.createElement("input"),e.type="text",e.style.display="none",e.className="dygraph-roller",this.graphDiv.appendChild(e));var t=this.getBooleanOption("showRoller")?"block":"none",a=this.getArea(),r={top:a.y+a.h-25+"px",left:a.x+1+"px",display:t};e.size="2",e.value=this.rollPeriod_,U(e.style,r);const i=this;e.onchange=function(){return i.adjustRoll(e.value)}};g.prototype.createDragInterface_=function(){var e={isZooming:!1,isPanning:!1,is2DPan:!1,dragStartX:null,dragStartY:null,dragEndX:null,dragEndY:null,dragDirection:null,prevEndX:null,prevEndY:null,prevDragDirection:null,cancelNextDblclick:!1,initialLeftmostDate:null,xUnitsPerPixel:null,dateRange:null,px:0,py:0,boundedDates:null,boundedValues:null,tarp:new Pe,initializeMouseDown:function(s,l,o){s.preventDefault?s.preventDefault():(s.returnValue=!1,s.cancelBubble=!0);var h=fe(l.canvas_);o.px=h.x,o.py=h.y,o.dragStartX=Te(s,o),o.dragStartY=Ce(s,o),o.cancelNextDblclick=!1,o.tarp.cover()},destroy:function(){var s=this;if((s.isZooming||s.isPanning)&&(s.isZooming=!1,s.dragStartX=null,s.dragStartY=null),s.isPanning){s.isPanning=!1,s.draggingDate=null,s.dateRange=null;for(var l=0;l<a.axes_.length;l++)delete a.axes_[l].draggingValue,delete a.axes_[l].dragValueRange}s.tarp.uncover()}},t=this.getOption("interactionModel"),a=this,r=function(s){return function(l){s(l,a,e)}};for(var i in t)t.hasOwnProperty(i)&&this.addAndTrackEvent(this.mouseEventElement_,i,r(t[i]));if(!t.willDestroyContextMyself){var n=function(s){e.destroy()};this.addAndTrackEvent(document,"mouseup",n)}};g.prototype.drawZoomRect_=function(e,t,a,r,i,n,s,l){var o=this.canvas_ctx_;n==we?o.clearRect(Math.min(t,s),this.layout_.getPlotArea().y,Math.abs(t-s),this.layout_.getPlotArea().h):n==Ae&&o.clearRect(this.layout_.getPlotArea().x,Math.min(r,l),this.layout_.getPlotArea().w,Math.abs(r-l)),e==we?a&&t&&(o.fillStyle="rgba(128,128,128,0.33)",o.fillRect(Math.min(t,a),this.layout_.getPlotArea().y,Math.abs(a-t),this.layout_.getPlotArea().h)):e==Ae&&i&&r&&(o.fillStyle="rgba(128,128,128,0.33)",o.fillRect(this.layout_.getPlotArea().x,Math.min(r,i),this.layout_.getPlotArea().w,Math.abs(i-r)))};g.prototype.clearZoomRect_=function(){this.currentZoomRectArgs_=null,this.canvas_ctx_.clearRect(0,0,this.width_,this.height_)};g.prototype.doZoomX_=function(e,t){this.currentZoomRectArgs_=null;var a=this.toDataXCoord(e),r=this.toDataXCoord(t);this.doZoomXDates_(a,r)};g.prototype.doZoomXDates_=function(e,t){var a=this.xAxisRange(),r=[e,t];const i=this.getFunctionOption("zoomCallback"),n=this;this.doAnimatedZoom(a,r,null,null,function(){i&&i.call(n,e,t,n.yAxisRanges())})};g.prototype.doZoomY_=function(e,t){this.currentZoomRectArgs_=null;for(var a=this.yAxisRanges(),r=[],i=0;i<this.axes_.length;i++){var n=this.toDataYCoord(e,i),s=this.toDataYCoord(t,i);r.push([s,n])}const l=this.getFunctionOption("zoomCallback"),o=this;this.doAnimatedZoom(null,null,a,r,function(){if(l){const[f,u]=o.xAxisRange();l.call(o,f,u,o.yAxisRanges())}})};g.zoomAnimationFunction=function(e,t){var a=1.5;return(1-Math.pow(a,-e))/(1-Math.pow(a,-t))};g.prototype.resetZoom=function(){const e=this.isZoomed("x"),t=this.isZoomed("y"),a=e||t;if(this.clearSelection(),!a)return;const[r,i]=this.xAxisExtremes(),n=this.getBooleanOption("animatedZooms"),s=this.getFunctionOption("zoomCallback");if(!n){this.dateWindow_=null,this.axes_.forEach(d=>{d.valueRange&&delete d.valueRange}),this.drawGraph_(),s&&s.call(this,r,i,this.yAxisRanges());return}var l=null,o=null,h=null,f=null;e&&(l=this.xAxisRange(),o=[r,i]),t&&(h=this.yAxisRanges(),f=this.yAxisExtremes());const u=this;this.doAnimatedZoom(l,o,h,f,function(){u.dateWindow_=null,u.axes_.forEach(c=>{c.valueRange&&delete c.valueRange}),s&&s.call(u,r,i,u.yAxisRanges())})};g.prototype.doAnimatedZoom=function(e,t,a,r,i){var n=this.getBooleanOption("animatedZooms")?g.ANIMATION_STEPS:1,s=[],l=[],o,h;if(e!==null&&t!==null)for(o=1;o<=n;o++)h=g.zoomAnimationFunction(o,n),s[o-1]=[e[0]*(1-h)+h*t[0],e[1]*(1-h)+h*t[1]];if(a!==null&&r!==null)for(o=1;o<=n;o++){h=g.zoomAnimationFunction(o,n);for(var f=[],u=0;u<this.axes_.length;u++)f.push([a[u][0]*(1-h)+h*r[u][0],a[u][1]*(1-h)+h*r[u][1]]);l[o-1]=f}const d=this;dt(function(c){if(l.length)for(var p=0;p<d.axes_.length;p++){var v=l[c][p];d.axes_[p].valueRange=[v[0],v[1]]}s.length&&(d.dateWindow_=s[c]),d.drawGraph_()},n,g.ANIMATION_DURATION/n,i)};g.prototype.getArea=function(){return this.plotter_.area};g.prototype.eventToDomCoords=function(e){if(e.offsetX&&e.offsetY)return[e.offsetX,e.offsetY];var t=fe(this.mouseEventElement_),a=Ee(e)-t.x,r=Oe(e)-t.y;return[a,r]};g.prototype.findClosestRow=function(e){for(var t=1/0,a=-1,r=this.layout_.points,i=0;i<r.length;i++)for(var n=r[i],s=n.length,l=0;l<s;l++){var o=n[l];if(he(o,!0)){var h=Math.abs(o.canvasx-e);h<t&&(t=h,a=o.idx)}}return a};g.prototype.findClosestPoint=function(e,t){for(var a=1/0,r,i,n,s,l,o,h,f=this.layout_.points.length-1;f>=0;--f)for(var u=this.layout_.points[f],d=0;d<u.length;++d)s=u[d],he(s)&&(i=s.canvasx-e,n=s.canvasy-t,r=i*i+n*n,r<a&&(a=r,l=s,o=f,h=s.idx));var c=this.layout_.setNames[o];return{row:h,seriesName:c,point:l}};g.prototype.findStackedPoint=function(e,t){for(var a=this.findClosestRow(e),r,i,n=0;n<this.layout_.points.length;++n){var s=this.getLeftBoundary_(n),l=a-s,o=this.layout_.points[n];if(!(l>=o.length)){var h=o[l];if(he(h)){var f=h.canvasy;if(e>h.canvasx&&l+1<o.length){var u=o[l+1];if(he(u)){var d=u.canvasx-h.canvasx;if(d>0){var c=(e-h.canvasx)/d;f+=c*(u.canvasy-h.canvasy)}}}else if(e<h.canvasx&&l>0){var p=o[l-1];if(he(p)){var d=h.canvasx-p.canvasx;if(d>0){var c=(h.canvasx-e)/d;f+=c*(p.canvasy-h.canvasy)}}}(n===0||f<t)&&(r=h,i=n)}}}var v=this.layout_.setNames[i];return{row:a,seriesName:v,point:r}};g.prototype.mouseMove_=function(e){var t=this.layout_.points;if(t!=null){var a=this.eventToDomCoords(e),r=a[0],i=a[1],n=this.getOption("highlightSeriesOpts"),s=!1;if(n&&!this.isSeriesLocked()){var l;this.getBooleanOption("stackedGraph")?l=this.findStackedPoint(r,i):l=this.findClosestPoint(r,i),s=this.setSelection(l.row,l.seriesName)}else{var o=this.findClosestRow(r);s=this.setSelection(o)}var h=this.getFunctionOption("highlightCallback");h&&s&&h.call(this,e,this.lastx_,this.selPoints_,this.lastRow_,this.highlightSet_)}};g.prototype.getLeftBoundary_=function(e){if(this.boundaryIds_[e])return this.boundaryIds_[e][0];for(var t=0;t<this.boundaryIds_.length;t++)if(this.boundaryIds_[t]!==void 0)return this.boundaryIds_[t][0];return 0};g.prototype.animateSelection_=function(e){var t=10,a=30;this.fadeLevel===void 0&&(this.fadeLevel=0),this.animateId===void 0&&(this.animateId=0);var r=this.fadeLevel,i=e<0?r:t-r;if(i<=0){this.fadeLevel&&this.updateSelection_(1);return}var n=++this.animateId,s=this,l=function(){s.fadeLevel!==0&&e<0&&(s.fadeLevel=0,s.clearSelection())};dt(function(o){s.animateId==n&&(s.fadeLevel+=e,s.fadeLevel===0?s.clearSelection():s.updateSelection_(s.fadeLevel/t))},i,a,l)};g.prototype.updateSelection_=function(e){this.cascadeEvents_("select",{selectedRow:this.lastRow_===-1?void 0:this.lastRow_,selectedX:this.lastx_===null?void 0:this.lastx_,selectedPoints:this.selPoints_});var t,a=this.canvas_ctx_;if(this.getOption("highlightSeriesOpts")){a.clearRect(0,0,this.width_,this.height_);var r=1-this.getNumericOption("highlightSeriesBackgroundAlpha"),i=Me(this.getOption("highlightSeriesBackgroundColor"));if(r){var n=this.getBooleanOption("animateBackgroundFade");if(n){if(e===void 0){this.animateSelection_(1);return}r*=e}a.fillStyle="rgba("+i.r+","+i.g+","+i.b+","+r+")",a.fillRect(0,0,this.width_,this.height_)}this.plotter_._renderLineChart(this.highlightSet_,a)}else if(this.previousVerticalX_>=0){var s=0,l=this.attr_("labels");for(t=1;t<l.length;t++){var o=this.getNumericOption("highlightCircleSize",l[t]);o>s&&(s=o)}var h=this.previousVerticalX_;a.clearRect(h-s-1,0,2*s+2,this.height_)}if(this.selPoints_.length>0){var f=this.selPoints_[0].canvasx;for(a.save(),t=0;t<this.selPoints_.length;t++){var u=this.selPoints_[t];if(!isNaN(u.canvasy)){var d=this.getNumericOption("highlightCircleSize",u.name),c=this.getFunctionOption("drawHighlightPointCallback",u.name),p=this.plotter_.colors[u.name];c||(c=ze.DEFAULT),a.lineWidth=this.getNumericOption("strokeWidth",u.name),a.strokeStyle=p,a.fillStyle=p,c.call(this,this,u.name,a,f,u.canvasy,p,d,u.idx)}}a.restore(),this.previousVerticalX_=f}};g.prototype.setSelection=function(t,a,r,i){this.selPoints_=[];var n=!1;if(t!==!1&&t>=0){t!=this.lastRow_&&(n=!0),this.lastRow_=t;for(var s=0;s<this.layout_.points.length;++s){var l=this.layout_.points[s],o=t-this.getLeftBoundary_(s);if(o>=0&&o<l.length&&l[o].idx==t){var h=l[o];h.yval!==null&&this.selPoints_.push(h)}else for(var f=0;f<l.length;++f){var h=l[f];if(h.idx==t){h.yval!==null&&this.selPoints_.push(h);break}}}}else this.lastRow_>=0&&(n=!0),this.lastRow_=-1;if(this.selPoints_.length?this.lastx_=this.selPoints_[0].xval:this.lastx_=null,a!==void 0&&(this.highlightSet_!==a&&(n=!0),this.highlightSet_=a),r!==void 0&&(this.lockedSet_=r),n&&(this.updateSelection_(void 0),i)){var u=this.getFunctionOption("highlightCallback");if(u){var d={};u.call(this,d,this.lastx_,this.selPoints_,this.lastRow_,this.highlightSet_)}}return n};g.prototype.mouseOut_=function(e){this.getFunctionOption("unhighlightCallback")&&this.getFunctionOption("unhighlightCallback").call(this,e),this.getBooleanOption("hideOverlayOnMouseOut")&&!this.lockedSet_&&this.clearSelection()};g.prototype.clearSelection=function(){if(this.cascadeEvents_("deselect",{}),this.lockedSet_=!1,this.fadeLevel){this.animateSelection_(-1);return}this.canvas_ctx_.clearRect(0,0,this.width_,this.height_),this.fadeLevel=0,this.selPoints_=[],this.lastx_=null,this.lastRow_=-1,this.highlightSet_=null};g.prototype.getSelection=function(){if(!this.selPoints_||this.selPoints_.length<1)return-1;for(var e=0;e<this.layout_.points.length;e++)for(var t=this.layout_.points[e],a=0;a<t.length;a++)if(t[a].x==this.selPoints_[0].x)return t[a].idx;return-1};g.prototype.getHighlightSeries=function(){return this.highlightSet_};g.prototype.isSeriesLocked=function(){return this.lockedSet_};g.prototype.loadedEvent_=function(e){this.rawData_=this.parseCSV_(e),this.cascadeDataDidUpdateEvent_(),this.predraw_()};g.prototype.addXTicks_=function(){var e;this.dateWindow_?e=[this.dateWindow_[0],this.dateWindow_[1]]:e=this.xAxisExtremes();var t=this.optionsViewForAxis_("x"),a=t("ticker")(e[0],e[1],this.plotter_.area.w,t,this);this.layout_.setXTicks(a)};g.prototype.getHandlerClass_=function(){var e;return this.attr_("dataHandler")?e=this.attr_("dataHandler"):this.fractions_?this.getBooleanOption("errorBars")?e=_e:e=ge:this.getBooleanOption("customBars")?e=pe:this.getBooleanOption("errorBars")?e=ve:e=ae,e};g.prototype.predraw_=function(){var e=new Date;this.dataHandler_=new(this.getHandlerClass_()),this.layout_.computePlotArea(),this.computeYAxes_(),this.is_initial_draw_||(this.canvas_ctx_.restore(),this.hidden_ctx_.restore()),this.canvas_ctx_.save(),this.hidden_ctx_.save(),this.plotter_=new N(this,this.hidden_,this.hidden_ctx_,this.layout_),this.createRollInterface_(),this.cascadeEvents_("predraw"),this.rolledSeries_=[null];for(var t=1;t<this.numColumns();t++){var a=this.dataHandler_.extractSeries(this.rawData_,t,this.attributes_);this.rollPeriod_>1&&(a=this.dataHandler_.rollingAverage(a,this.rollPeriod_,this.attributes_,t)),this.rolledSeries_.push(a)}this.drawGraph_();var r=new Date;this.drawingTimeMs_=r-e};g.PointType=void 0;g.stackPoints_=function(e,t,a,r){for(var i=null,n=null,s=null,l=-1,o=function(p){if(!(l>=p)){for(var v=p;v<e.length;++v)if(s=null,!isNaN(e[v].yval)&&e[v].yval!==null){l=v,s=e[v];break}}},h=0;h<e.length;++h){var f=e[h],u=f.xval;t[u]===void 0&&(t[u]=0);var d=f.yval;isNaN(d)||d===null?r=="none"?d=0:(o(h),n&&s&&r!="none"?d=n.yval+(s.yval-n.yval)*((u-n.xval)/(s.xval-n.xval)):n&&r=="all"?d=n.yval:s&&r=="all"?d=s.yval:d=0):n=f;var c=t[u];i!=u&&(c+=d,t[u]=c),i=u,f.yval_stacked=c,c>a[1]&&(a[1]=c),c<a[0]&&(a[0]=c)}};g.prototype.gatherDatasets_=function(e,t){var a=[],r=[],i=[],n={},s,l,o,h,f,u=e.length-1,d;for(s=u;s>=1;s--)if(this.visibility()[s-1]){if(t){d=e[s];var c=t[0],p=t[1];for(o=null,h=null,l=0;l<d.length;l++)d[l][0]>=c&&o===null&&(o=l),d[l][0]<=p&&(h=l);o===null&&(o=0);for(var v=o,_=!0;_&&v>0;)v--,_=d[v][1]===null;h===null&&(h=d.length-1);var y=h;for(_=!0;_&&y<d.length-1;)y++,_=d[y][1]===null;v!==o&&(o=v),y!==h&&(h=y),a[s-1]=[o,h],d=d.slice(o,h+1)}else d=e[s],a[s-1]=[0,d.length-1];var m=this.attr_("labels")[s],b=this.dataHandler_.getExtremeYValues(d,t,this.getBooleanOption("stepPlot",m)),w=this.dataHandler_.seriesToPoints(d,m,a[s-1][0]);this.getBooleanOption("stackedGraph")&&(f=this.attributes_.axisForSeries(m),i[f]===void 0&&(i[f]=[]),g.stackPoints_(w,i[f],b,this.getBooleanOption("stackedGraphNaNFill"))),n[m]=b,r[s]=w}return{points:r,extremes:n,boundaryIds:a}};g.prototype.drawGraph_=function(){var e=new Date,t=this.is_initial_draw_;this.is_initial_draw_=!1,this.layout_.removeAllDatasets(),this.setColors_(),this.attrs_.pointSize=.5*this.getNumericOption("highlightCircleSize");var a=this.gatherDatasets_(this.rolledSeries_,this.dateWindow_),r=a.points,i=a.extremes;this.boundaryIds_=a.boundaryIds,this.setIndexByName_={};for(var n=this.attr_("labels"),s=0,l=1;l<r.length;l++)this.visibility()[l-1]&&(this.layout_.addDataset(n[l],r[l]),this.datasetIndex_[l]=s++);for(var l=0;l<n.length;l++)this.setIndexByName_[n[l]]=l;if(this.computeYAxisRanges_(i),this.layout_.setYAxes(this.axes_),this.addXTicks_(),this.layout_.evaluate(),this.renderGraph_(t),this.getStringOption("timingName")){var o=new Date;console.log(this.getStringOption("timingName")+" - drawGraph: "+(o-e)+"ms")}};g.prototype.renderGraph_=function(e){this.cascadeEvents_("clearChart"),this.plotter_.clear();const t=this.getFunctionOption("underlayCallback");t&&t.call(this,this.hidden_ctx_,this.layout_.getPlotArea(),this,this);var a={canvas:this.hidden_,drawingContext:this.hidden_ctx_};this.cascadeEvents_("willDrawChart",a),this.plotter_.render(),this.cascadeEvents_("didDrawChart",a),this.lastRow_=-1,this.canvas_.getContext("2d").clearRect(0,0,this.width_,this.height_);const r=this.getFunctionOption("drawCallback");if(r!==null&&r.call(this,this,e),e)for(this.readyFired_=!0;this.readyFns_.length>0;){var i=this.readyFns_.pop();i(this)}};g.prototype.computeYAxes_=function(){var e,t,a;for(this.axes_=[],e=0;e<this.attributes_.numAxes();e++)t={g:this},U(t,this.attributes_.axisOptions(e)),this.axes_[e]=t;for(e=0;e<this.axes_.length;e++)if(e===0)t=this.optionsViewForAxis_("y"+(e?"2":"")),a=t("valueRange"),a&&(this.axes_[e].valueRange=a);else{var r=this.user_attrs_.axes;r&&r.y2&&(a=r.y2.valueRange,a&&(this.axes_[e].valueRange=a))}};g.prototype.numAxes=function(){return this.attributes_.numAxes()};g.prototype.axisPropertiesForSeries=function(e){return this.axes_[this.attributes_.axisForSeries(e)]};g.prototype.computeYAxisRanges_=function(e){for(var t=function(G){return isNaN(parseFloat(G))},a=this.attributes_.numAxes(),r,i,n,s,l,o=0;o<a;o++){var h=this.axes_[o],f=this.attributes_.getForAxis("logscale",o),u=this.attributes_.getForAxis("includeZero",o),d=this.attributes_.getForAxis("independentTicks",o);n=this.attributes_.seriesForAxis(o),r=!0,s=.1;const G=this.getNumericOption("yRangePad");if(G!==null&&(r=!1,s=G/this.plotter_.area.h),n.length===0)h.extremeRange=[0,1];else{for(var c=1/0,p=-1/0,v,_,y=0;y<n.length;y++)e.hasOwnProperty(n[y])&&(v=e[n[y]][0],v!==null&&(c=Math.min(v,c)),_=e[n[y]][1],_!==null&&(p=Math.max(_,p)));u&&!f&&(c>0&&(c=0),p<0&&(p=0)),c==1/0&&(c=0),p==-1/0&&(p=1),i=p-c,i===0&&(p!==0?i=Math.abs(p):(p=1,i=1));var m=p,b=c;r&&(f?(m=p+s*i,b=c):(m=p+s*i,b=c-s*i,b<0&&c>=0&&(b=0),m>0&&p<=0&&(m=0))),h.extremeRange=[b,m]}if(h.valueRange){var w=t(h.valueRange[0])?h.extremeRange[0]:h.valueRange[0],L=t(h.valueRange[1])?h.extremeRange[1]:h.valueRange[1];h.computedValueRange=[w,L]}else h.computedValueRange=h.extremeRange;if(!r){if(w=h.computedValueRange[0],L=h.computedValueRange[1],w===L)if(w===0)L=1;else{var R=Math.abs(w/10);w-=R,L+=R}if(f){var A=s/(2*s-1),x=(s-1)/(2*s-1);h.computedValueRange[0]=be(w,L,A),h.computedValueRange[1]=be(w,L,x)}else i=L-w,h.computedValueRange[0]=w-i*s,h.computedValueRange[1]=L+i*s}if(d){h.independentTicks=d;var D=this.optionsViewForAxis_("y"+(o?"2":"")),S=D("ticker");h.ticks=S(h.computedValueRange[0],h.computedValueRange[1],this.plotter_.area.h,D,this),l||(l=h)}}if(l===void 0)throw'Configuration Error: At least one axis has to have the "independentTicks" option activated.';for(var o=0;o<a;o++){var h=this.axes_[o];if(!h.independentTicks){for(var D=this.optionsViewForAxis_("y"+(o?"2":"")),S=D("ticker"),M=l.ticks,I=l.computedValueRange[1]-l.computedValueRange[0],Z=h.computedValueRange[1]-h.computedValueRange[0],j=[],B=0;B<M.length;B++){var P=(M[B].v-l.computedValueRange[0])/I,re=h.computedValueRange[0]+P*Z;j.push(re)}h.ticks=S(h.computedValueRange[0],h.computedValueRange[1],this.plotter_.area.h,D,this,j)}}};g.prototype.detectTypeFromString_=function(e){var t=!1,a=e.indexOf("-");(a>0&&e[a-1]!="e"&&e[a-1]!="E"||e.indexOf("/")>=0||isNaN(parseFloat(e)))&&(t=!0),this.setXAxisOptions_(t)};g.prototype.setXAxisOptions_=function(e){e?(this.attrs_.xValueParser=lt,this.attrs_.axes.x.valueFormatter=Ne,this.attrs_.axes.x.ticker=ue,this.attrs_.axes.x.axisLabelFormatter=ce):(this.attrs_.xValueParser=function(t){return parseFloat(t)},this.attrs_.axes.x.valueFormatter=function(t){return t},this.attrs_.axes.x.ticker=te,this.attrs_.axes.x.axisLabelFormatter=this.attrs_.axes.x.valueFormatter)};g.prototype.parseCSV_=function(e){var t=[],a=ct(e),r=e.split(a||`
`),i,n,s=this.getStringOption("delimiter");r[0].indexOf(s)==-1&&r[0].indexOf("	")>=0&&(s="	");var l=0;"labels"in this.user_attrs_||(l=1,this.attrs_.labels=r[0].split(s),this.attributes_.reparseSeries());for(var o,h=!1,f=this.attr_("labels").length,u=!1,d=l;d<r.length;d++){var c=r[d];if(c.length!==0&&c[0]!="#"){var p=c.split(s);if(!(p.length<2)){var v=[];if(h||(this.detectTypeFromString_(p[0]),o=this.getFunctionOption("xValueParser"),h=!0),v[0]=o(p[0],this),this.fractions_)for(n=1;n<p.length;n++)i=p[n].split("/"),i.length!=2?(console.error(`Expected fractional "num/den" values in CSV data but found a value '`+p[n]+"' on line "+(1+d)+" ('"+c+"') which is not of this form."),v[n]=[0,0]):v[n]=[q(i[0],d,c),q(i[1],d,c)];else if(this.getBooleanOption("errorBars"))for(p.length%2!=1&&console.error("Expected alternating (value, stdev.) pairs in CSV data but line "+(1+d)+" has an odd number of values ("+(p.length-1)+"): '"+c+"'"),n=1;n<p.length;n+=2)v[(n+1)/2]=[q(p[n],d,c),q(p[n+1],d,c)];else if(this.getBooleanOption("customBars"))for(n=1;n<p.length;n++){var _=p[n];/^ *$/.test(_)?v[n]=[null,null,null]:(i=_.split(";"),i.length==3?v[n]=[q(i[0],d,c),q(i[1],d,c),q(i[2],d,c)]:console.warn('When using customBars, values must be either blank or "low;center;high" tuples (got "'+_+'" on line '+(1+d)+")"))}else for(n=1;n<p.length;n++)v[n]=q(p[n],d,c);if(t.length>0&&v[0]<t[t.length-1][0]&&(u=!0),v.length!=f&&console.error("Number of columns in line "+d+" ("+v.length+") does not agree with number of labels ("+f+") "+c),d===0&&this.attr_("labels")){var y=!0;for(n=0;y&&n<v.length;n++)v[n]&&(y=!1);if(y){console.warn("The dygraphs 'labels' option is set, but the first row of CSV data ('"+c+"') appears to also contain labels. Will drop the CSV labels and use the option labels.");continue}}t.push(v)}}}return u&&(console.warn("CSV is out of order; order it correctly to speed loading."),t.sort(function(m,b){return m[0]-b[0]})),t};function Bt(e){const t=e[0],a=t[0];if(typeof a!="number"&&!ht(a))throw new Error(`Expected number or date but got ${typeof a}: ${a}.`);for(let r=1;r<t.length;r++){const i=t[r];if(i!=null&&typeof i!="number"&&!de(i))throw new Error(`Expected number or array but got ${typeof i}: ${i}.`)}}g.prototype.parseArray_=function(e){if(e.length===0&&(e=[[0]]),e[0].length===0)return console.error("Data set cannot contain an empty row"),null;Bt(e);var t;if(this.attr_("labels")===null){for(console.warn("Using default labels. Set labels explicitly via 'labels' in the options parameter"),this.attrs_.labels=["X"],t=1;t<e[0].length;t++)this.attrs_.labels.push("Y"+t);this.attributes_.reparseSeries()}else{var a=this.attr_("labels");if(a.length!=e[0].length)return console.error("Mismatch between number of labels ("+a+") and number of columns in array ("+e[0].length+")"),null}if(ht(e[0][0])){this.attrs_.axes.x.valueFormatter=Ne,this.attrs_.axes.x.ticker=ue,this.attrs_.axes.x.axisLabelFormatter=ce;var r=ut(e);for(t=0;t<e.length;t++){if(r[t].length===0)return console.error("Row "+(1+t)+" of data is empty"),null;if(r[t][0]===null||typeof r[t][0].getTime!="function"||isNaN(r[t][0].getTime()))return console.error("x value in row "+(1+t)+" is not a Date"),null;r[t][0]=r[t][0].getTime()}return r}else return this.attrs_.axes.x.valueFormatter=function(i){return i},this.attrs_.axes.x.ticker=te,this.attrs_.axes.x.axisLabelFormatter=We,e};g.prototype.parseDataTable_=function(e){var t=function(w){var L=String.fromCharCode(65+w%26);for(w=Math.floor(w/26);w>0;)L=String.fromCharCode(65+(w-1)%26)+L.toLowerCase(),w=Math.floor((w-1)/26);return L},a=e.getNumberOfColumns(),r=e.getNumberOfRows(),i=e.getColumnType(0);if(i=="date"||i=="datetime")this.attrs_.xValueParser=lt,this.attrs_.axes.x.valueFormatter=Ne,this.attrs_.axes.x.ticker=ue,this.attrs_.axes.x.axisLabelFormatter=ce;else if(i=="number")this.attrs_.xValueParser=function(w){return parseFloat(w)},this.attrs_.axes.x.valueFormatter=function(w){return w},this.attrs_.axes.x.ticker=te,this.attrs_.axes.x.axisLabelFormatter=this.attrs_.axes.x.valueFormatter;else throw new Error("only 'date', 'datetime' and 'number' types are supported for column 1 of DataTable input (Got '"+i+"')");var n=[],s={},l=!1,o,h;for(o=1;o<a;o++){var f=e.getColumnType(o);if(f=="number")n.push(o);else if(f=="string"&&this.getBooleanOption("displayAnnotations")){var u=n[n.length-1];s.hasOwnProperty(u)?s[u].push(o):s[u]=[o],l=!0}else throw new Error("Only 'number' is supported as a dependent type with Gviz. 'string' is only supported if displayAnnotations is true")}var d=[e.getColumnLabel(0)];for(o=0;o<n.length;o++)d.push(e.getColumnLabel(n[o])),this.getBooleanOption("errorBars")&&(o+=1);this.attrs_.labels=d,a=d.length;var c=[],p=!1,v=[];for(o=0;o<r;o++){var _=[];if(typeof e.getValue(o,0)>"u"||e.getValue(o,0)===null){console.warn("Ignoring row "+o+" of DataTable because of undefined or null first column.");continue}if(i=="date"||i=="datetime"?_.push(e.getValue(o,0).getTime()):_.push(e.getValue(o,0)),this.getBooleanOption("errorBars"))for(h=0;h<a-1;h++)_.push([e.getValue(o,1+2*h),e.getValue(o,2+2*h)]);else{for(h=0;h<n.length;h++){var y=n[h];if(_.push(e.getValue(o,y)),l&&s.hasOwnProperty(y)&&e.getValue(o,s[y][0])!==null){var m={};m.series=e.getColumnLabel(y),m.xval=_[0],m.shortText=t(v.length),m.text="";for(var b=0;b<s[y].length;b++)b&&(m.text+=`
`),m.text+=e.getValue(o,s[y][b]);v.push(m)}}for(h=0;h<_.length;h++)isFinite(_[h])||(_[h]=null)}c.length>0&&_[0]<c[c.length-1][0]&&(p=!0),c.push(_)}p&&(console.warn("DataTable is out of order; order it correctly to speed loading."),c.sort(function(w,L){return w[0]-L[0]})),this.rawData_=c,v.length>0&&this.setAnnotations(v,!0),this.attributes_.reparseSeries()};g.prototype.cascadeDataDidUpdateEvent_=function(){this.cascadeEvents_("dataDidUpdate",{})};g.prototype.start_=function(){var e=this.file_;typeof e=="function"&&(e=e());const t=Dt(e);if(t=="array")this.rawData_=this.parseArray_(e),this.cascadeDataDidUpdateEvent_(),this.predraw_();else if(t=="object"&&typeof e.getColumnRange=="function")this.parseDataTable_(e),this.cascadeDataDidUpdateEvent_(),this.predraw_();else if(t=="string"){var a=ct(e);if(a)this.loadedEvent_(e);else{var r;window.XMLHttpRequest?r=new XMLHttpRequest:r=new ActiveXObject("Microsoft.XMLHTTP");var i=this;r.onreadystatechange=function(){r.readyState==4&&(r.status===200||r.status===0)&&i.loadedEvent_(r.responseText)},r.open("GET",e,!0),r.send(null)}}else console.error("Unknown data format: "+t)};g.prototype.updateOptions=function(e,t){typeof t>"u"&&(t=!1);var a=e.file,r=g.copyUserAttrs_(e),i=this.attributes_.numAxes();"rollPeriod"in r&&(this.rollPeriod_=r.rollPeriod),"dateWindow"in r&&(this.dateWindow_=r.dateWindow);var n=Ot(this.attr_("labels"),r);Ve(this.user_attrs_,r),this.attributes_.reparseSeries(),i<this.attributes_.numAxes()&&this.plotter_.clear(),a?(this.cascadeEvents_("dataWillUpdate",{}),this.file_=a,t||this.start_()):t||(n?this.predraw_():this.renderGraph_(!1))};g.copyUserAttrs_=function(e){var t={};for(var a in e)e.hasOwnProperty(a)&&a!="file"&&e.hasOwnProperty(a)&&(t[a]=e[a]);return t};g.prototype.resize=function(e,t){if(!this.resize_lock){this.resize_lock=!0,e===null!=(t===null)&&(console.warn("Dygraph.resize() should be called with zero parameters or two non-NULL parameters. Pretending it was zero."),e=t=null);var a=this.width_,r=this.height_;e?(this.maindiv_.style.width=e+"px",this.maindiv_.style.height=t+"px",this.width_=e,this.height_=t):(this.width_=this.maindiv_.clientWidth,this.height_=this.maindiv_.clientHeight),(a!=this.width_||r!=this.height_)&&(this.resizeElements_(),this.predraw_()),this.resize_lock=!1}};g.prototype.adjustRoll=function(e){this.rollPeriod_=e,this.predraw_()};g.prototype.visibility=function(){for(this.getOption("visibility")||(this.attrs_.visibility=[]);this.getOption("visibility").length<this.numColumns()-1;)this.attrs_.visibility.push(!0);return this.getOption("visibility")};g.prototype.setVisibility=function(e,t){var a=this.visibility(),r=!1;if(Array.isArray(e)||(e!==null&&typeof e=="object"?r=!0:e=[e]),r)for(var i in e)e.hasOwnProperty(i)&&(i<0||i>=a.length?console.warn("Invalid series number in setVisibility: "+i):a[i]=e[i]);else for(var i=0;i<e.length;i++)typeof e[i]=="boolean"?i>=a.length?console.warn("Invalid series number in setVisibility: "+i):a[i]=e[i]:e[i]<0||e[i]>=a.length?console.warn("Invalid series number in setVisibility: "+e[i]):a[e[i]]=t;this.predraw_()};g.prototype.size=function(){return{width:this.width_,height:this.height_}};g.prototype.setAnnotations=function(e,t){if(this.annotations_=e,!this.layout_){console.warn("Tried to setAnnotations before dygraph was ready. Try setting them in a ready() block. See dygraphs.com/tests/annotation.html");return}this.layout_.setAnnotations(this.annotations_),t||this.predraw_()};g.prototype.annotations=function(){return this.annotations_};g.prototype.getLabels=function(){var e=this.attr_("labels");return e?e.slice():null};g.prototype.indexFromSetName=function(e){return this.setIndexByName_[e]};g.prototype.getRowForX=function(e){for(var t=0,a=this.numRows()-1;t<=a;){var r=a+t>>1,i=this.getValue(r,0);if(i<e)t=r+1;else if(i>e)a=r-1;else if(t!=r)a=r;else return r}return null};g.prototype.ready=function(e){this.is_initial_draw_?this.readyFns_.push(e):e.call(this,this)};g.prototype.addAndTrackEvent=function(e,t,a){le(e,t,a),this.registeredEvents_.push({elem:e,type:t,fn:a})};g.prototype.removeTrackedEvents_=function(){if(this.registeredEvents_)for(var e=0;e<this.registeredEvents_.length;e++){var t=this.registeredEvents_[e];V(t.elem,t.type,t.fn)}this.registeredEvents_=[]};g.PLUGINS=[W,J,k,K,Q,se];g.GVizChart=Re;g.DASHED_LINE=wt;g.DOT_DASH_LINE=At;g.dateAxisLabelFormatter=ce;g.toRGB_=Me;g.findPos=fe;g.pageX=Ee;g.pageY=Oe;g.dateString_=ot;g.defaultInteractionModel=E.defaultModel;g.nonInteractiveModel=g.nonInteractiveModel_=E.nonInteractiveModel_;g.Circles=ze;g.Plugins={Legend:W,Axes:J,Annotations:Q,ChartLabels:K,Grid:se,RangeSelector:k};g.DataHandlers={DefaultHandler:ae,BarsHandler:z,CustomBarsHandler:pe,DefaultFractionHandler:ge,ErrorBarsHandler:ve,FractionsBarsHandler:_e};g.startPan=E.startPan;g.startZoom=E.startZoom;g.movePan=E.movePan;g.moveZoom=E.moveZoom;g.endPan=E.endPan;g.endZoom=E.endZoom;g.numericLinearTicks=_t;g.numericTicks=te;g.dateTicker=ue;g.Granularity=O;g.getDateAxis=nt;g.floatFormat=st;Ht(g);export{g as D};
