//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// This file grabs the Project specific JS
//
//=============================================================================================
console.log('========== Project.js ==============')

// This is the main entry point into the project specific JS
const ProjectIndex = require('../project/ProjectIndex.js')

// ProjectIndex should be composed of:
// {
// 	ProjectDefinitions,
// 	IoServersArray,
// 	IoServersArrayDev,
// 	RegistersList,
// 	RegistersPersist,
// 	Dashboards,
// 	UnitsConversions,
// 	Trends,
// }

// Decorate UnitsConversions.alternateUnits with scale and offset
function init(index)
{
	const alt = index.UnitsConversions.alternateUnits
	Object.keys(alt).forEach(key => {
		// Calculate scale and offset
		const y1 = alt[key].transform(1)
		const y0 = alt[key].transform(0)
		alt[key].scale = y1 - y0
		alt[key].offset = y0
	})

	return index
}

const Project = Object.assign({}, init(ProjectIndex))

const pd = ProjectIndex.ProjectDefinitions
const projectFolder = pd.projectFolder

// Make sure we have clean definitions with defaults
Project.ProjectDefinitions = {
	datalog: Object.assign({}, {
			enabled: true,
			path: projectFolder + '/datalog',
			intervalFine: 60,
			intervalCoarse: 60 * 5,
		}, pd.datalog),
	httpServer: Object.assign({}, {
			enabled: true,
			port: 3880,
			host: '0.0.0.0',
		}, pd.httpServer),
	projectFolder:        projectFolder,
	systemSettingsFile:   pd.systemSettingsFile   || projectFolder + '/system-settings.json',
	alarmSettingsFile:    pd.alarmSettingsFile    || projectFolder + '/alarm-settings.json',
	controlCurvesFile:    pd.controlCurvesFile    || projectFolder + '/control-curves.json',
	projectConstantsFile: pd.projectConstantsFile || projectFolder + '/project-constants.json',
	schedulesFile:        pd.schedulesFile        || projectFolder + '/schedules.json',
	docsDirectory:        pd.docsDirectory        || projectFolder + '/docs',
	docsIndexFile:        pd.docsIndexFile        || projectFolder + '/docs/documents.json',
}

// console.log('Project.ProjectDefinitions:', Project.ProjectDefinitions)
// console.log('Project:', Project)
// process.exit(0)

module.exports = Project
