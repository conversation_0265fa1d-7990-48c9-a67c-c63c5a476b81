import{e as P,a as j,c as z,f as R}from"./use-key-composition-CoMUTTxZ.js";import{r as C,a as l,h as f,an as D,g as K,ao as L,ap as s,C as N,b as M,a8 as x}from"./index-CzmOWWdj.js";function G(a,v){const e=C(null),c=l(()=>a.disable===!0?null:f("span",{ref:e,class:"no-outline",tabindex:-1}));function g(r){const u=v.value;r?.qAvoidFocus!==!0&&(r?.type.indexOf("key")===0?document.activeElement!==u&&u?.contains(document.activeElement)===!0&&u.focus():e.value!==null&&(r===void 0||u?.contains(r.target)===!0)&&e.value.focus())}return{refocusTargetEl:c,refocusTarget:g}}const H={xs:30,sm:35,md:40,lg:50,xl:60},U={...j,...D,...P,modelValue:{required:!0,default:null},val:{},trueValue:{default:!0},falseValue:{default:!1},indeterminateValue:{default:null},checkedIcon:String,uncheckedIcon:String,indeterminateIcon:String,toggleOrder:{type:String,validator:a=>a==="tf"||a==="ft"},toggleIndeterminate:Boolean,label:String,leftLabel:Boolean,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},W=["update:modelValue"];function X(a,v){const{props:e,slots:c,emit:g,proxy:r}=K(),{$q:u}=r,q=z(e,u),p=C(null),{refocusTargetEl:k,refocusTarget:I}=G(e,p),S=L(e,H),i=l(()=>e.val!==void 0&&Array.isArray(e.modelValue)),m=l(()=>{const t=s(e.val);return i.value===!0?e.modelValue.findIndex(o=>s(o)===t):-1}),n=l(()=>i.value===!0?m.value!==-1:s(e.modelValue)===s(e.trueValue)),d=l(()=>i.value===!0?m.value===-1:s(e.modelValue)===s(e.falseValue)),h=l(()=>n.value===!1&&d.value===!1),$=l(()=>e.disable===!0?-1:e.tabindex||0),y=l(()=>`q-${a} cursor-pointer no-outline row inline no-wrap items-center`+(e.disable===!0?" disabled":"")+(q.value===!0?` q-${a}--dark`:"")+(e.dense===!0?` q-${a}--dense`:"")+(e.leftLabel===!0?" reverse":"")),_=l(()=>{const t=n.value===!0?"truthy":d.value===!0?"falsy":"indet",o=e.color!==void 0&&(e.keepColor===!0||(a==="toggle"?n.value===!0:d.value!==!0))?` text-${e.color}`:"";return`q-${a}__inner relative-position non-selectable q-${a}__inner--${t}${o}`}),O=l(()=>{const t={type:"checkbox"};return e.name!==void 0&&Object.assign(t,{".checked":n.value,"^checked":n.value===!0?"checked":void 0,name:e.name,value:i.value===!0?e.val:e.trueValue}),t}),A=R(O),T=l(()=>{const t={tabindex:$.value,role:a==="toggle"?"switch":"checkbox","aria-label":e.label,"aria-checked":h.value===!0?"mixed":n.value===!0?"true":"false"};return e.disable===!0&&(t["aria-disabled"]="true"),t});function b(t){t!==void 0&&(x(t),I(t)),e.disable!==!0&&g("update:modelValue",w(),t)}function w(){if(i.value===!0){if(n.value===!0){const t=e.modelValue.slice();return t.splice(m.value,1),t}return e.modelValue.concat([e.val])}if(n.value===!0){if(e.toggleOrder!=="ft"||e.toggleIndeterminate===!1)return e.falseValue}else if(d.value===!0){if(e.toggleOrder==="ft"||e.toggleIndeterminate===!1)return e.trueValue}else return e.toggleOrder!=="ft"?e.trueValue:e.falseValue;return e.indeterminateValue}function B(t){(t.keyCode===13||t.keyCode===32)&&x(t)}function E(t){(t.keyCode===13||t.keyCode===32)&&b(t)}const F=v(n,h);return Object.assign(r,{toggle:b}),()=>{const t=F();e.disable!==!0&&A(t,"unshift",` q-${a}__native absolute q-ma-none q-pa-none`);const o=[f("div",{class:_.value,style:S.value,"aria-hidden":"true"},t)];k.value!==null&&o.push(k.value);const V=e.label!==void 0?N(c.default,[e.label]):M(c.default);return V!==void 0&&o.push(f("div",{class:`q-${a}__label q-anchor--skip`},V)),f("div",{ref:p,class:y.value,...T.value,onClick:b,onKeydown:B,onKeyup:E},o)}}export{U as a,W as b,X as c,H as o,G as u};
