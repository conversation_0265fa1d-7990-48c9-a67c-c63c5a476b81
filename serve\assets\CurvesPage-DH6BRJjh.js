import{g as ke,r as w,a as t,a5 as el,a6 as J,o as ll,a7 as al,h as V,k as tl,c as ol,w as te,a8 as nl,a0 as oe,K as ne,T as R,F as S,H as b,X as p,Q as ae,f as Ve,D as E,E as A,U as Q,Y as rl,G as Z,S as D,I as ee,J as le,M as U}from"./index-CzmOWWdj.js";import{Q as sl}from"./QSpace-i2TdLNVM.js";import{Q as M}from"./QInput-CCOJFSf5.js";import{Q as ul}from"./QPage-Disdm55v.js";import{_ as il}from"./PageHeading-CorBRYVa.js";import{a as dl,i as ge}from"./Login-BVtZ5S7B.js";import{r as cl}from"./SaveButton-DQ7yxOUN.js";import{_ as vl}from"./AqArrayForm-C93bdg5l.js";import{_ as ye}from"./AqPersistToggleButton-BcqzKwP0.js";import{e as ml,a as pl,c as fl,f as bl,g as gl}from"./use-key-composition-CoMUTTxZ.js";import{T as yl}from"./TouchPan-C63yVDWw.js";import{b as T}from"./focusout-C-pmmZED.js";import{_ as Me}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{D as xl}from"./dygraph-CO2DREVQ.js";import{d as hl}from"./Debug-gcimLaHD.js";import"./QSeparator-D-fNGoQY.js";import"./QMenu-D3shCIOy.js";import"./use-timeout-DeCFbuIx.js";const xe="q-slider__marker-labels",Cl=a=>({value:a}),kl=({marker:a})=>V("div",{key:a.value,style:a.style,class:a.classes},a.label),_e=[34,37,40,33,39,38],Vl={...pl,...ml,min:{type:Number,default:0},max:{type:Number,default:100},innerMin:Number,innerMax:Number,step:{type:Number,default:1,validator:a=>a>=0},snap:Boolean,vertical:Boolean,reverse:Boolean,color:String,markerLabelsClass:String,label:Boolean,labelColor:String,labelTextColor:String,labelAlways:Boolean,switchLabelSide:Boolean,markers:[Boolean,Number],markerLabels:[Boolean,Array,Object,Function],switchMarkerLabelsSide:Boolean,trackImg:String,trackColor:String,innerTrackImg:String,innerTrackColor:String,selectionColor:String,selectionImg:String,thumbSize:{type:String,default:"20px"},trackSize:{type:String,default:"4px"},disable:Boolean,readonly:Boolean,dense:Boolean,tabindex:[String,Number],thumbColor:String,thumbPath:{type:String,default:"M 4, 10 a 6,6 0 1,0 12,0 a 6,6 0 1,0 -12,0"}},Ml=["pan","update:modelValue","change"];function _l({updateValue:a,updatePosition:i,getDragging:d,formAttrs:r}){const{props:e,emit:h,slots:c,proxy:{$q:n}}=ke(),o=fl(e,n),_=bl(r),s=w(!1),g=w(!1),x=w(!1),k=w(!1),v=t(()=>e.vertical===!0?"--v":"--h"),L=t(()=>"-"+(e.switchLabelSide===!0?"switched":"standard")),q=t(()=>e.vertical===!0?e.reverse===!0:e.reverse!==(n.lang.rtl===!0)),$=t(()=>isNaN(e.innerMin)===!0||e.innerMin<e.min?e.min:e.innerMin),I=t(()=>isNaN(e.innerMax)===!0||e.innerMax>e.max?e.max:e.innerMax),y=t(()=>e.disable!==!0&&e.readonly!==!0&&$.value<I.value),B=t(()=>{if(e.step===0)return u=>u;const l=(String(e.step).trim().split(".")[1]||"").length;return u=>parseFloat(u.toFixed(l))}),N=t(()=>e.step===0?1:e.step),$e=t(()=>y.value===!0?e.tabindex||0:-1),X=t(()=>e.max-e.min),re=t(()=>I.value-$.value),Y=t(()=>O($.value)),j=t(()=>O(I.value)),P=t(()=>e.vertical===!0?q.value===!0?"bottom":"top":q.value===!0?"right":"left"),se=t(()=>e.vertical===!0?"height":"width"),Se=t(()=>e.vertical===!0?"width":"height"),ue=t(()=>e.vertical===!0?"vertical":"horizontal"),we=t(()=>{const l={role:"slider","aria-valuemin":$.value,"aria-valuemax":I.value,"aria-orientation":ue.value,"data-step":e.step};return e.disable===!0?l["aria-disabled"]="true":e.readonly===!0&&(l["aria-readonly"]="true"),l}),Ue=t(()=>`q-slider q-slider${v.value} q-slider--${s.value===!0?"":"in"}active inline no-wrap `+(e.vertical===!0?"row":"column")+(e.disable===!0?" disabled":" q-slider--enabled"+(y.value===!0?" q-slider--editable":""))+(x.value==="both"?" q-slider--focus":"")+(e.label||e.labelAlways===!0?" q-slider--label":"")+(e.labelAlways===!0?" q-slider--label-always":"")+(o.value===!0?" q-slider--dark":"")+(e.dense===!0?" q-slider--dense q-slider--dense"+v.value:""));function z(l){const u="q-slider__"+l;return`${u} ${u}${v.value} ${u}${v.value}${L.value}`}function ie(l){const u="q-slider__"+l;return`${u} ${u}${v.value}`}const qe=t(()=>{const l=e.selectionColor||e.color;return"q-slider__selection absolute"+(l!==void 0?` text-${l}`:"")}),Ie=t(()=>ie("markers")+" absolute overflow-hidden"),Le=t(()=>ie("track-container")),Be=t(()=>z("pin")),Fe=t(()=>z("label")),Re=t(()=>z("text-container")),Ne=t(()=>z("marker-labels-container")+(e.markerLabelsClass!==void 0?` ${e.markerLabelsClass}`:"")),Te=t(()=>"q-slider__track relative-position no-outline"+(e.trackColor!==void 0?` bg-${e.trackColor}`:"")),De=t(()=>{const l={[Se.value]:e.trackSize};return e.trackImg!==void 0&&(l.backgroundImage=`url(${e.trackImg}) !important`),l}),Ae=t(()=>"q-slider__inner absolute"+(e.innerTrackColor!==void 0?` bg-${e.innerTrackColor}`:"")),de=t(()=>{const l=j.value-Y.value,u={[P.value]:`${100*Y.value}%`,[se.value]:l===0?"2px":`${100*l}%`};return e.innerTrackImg!==void 0&&(u.backgroundImage=`url(${e.innerTrackImg}) !important`),u});function Xe(l){const{min:u,max:m,step:f}=e;let C=u+l*(m-u);if(f>0){const F=(C-$.value)%f;C+=(Math.abs(F)>=f/2?(F<0?-1:1)*f:0)-F}return C=B.value(C),T(C,$.value,I.value)}function O(l){return X.value===0?0:(l-e.min)/X.value}function Ye(l,u){const m=al(l),f=e.vertical===!0?T((m.top-u.top)/u.height,0,1):T((m.left-u.left)/u.width,0,1);return T(q.value===!0?1-f:f,Y.value,j.value)}const ce=t(()=>el(e.markers)===!0?e.markers:N.value),ve=t(()=>{const l=[],u=ce.value,m=e.max;let f=e.min;do l.push(f),f+=u;while(f<m);return l.push(m),l}),me=t(()=>{const l=` ${xe}${v.value}-`;return xe+`${l}${e.switchMarkerLabelsSide===!0?"switched":"standard"}${l}${q.value===!0?"rtl":"ltr"}`}),K=t(()=>e.markerLabels===!1?null:ze(e.markerLabels).map((l,u)=>({index:u,value:l.value,label:l.label||l.value,classes:me.value+(l.classes!==void 0?" "+l.classes:""),style:{...fe(l.value),...l.style||{}}}))),pe=t(()=>({markerList:K.value,markerMap:Ee.value,classes:me.value,getStyle:fe})),Pe=t(()=>{const l=re.value===0?"2px":100*ce.value/re.value;return{...de.value,backgroundSize:e.vertical===!0?`2px ${l}%`:`${l}% 2px`}});function ze(l){if(l===!1)return null;if(l===!0)return ve.value.map(Cl);if(typeof l=="function")return ve.value.map(m=>{const f=l(m);return J(f)===!0?{...f,value:m}:{value:m,label:f}});const u=({value:m})=>m>=e.min&&m<=e.max;return Array.isArray(l)===!0?l.map(m=>J(m)===!0?m:{value:m}).filter(u):Object.keys(l).map(m=>{const f=l[m],C=Number(m);return J(f)===!0?{...f,value:C}:{value:C,label:f}}).filter(u)}function fe(l){return{[P.value]:`${100*(l-e.min)/X.value}%`}}const Ee=t(()=>{if(e.markerLabels===!1)return null;const l={};return K.value.forEach(u=>{l[u.value]=u}),l});function Qe(){if(c["marker-label-group"]!==void 0)return c["marker-label-group"](pe.value);const l=c["marker-label"]||kl;return K.value.map(u=>l({marker:u,...pe.value}))}const je=t(()=>[[yl,Oe,void 0,{[ue.value]:!0,prevent:!0,stop:!0,mouse:!0,mouseAllDir:!0}]]);function Oe(l){l.isFinal===!0?(k.value!==void 0&&(i(l.evt),l.touch===!0&&a(!0),k.value=void 0,h("pan","end")),s.value=!1,x.value=!1):l.isFirst===!0?(k.value=d(l.evt),i(l.evt),a(),s.value=!0,h("pan","start")):(i(l.evt),a())}function be(){x.value=!1}function Ke(l){i(l,d(l)),a(),g.value=!0,s.value=!0,document.addEventListener("mouseup",G,!0)}function G(){g.value=!1,s.value=!1,a(!0),be(),document.removeEventListener("mouseup",G,!0)}function Ge(l){i(l,d(l)),a(!0)}function He(l){_e.includes(l.keyCode)&&a(!0)}function We(l){if(e.vertical===!0)return null;const u=n.lang.rtl!==e.reverse?1-l:l;return{transform:`translateX(calc(${2*u-1} * ${e.thumbSize} / 2 + ${50-100*u}%))`}}function Je(l){const u=t(()=>g.value===!1&&(x.value===l.focusValue||x.value==="both")?" q-slider--focus":""),m=t(()=>`q-slider__thumb q-slider__thumb${v.value} q-slider__thumb${v.value}-${q.value===!0?"rtl":"ltr"} absolute non-selectable`+u.value+(l.thumbColor.value!==void 0?` text-${l.thumbColor.value}`:"")),f=t(()=>({width:e.thumbSize,height:e.thumbSize,[P.value]:`${100*l.ratio.value}%`,zIndex:x.value===l.focusValue?2:void 0})),C=t(()=>l.labelColor.value!==void 0?` text-${l.labelColor.value}`:""),F=t(()=>We(l.ratio.value)),H=t(()=>"q-slider__text"+(l.labelTextColor.value!==void 0?` text-${l.labelTextColor.value}`:""));return()=>{const W=[V("svg",{class:"q-slider__thumb-shape absolute-full",viewBox:"0 0 20 20","aria-hidden":"true"},[V("path",{d:e.thumbPath})]),V("div",{class:"q-slider__focus-ring fit"})];return(e.label===!0||e.labelAlways===!0)&&(W.push(V("div",{class:Be.value+" absolute fit no-pointer-events"+C.value},[V("div",{class:Fe.value,style:{minWidth:e.thumbSize}},[V("div",{class:Re.value,style:F.value},[V("span",{class:H.value},l.label.value)])])])),e.name!==void 0&&e.disable!==!0&&_(W,"push")),V("div",{class:m.value,style:f.value,...l.getNodeData()},W)}}function Ze(l,u,m,f){const C=[];e.innerTrackColor!=="transparent"&&C.push(V("div",{key:"inner",class:Ae.value,style:de.value})),e.selectionColor!=="transparent"&&C.push(V("div",{key:"selection",class:qe.value,style:l.value})),e.markers!==!1&&C.push(V("div",{key:"marker",class:Ie.value,style:Pe.value})),f(C);const F=[tl("div",{key:"trackC",class:Le.value,tabindex:u.value,...m.value},[V("div",{class:Te.value,style:De.value},C)],"slide",y.value,()=>je.value)];if(e.markerLabels!==!1){const H=e.switchMarkerLabelsSide===!0?"unshift":"push";F[H](V("div",{key:"markerL",class:Ne.value},Qe()))}return F}return ll(()=>{document.removeEventListener("mouseup",G,!0)}),{state:{active:s,focus:x,preventFocus:g,dragging:k,editable:y,classes:Ue,tabindex:$e,attributes:we,roundValueFn:B,keyStep:N,trackLen:X,innerMin:$,innerMinRatio:Y,innerMax:I,innerMaxRatio:j,positionProp:P,sizeProp:se,isReversed:q},methods:{onActivate:Ke,onMobileClick:Ge,onBlur:be,onKeyup:He,getContent:Ze,getThumbRenderFn:Je,convertRatioToModel:Xe,convertModelToRatio:O,getDraggingRatio:Ye}}}const $l=()=>({}),he=ol({name:"QSlider",props:{...Vl,modelValue:{required:!0,default:null,validator:a=>typeof a=="number"||a===null},labelValue:[String,Number]},emits:Ml,setup(a,{emit:i}){const{proxy:{$q:d}}=ke(),{state:r,methods:e}=_l({updateValue:v,updatePosition:q,getDragging:L,formAttrs:gl(a)}),h=w(null),c=w(0),n=w(0);function o(){n.value=a.modelValue===null?r.innerMin.value:T(a.modelValue,r.innerMin.value,r.innerMax.value)}te(()=>`${a.modelValue}|${r.innerMin.value}|${r.innerMax.value}`,o),o();const _=t(()=>e.convertModelToRatio(n.value)),s=t(()=>r.active.value===!0?c.value:_.value),g=t(()=>{const y={[r.positionProp.value]:`${100*r.innerMinRatio.value}%`,[r.sizeProp.value]:`${100*(s.value-r.innerMinRatio.value)}%`};return a.selectionImg!==void 0&&(y.backgroundImage=`url(${a.selectionImg}) !important`),y}),x=e.getThumbRenderFn({focusValue:!0,getNodeData:$l,ratio:s,label:t(()=>a.labelValue!==void 0?a.labelValue:n.value),thumbColor:t(()=>a.thumbColor||a.color),labelColor:t(()=>a.labelColor),labelTextColor:t(()=>a.labelTextColor)}),k=t(()=>r.editable.value!==!0?{}:d.platform.is.mobile===!0?{onClick:e.onMobileClick}:{onMousedown:e.onActivate,onFocus:$,onBlur:e.onBlur,onKeydown:I,onKeyup:e.onKeyup});function v(y){n.value!==a.modelValue&&i("update:modelValue",n.value),y===!0&&i("change",n.value)}function L(){return h.value.getBoundingClientRect()}function q(y,B=r.dragging.value){const N=e.getDraggingRatio(y,B);n.value=e.convertRatioToModel(N),c.value=a.snap!==!0||a.step===0?N:e.convertModelToRatio(n.value)}function $(){r.focus.value=!0}function I(y){if(_e.includes(y.keyCode)===!1)return;nl(y);const B=([34,33].includes(y.keyCode)?10:1)*r.keyStep.value,N=([34,37,40].includes(y.keyCode)?-1:1)*(r.isReversed.value===!0?-1:1)*(a.vertical===!0?-1:1)*B;n.value=T(r.roundValueFn.value(n.value+N),r.innerMin.value,r.innerMax.value),v()}return()=>{const y=e.getContent(g,r.tabindex,k,B=>{B.push(x())});return V("div",{ref:h,class:r.classes.value+(a.modelValue===null?" q-slider--no-value":""),...r.attributes.value,"aria-valuenow":a.modelValue},y)}}}),Sl={class:"range"},wl={class:"flex"},Ul={class:"slider-wrapper"},ql={class:"slider-wrapper"},Il={class:"range-bottom"},Ll={__name:"CurveRange",props:oe({controlCurve:{type:Object,required:!0},onDestroy:{type:Function,required:!0},onUpdate:{type:Function,required:!0}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(a){const i=ne(a,"modelValue"),d=a,r=c=>{const n=h(Number(c),d.controlCurve.xMin,d.controlCurve.xMax);i.value[0]=n,d.onUpdate()},e=c=>{const n=h(Number(c),d.controlCurve.yMin,d.controlCurve.yMax);i.value[1]=n,d.onUpdate()},h=(c,n,o)=>Math.min(Math.max(c,n),o);return(c,n)=>(S(),R("div",Sl,[b(ae,{onClick:n[0]||(n[0]=()=>a.onDestroy(i.value)),color:"primary",class:"cursor-pointer",size:"20px",name:"close"}),p("div",wl,[p("div",Ul,[n[3]||(n[3]=p("p",{style:{all:"unset"}},"X",-1)),b(he,{modelValue:i.value[0],"onUpdate:modelValue":n[1]||(n[1]=o=>i.value[0]=o),modelModifiers:{number:!0},onChange:a.onUpdate,step:a.controlCurve.xStep,min:a.controlCurve.xMin,max:a.controlCurve.xMax,vertical:"",reverse:"",label:"","switch-label-side":"",color:"primary",class:"slider"},null,8,["modelValue","onChange","step","min","max"])]),p("div",ql,[n[4]||(n[4]=p("p",{style:{all:"unset"}},"Y",-1)),b(he,{modelValue:i.value[1],"onUpdate:modelValue":n[2]||(n[2]=o=>i.value[1]=o),modelModifiers:{number:!0},onChange:a.onUpdate,min:a.controlCurve.yMin,max:a.controlCurve.yMax,vertical:"",reverse:"",label:"","switch-label-side":"",color:"primary",class:"slider"},null,8,["modelValue","onChange","min","max"])])]),p("div",Il,[b(M,{type:"number","input-class":"text-center q-no-input-spinner",label:"X",dense:"","model-value":i.value[0],"onUpdate:modelValue":r,debounce:"500"},null,8,["model-value"]),b(M,{type:"number","input-class":"text-center q-no-input-spinner",label:"Y",dense:"","model-value":i.value[1],"onUpdate:modelValue":e,debounce:"500"},null,8,["model-value"])])]))}},Bl=Me(Ll,[["__scopeId","data-v-3a842278"]]),Fl={class:"parent-chart relative-position"},Rl={__name:"DyGraphControlCurves",props:oe({curveInfo:{type:Object,required:!0},dense:{type:Boolean}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(a){const i=w(null),d=w(null),r=a,e={yMargin:.1},h=ne(a,"modelValue");function c(s,g){const x=g.length;let k=g[0][1];if(s>g[0][0]){k=g[x-1][1];for(let v=1;v<x;v++){let L=g[v][0];if(s<=L){let q=g[v][1],$=g[v-1][0],I=g[v-1][1],y=(s-$)/(L-$);k=q*y+I*(1-y);break}}}return k}function n(){const{xMin:s,xMax:g,xStep:x}=r.curveInfo,k=[];for(let v=s;v<=g;v+=x){const L=c(v,h.value);k.push([v,L])}return k}const o=s=>{const g=s.x,x=s.series[0]?.y;return`
    <div style="white-space: normal;">
      <div>X: ${Number(g).toFixed(2)} ${r.curveInfo.unitsX}</div>
      <div>Y: ${Number(x).toFixed(2)} ${r.curveInfo.unitsY}</div>
    </div>
  `},_=()=>{if(!i.value)return;const s=n();d.value=new xl(i.value,s,{labels:["Date","Y"],valueRange:[r.curveInfo.yMin-r.curveInfo.yMin*e.yMargin,r.curveInfo.yMax+r.curveInfo.yMax*e.yMargin],showLabelsOnHighlight:!0,highlightCircleSize:5,legend:"follow",strokeWidth:2,animatedZooms:!0,ylabel:`${r.curveInfo.labelY} (${r.curveInfo.unitsY})`,xlabel:`${r.curveInfo.labelX} (${r.curveInfo.unitsX})`,legendFormatter:o,pointClickCallback:function(g,x){if(r.dense)return;const{xval:k,yval:v}=x;h.value.push([Number(k.toFixed(2)),Number(v.toFixed(2))])}})};return te(()=>h.value,()=>{_()},{deep:!0,immediate:!0}),Ve(()=>{_(),window.addEventListener("resize",()=>{d.value&&d.value.resize()})}),(s,g)=>(S(),R("div",Fl,[p("div",{id:"div_g_curves",ref_key:"graphDivRef",ref:i,class:"chart-container"},null,512)]))}},Nl={key:0,class:"custom-scroll range-wrapper"},Tl={__name:"ControlCurves",props:oe({curveInfo:{type:Object,required:!0},dense:{type:Boolean,default:!1}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(a){const i=w(0),d=ne(a,"modelValue"),r=a,e=c=>{d.value.length>2&&(d.value=d.value.filter(o=>o[0]!==c[0]))},h=()=>{d.value.sort((c,n)=>c[0]-n[0]),i.value++};return te(()=>[d.value.length,r.dense],()=>{h()},{deep:!0}),(c,n)=>(S(),R(Q,null,[(S(),E(Rl,{modelValue:d.value,"onUpdate:modelValue":n[0]||(n[0]=o=>d.value=o),"curve-info":a.curveInfo,style:{height:"320px"},key:i.value,dense:a.dense},null,8,["modelValue","curve-info","dense"])),a.dense?A("",!0):(S(),R("div",Nl,[(S(!0),R(Q,null,rl(d.value,(o,_)=>(S(),E(Bl,{key:`${i.value}-${_}`,modelValue:d.value[_],"onUpdate:modelValue":s=>d.value[_]=s,"control-curve":a.curveInfo,onUpdate:h,onDestroy:e},null,8,["modelValue","onUpdate:modelValue","control-curve"]))),128))]))],64))}},Dl=Me(Tl,[["__scopeId","data-v-0b5201ff"]]),Al={class:"a-container-lg q-mb-xl"},Xl={class:"q-gutter-sm"},Yl={class:"col-12 q-px-xs"},Pl={class:"row bg-primary text-white rounded-borders q-pa-sm"},zl={class:"text-h3"},El={class:"text-h5 q-pt-sm"},Ql={class:"col-2"},jl={class:"col-4"},Ol={class:"col-3"},Kl={class:"col-3"},Gl={class:"col-2"},Hl={class:"col-2"},Wl={class:"col"},Jl={class:"col"},Zl={class:"col"},ea={class:"col"},la={class:"col"},aa={class:"col-12"},ta={key:0,class:"text-h6 q-ma-sm text-black fixed-bottom-right q-pa-md bg-warning rounded-borders",style:{opacity:"0.7"}},Ce="/setup/curves",ka={__name:"CurvesPage",setup(a){const i=new cl({name:"example",description:"Example Control Curve",labelX:"Geothermal Fluid Temperature",labelY:"Flow Rate",unitsX:"°C",unitsY:"US GPM",xMin:0,xMax:30,xStep:.5,yMin:0,yMax:1500,data:[[4,1500],[9,450],[15,450],[27,1e3]]}),d=w(!0),r=w(!1),e=t(()=>({dense:d.value,outlined:r.value,readonly:!r.value,standout:!r.value}));Ve(()=>{console.log("CurvesPage mounted"),dl.get(Ce).then(c=>{console.log("CurvesPage api.get",c.data),i.writeValue(c.data)})});function h(){i.post(Ce).then(c=>{console.log("postCurves",c)})}return(c,n)=>(S(),E(ul,null,{default:Z(()=>[p("div",Al,[b(il,{title:"Control Curves",icon:"show_chart"},{default:Z(()=>[p("div",Xl,[D(ge)?(S(),E(ye,{key:0,round:"",flat:"",modelValue:r.value,"onUpdate:modelValue":n[0]||(n[0]=o=>r.value=o),"local-store-key":"curvesEdit","default-value":!1,"color-true":"primary","color-false":"grey-5","icon-true":"edit","icon-false":"edit_off","tooltip-true":"Click to disable editing","tooltip-false":"Click to enable editing"},null,8,["modelValue"])):A("",!0),b(ye,{round:"",flat:"",modelValue:d.value,"onUpdate:modelValue":n[1]||(n[1]=o=>d.value=o),"local-store-key":"curvesShowDense","default-value":!0,"color-true":"grey-5","color-false":"grey-5","icon-true":"compress","icon-false":"expand","tooltip-true":"Click to expand the form spacing","tooltip-false":"Click to make the form compact"},null,8,["modelValue"])])]),_:1}),b(vl,{"reactive-array":D(i),onReset:n[2]||(n[2]=o=>D(i).reset()),onSubmit:n[3]||(n[3]=o=>h()),enable:r.value,class:"q-pt-sm"},{default:Z(({item:o,index:_})=>[p("div",Yl,[p("div",Pl,[p("div",zl,[b(ae,{name:"show_chart"}),ee(" "+le(o.name),1)]),b(sl),p("div",El,le(o.description),1)])]),e.value.readonly?A("",!0):(S(),R(Q,{key:0},[p("div",Ql,[b(M,U({modelValue:o.name,"onUpdate:modelValue":s=>o.name=s,label:"["+_+"] Name"},e.value),null,16,["modelValue","onUpdate:modelValue","label"])]),p("div",jl,[b(M,U({modelValue:o.description,"onUpdate:modelValue":s=>o.description=s,label:"Description"},e.value),null,16,["modelValue","onUpdate:modelValue"])]),p("div",Ol,[b(M,U({modelValue:o.labelX,"onUpdate:modelValue":s=>o.labelX=s,label:"X Axis Label"},e.value),null,16,["modelValue","onUpdate:modelValue"])]),p("div",Kl,[b(M,U({modelValue:o.labelY,"onUpdate:modelValue":s=>o.labelY=s,label:"Y Axis Label"},e.value),null,16,["modelValue","onUpdate:modelValue"])]),p("div",Gl,[b(M,U({modelValue:o.unitsX,"onUpdate:modelValue":s=>o.unitsX=s,label:"X Axis Units"},e.value),null,16,["modelValue","onUpdate:modelValue"])]),p("div",Hl,[b(M,U({modelValue:o.unitsY,"onUpdate:modelValue":s=>o.unitsY=s,label:"Y Axis Units"},e.value),null,16,["modelValue","onUpdate:modelValue"])]),p("div",Wl,[b(M,U({modelValue:o.xMin,"onUpdate:modelValue":s=>o.xMin=s,modelModifiers:{number:!0},label:"X Min"},e.value,{type:"number"}),null,16,["modelValue","onUpdate:modelValue"])]),p("div",Jl,[b(M,U({modelValue:o.xMax,"onUpdate:modelValue":s=>o.xMax=s,modelModifiers:{number:!0},label:"X Max"},e.value,{type:"number"}),null,16,["modelValue","onUpdate:modelValue"])]),p("div",Zl,[b(M,U({modelValue:o.xStep,"onUpdate:modelValue":s=>o.xStep=s,modelModifiers:{number:!0},label:"X Step"},e.value,{type:"number"}),null,16,["modelValue","onUpdate:modelValue"])]),p("div",ea,[b(M,U({modelValue:o.yMin,"onUpdate:modelValue":s=>o.yMin=s,modelModifiers:{number:!0},label:"Y Min"},e.value,{type:"number"}),null,16,["modelValue","onUpdate:modelValue"])]),p("div",la,[b(M,U({modelValue:o.yMax,"onUpdate:modelValue":s=>o.yMax=s,modelModifiers:{number:!0},label:"Y Max"},e.value,{type:"number"}),null,16,["modelValue","onUpdate:modelValue"])])],64)),p("div",aa,[b(Dl,{modelValue:o.data,"onUpdate:modelValue":s=>o.data=s,"curve-info":o,dense:e.value.readonly},null,8,["modelValue","onUpdate:modelValue","curve-info","dense"]),D(hl)?(S(),R(Q,{key:0},[ee("DEBUG: "+le(o.data),1)],64)):A("",!0)])]),_:1},8,["reactive-array","enable"]),D(ge)?A("",!0):(S(),R("div",ta,[b(ae,{name:"show_chart"}),n[4]||(n[4]=ee(" You must be logged in as an administrator to edit the Control Curves "))]))])]),_:1}))}};export{ka as default};
