const fs = require('fs')
const { postMetadata, postDataBatch } = require('./lib/post_functions.js')
const { error } = require('console')
const { processHistoricalData } = require('./lib/csv_helper.js')
const { sseBroadcast } = require('../routes/sse.js');

/**
 * @typedef {string} CurrentState
 * values
    IDLE 
    METADATA_START
    METADATA_IN_PROGRESS
    CSV_START
    CSV_IN_PROGRESS
    LIVE_STREAM_START
    LIVE_STREAM_IN_PROGRESS
    ERROR 
    SHUTDOWN_START
    SHUTDOWN_PROGRESS
 */
/** @type {CurrentState | null} */
let currentState = null
let stateChangeCounter = 0

let currentPromise = null
let MAX_LIVE_DATA_QUEUE_SIZE = 1000

let latestTimestamp = null // acts as t1 
let dataTime = null // latest device timestamp recieved by server, acts as t0

/**
 * @type {Array} liveDataQueue
 */
const liveDataQueue = []

// configuration - passed from embedded system
let config = {
    serverUrl: null,
    batchSize: 100,
    projectFinePath: null,
    projectMetadataJsonPath: null,
    datalogResolution: "fine",
    postFormat: "array",
    enable: false
};

const controller = new AbortController()
const { signal } = controller

/**
 * Function to set state and increment counter on state change 
 * @param {CurrentState} newState - state to replace currentState with
 */
function setState(newState) {
    if (currentState !== newState) {
        const previousState = currentState;
        currentState = newState;
        stateChangeCounter++;
        console.log(`[STATE CHANGE] ${previousState} -> ${newState} (counter: ${stateChangeCounter})`);
        try {
            const status = getStatus();
            sseBroadcast(status, 'cloud');
        } catch (error) {
            console.log(`SSE not available: ${error.message}`);
        }
    }
}

/**
 * Initializes the data poster module.
 * Sets up configuration, loads metadata, and prepares the state machine.
 *
 * @param {object} projectSettings - Configuration settings passed from the embedded system.
 * @param {string} projectSettings.fine - Path to the directory containing CSV files.
 * @param {string} projectSettings.metadata - Path to the metadata JSON file.
 * @param {string} projectSettings.serverUrl - URL of the data server.
 */
function init(projectSettings) {     // callback to sse passed as arg 
    console.log('[INITT] Starting initialization...')

    config.enable = !!projectSettings.enable
    config.serverUrl = projectSettings.serverUrl
    config.projectFinePath = projectSettings.fine
    config.projectMetadataJsonPath = projectSettings.metadata

    // console.log(`[DEBUG] Fine path set to: ${config.projectFinePath}`)  // Debug log
    // console.log(`[DEBUG] Metadata path set to: ${config.projectMetadataJsonPath}`)  // Debug log

    loadMetadata(config.projectMetadataJsonPath)

    try {
        if (metadata) {
            console.log('[INIT] Device metadata loaded successfully')
        } else {
            console.log('[INIT] Metadata empty...')
        }
    } catch (error) {
        console.error('[INITT] Device metadata not loaded', error)
        setState("ERROR")
        return
    }

    liveDataQueue.length = 0
    setState("IDLE")

    // console.log(`[INITT] Initial state set to: ${currentState}`)
    // console.log(`[INITT] Initial state: ${currentState}, Enabled: ${config.enable}, URL: ${config.serverUrl || 'not set'}`);
    console.log('[INITT] Initialization complete')
}

let resetAfterOperation = false;
/**
 * Main service loop for the data poster state machine.
 * @param {object} runtimeConfig - The latest configuration from the application.
 * @param {boolean} runtimeConfig.enable - The current enable status.
 * @param {string} runtimeConfig.serverUrl - The current server URL.
 * @param {object} data - New data to be processed.
 */
function svc(runtimeConfig, data) {

    const previousUrl = config.serverUrl;

    // Update config
    config.enable = !!runtimeConfig.enable;
    config.serverUrl = runtimeConfig.serverUrl;

    // config check to reset state
    const urlChanged = previousUrl !== config.serverUrl;
    if (!config.enable || urlChanged) {
        if (!currentPromise) {
            setState('IDLE');
            // console.log(`[SVC] Module disabled or URL changed, state set to: ${currentState}`);
            return;
        } else {
            resetAfterOperation = true;
            // console.log(`[SVC] Module disabled or URL changed, will reset after current operation`);
        }
    }

    // Add incoming data to the queue
    if (data) {
        liveDataQueue.push(data)
        latestTimestamp = data[0]
        if (liveDataQueue.length > MAX_LIVE_DATA_QUEUE_SIZE) {
            console.warn(`[SVC] Live data queue overflow! Size: ${liveDataQueue.length}`)
            liveDataQueue.length = 0
            setState("IDLE")
            return
        }
    }

    switch (currentState) {
        case "IDLE":
            if (config.enable && config.serverUrl) {
                setState("METADATA_START")
            }
        // break;

        case "METADATA_START":
            const operationUrl = config.serverUrl;
            console.log("[SVC] Initiating metadata send")
            setState("METADATA_IN_PROGRESS")
            currentPromise = postMetadata(config.serverUrl, metadata, signal)

                .then((data) => data.response.text())

                .then((data) => {
                    console.log("[SVC] Metadata successfully sent")
                    dataTime = Number(data.split(',')[1])
                    // console.log(`[SVC] Server response dataTime: ${dataTime}`)

                    if (resetAfterOperation) {
                        setState('IDLE');
                        // console.log(`[SVC] URL changed or module disabled after metadata send, state set to: ${currentState}`);
                        resetAfterOperation = false;
                        return;
                    }

                    setState("CSV_IN_PROGRESS")
                    console.log(`[SVC] State: ${currentState}. Starting historical data processing...`)

                    return processHistoricalData(dataTime, latestTimestamp, {
                        ...config,
                        mac: metadata.mac,
                        signal: signal
                    })
                        .then(() => {
                            if (resetAfterOperation) {
                                setState('IDLE');
                                // console.log(`[SVC] Module disabled after CSV processing, state set to: ${currentState}`);
                                resetAfterOperation = false;
                                return;
                            } else {
                                console.log(`[SVC] CSV data processed successfully`);
                                setState("LIVE_STREAM_START");
                                // console.log(`[SVC] State changed to: ${currentState}`);
                            }
                        });
                })

                .catch((error) => {
                    console.error(`[SVC] Error : ${error.message}`)
                    setState("ERROR")
                })
                .finally(() => {
                    currentPromise = null;
                });
            break

        case "METADATA_IN_PROGRESS":
            break

        case "CSV_START":
            break

        case "CSV_IN_PROGRESS":
            console.log("[SVC] CSV processing ongoing, awaiting completion");
            break

        case "LIVE_STREAM_START":
            if (resetAfterOperation) {
                setState('IDLE')
                // console.log(`[SVC] Module disabled, state changed to: ${currentState}, in LIVE_STREAM_START`)
                resetAfterOperation = false;
                return
            }

            if (liveDataQueue.length > 0) {
                const operationUrl = config.serverUrl;
                setState("LIVE_STREAM_IN_PROGRESS")
                // console.log(`[SVC] State: ${currentState}. Queue size: ${liveDataQueue.length}, in LIVE_STREAM_START`)

                const dataToStream = [...liveDataQueue] // pushes all data from livedataqueue, so accumulated points flushed on first call
                currentPromise = postDataBatch(dataToStream, config.serverUrl, metadata.mac, signal) // dataToStream pushed to server 

                    .then(() => {

                        liveDataQueue.splice(0, dataToStream.length)
                        if (resetAfterOperation) {
                            setState('IDLE');
                            // console.log(`[SVC] Module disabled after streaming, state set to: ${currentState}`);
                            resetAfterOperation = false;
                            return;
                        } else {
                            setState("LIVE_STREAM_START");
                            // console.log(`[SVC] Live stream batch sent, state set to: ${currentState}`);
                        }
                    })
                    .catch(() => {
                        console.error(`[SVC] Error streaming live data: ${error.message}`)
                        setState("ERROR")
                    })
                    .finally(() => {
                        currentPromise = null;
                    });
            }
            break

        case "LIVE_STREAM_IN_PROGRESS":
            break


        case "ERROR":
            console.error("[SVC] State: ERROR")
            setState("IDLE")
            break

        case "SHUTTING_DOWN":
            console.log("[SVC] State: SHUTTING_DOWN")
            break

        default:
            console.warn(`[SVC] Encountered unknown state: ${currentState}`)
            setState("ERROR")
            break

    }
}

/**
 * Initiates the shutdown process, cleaning up resources and preparing for application termination.
 */
async function shutdown() {
    console.log("[SHUTDOWN] Starting shutdown...")
    currentState = "SHUTTING_DOWN"

    liveDataQueue.length = 0

    try {
        if (currentPromise) {
            console.log("[DEBUG] executing abort")
            controller.abort()
            await currentPromise
        }

    } catch (error) {
        console.error(`[SHUTDOWN] Error during shutdown: ${error.message}`)
    }

    console.log("[SHUTDOWN] SHUTTING_DOWN... Cleanup complete")

}

// Load metadata dynamically
let metadata = null

function loadMetadata(metadataPath) {
    try {
        if (fs.existsSync(metadataPath)) {
            const metadataContent = fs.readFileSync(metadataPath, 'utf8')
            metadata = JSON.parse(metadataContent)
            console.log('[DATA_POSTER] Metadata loaded successfully from:', metadataPath)
        } else {
            console.warn('[DATA_POSTER] Metadata file not found:', metadataPath)
        }
    } catch (error) {
        console.error('[DATA_POSTER] Error loading metadata:', error)
    }
}

function getStatus(verbose = true) {    // add verbose flag 
    if (verbose) {
        return {
            state: currentState,
            stateChangeCounter: stateChangeCounter,
            enabled: config.enable,
            serverUrl: config.serverUrl,
            queueSize: liveDataQueue.length,
            maxQueueSize: MAX_LIVE_DATA_QUEUE_SIZE,
            hasActiveOperation: currentPromise !== null,
            resetPending: resetAfterOperation,
            metadata: metadata ? {
                mac: metadata.mac,
                projectName: metadata.projectName,
            } : null
        }
    } else {
        return {
            state: currentState,
            stateChangeCounter: stateChangeCounter,
        }
    }

}

module.exports = { init, svc, shutdown, getStatus }