import{r as l,f as i,o as p,D as u,F as m,M as d,O as S}from"./index-CzmOWWdj.js";import{s as f}from"./Login-BVtZ5S7B.js";import{_}from"./_plugin-vue_export-helper-DlAUqK2U.js";const g={__name:"SseSpinner",props:{url:{type:String,default:f}},emits:["message"],setup(n,{emit:a}){const c=n,t=a;let o=null;const r=l(0);return i(()=>{o=new EventSource(c.url),o.onmessage=function(e){try{const s=JSON.parse(e.data);r.value+=45,t("message",s)}catch(s){console.error("SseSpinner",s),t("message",e.data)}}}),p(()=>{o.close(),console.log("SseSpinner closed!")}),(e,s)=>(m(),u(S,d({class:"event-source-widget",icon:r.value?"autorenew":"trip_origin",round:"",flat:"",disabled:"",style:"rotate:"+r.value+"deg;"},e.$attrs),null,16,["icon","style"]))}},w=_(g,[["__scopeId","data-v-e61a87bf"]]);export{w as S};
