import{c as l,h as e,a as u,Q as r}from"./index-CzmOWWdj.js";import{b,a as h,c as i}from"./use-checkbox-DDSE4paG.js";const k=()=>e("div",{key:"svg",class:"q-checkbox__bg absolute"},[e("svg",{class:"q-checkbox__svg fit absolute-full",viewBox:"0 0 24 24"},[e("path",{class:"q-checkbox__truthy",fill:"none",d:"M1.73,12.91 8.1,19.28 22.79,4.59"}),e("path",{class:"q-checkbox__indet",d:"M4,14H20V10H4"})])]),d=l({name:"QCheckbox",props:h,emits:b,setup(c){const s=k();function n(t,a){const o=u(()=>(t.value===!0?c.checkedIcon:a.value===!0?c.indeterminateIcon:c.uncheckedIcon)||null);return()=>o.value!==null?[e("div",{key:"icon",class:"q-checkbox__icon-container absolute-full flex flex-center no-wrap"},[e(r,{class:"q-checkbox__icon",name:o.value})])]:[s]}return i("checkbox",n)}});export{d as Q};
