//*********************************************************************************************
//
// logs.js
//
// HTTP API routes for embedded system logging
//=============================================================================================

const { API_ROOT } = require('../System.js');
const {startupLogger } = require('../AppLogger.js');

/**
 * Routes for embedded system logs access
 * Provides HTTP API for accessing Startup, Operational, and Alerts logs
 * with pagination support and real-time SSE updates
 * 
 * @param {object} fastify - The Fastify instance
 * @param {object} options
 * @param {Function} done - The callback function to call when the plugin is registered
 */
function logsRoutes(fastify, options, done) {
    
    // Get the embedded logger instance
    let embeddedLogger;
    try {
        embeddedLogger = require('../Logger.js');
    } catch (error) {
        console.error('Failed to load embedded logger:', error.message);
        done();
        return;
    }

    //----------------------------------------------------------------------------------------------------
    // GET /api/logs - Get all log types summary
    //----------------------------------------------------------------------------------------------------
    fastify.get(API_ROOT + '/logs', async (request, reply) => {
        try {
            const allLogs = embeddedLogger.getAllMemoryLogs();
            const summary = {
                startup: {
                    count: allLogs.startup.length,
                    latest: allLogs.startup[allLogs.startup.length - 1] || null
                },
                operational: {
                    count: allLogs.operational.length,
                    latest: allLogs.operational[allLogs.operational.length - 1] || null
                },
                alerts: {
                    count: allLogs.alerts.length,
                    latest: allLogs.alerts[allLogs.alerts.length - 1] || null
                }
            };
            
            return reply.send({
                success: true,
                data: summary,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            return reply.code(500).send({
                success: false,
                error: 'Failed to retrieve logs summary',
                message: error.message
            });
        }
    });

    //----------------------------------------------------------------------------------------------------
    // GET /api/logs/:logType - Get logs for specific type with pagination
    // Query parameters: limit (default: 50), offset (default: 0)
    //----------------------------------------------------------------------------------------------------
    fastify.get(API_ROOT + '/logs/:logType', async (request, reply) => {
        const { logType } = request.params;
        const { limit = 50, offset = 0 } = request.query;
        
        // Validate log type
        if (!['startup', 'operational', 'alerts'].includes(logType)) {
            return reply.code(400).send({
                success: false,
                error: 'Invalid log type',
                message: 'Log type must be one of: startup, operational, alerts'
            });
        }

        // Validate pagination parameters
        const limitNum = parseInt(limit);
        const offsetNum = parseInt(offset);
        
        if (isNaN(limitNum) || limitNum < 1 || limitNum > 1000) {
            return reply.code(400).send({
                success: false,
                error: 'Invalid limit parameter',
                message: 'Limit must be a number between 1 and 1000'
            });
        }
        
        if (isNaN(offsetNum) || offsetNum < 0) {
            return reply.code(400).send({
                success: false,
                error: 'Invalid offset parameter',
                message: 'Offset must be a non-negative number'
            });
        }

        try {
            const memoryTransport = embeddedLogger.memoryTransports[logType];
            if (!memoryTransport) {
                return reply.code(404).send({
                    success: false,
                    error: 'Log type not found',
                    message: `No memory transport found for log type: ${logType}`
                });
            }

            const result = memoryTransport.paginateLogs(limitNum, offsetNum);
            
            return reply.send({
                success: true,
                data: {
                    logType,
                    logs: result.logs,
                    pagination: {
                        total: result.total,
                        limit: result.limit,
                        offset: result.offset,
                        hasMore: result.offset + result.limit < result.total
                    }
                },
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            return reply.code(500).send({
                success: false,
                error: 'Failed to retrieve logs',
                message: error.message
            });
        }
    });

    //----------------------------------------------------------------------------------------------------
    // GET /api/logs/:logType/latest - Get latest N logs for specific type
    // Query parameters: count (default: 10, max: 100)
    //----------------------------------------------------------------------------------------------------
    fastify.get(API_ROOT + '/logs/:logType/latest', async (request, reply) => {
        const { logType } = request.params;
        const { count = 10 } = request.query;
        
        // Validate log type
        if (!['startup', 'operational', 'alerts'].includes(logType)) {
            return reply.code(400).send({
                success: false,
                error: 'Invalid log type',
                message: 'Log type must be one of: startup, operational, alerts'
            });
        }

        // Validate count parameter
        const countNum = parseInt(count);
        if (isNaN(countNum) || countNum < 1 || countNum > 100) {
            return reply.code(400).send({
                success: false,
                error: 'Invalid count parameter',
                message: 'Count must be a number between 1 and 100'
            });
        }

        try {
            const logs = embeddedLogger.getMemoryLogs(logType);
            const latestLogs = logs.slice(-countNum).reverse(); // Get latest N, most recent first
            
            return reply.send({
                success: true,
                data: {
                    logType,
                    logs: latestLogs,
                    count: latestLogs.length,
                    total: logs.length
                },
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            return reply.code(500).send({
                success: false,
                error: 'Failed to retrieve latest logs',
                message: error.message
            });
        }
    });

    //----------------------------------------------------------------------------------------------------
    // GET /api/logs/:logType/stats - Get statistics for specific log type
    //----------------------------------------------------------------------------------------------------
    fastify.get(API_ROOT + '/logs/:logType/stats', async (request, reply) => {
        const { logType } = request.params;
        
        // Validate log type
        if (!['startup', 'operational', 'alerts'].includes(logType)) {
            return reply.code(400).send({
                success: false,
                error: 'Invalid log type',
                message: 'Log type must be one of: startup, operational, alerts'
            });
        }

        try {
            const memoryTransport = embeddedLogger.memoryTransports[logType];
            if (!memoryTransport) {
                return reply.code(404).send({
                    success: false,
                    error: 'Log type not found',
                    message: `No memory transport found for log type: ${logType}`
                });
            }

            const stats = memoryTransport.getStats();
            
            return reply.send({
                success: true,
                data: stats,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            return reply.code(500).send({
                success: false,
                error: 'Failed to retrieve log statistics',
                message: error.message
            });
        }
    });

    //----------------------------------------------------------------------------------------------------
    // DELETE /api/logs/:logType - Clear logs for specific type
    //----------------------------------------------------------------------------------------------------
    fastify.delete(API_ROOT + '/logs/:logType', async (request, reply) => {
        const { logType } = request.params;
        
        // Validate log type
        if (!['startup', 'operational', 'alerts'].includes(logType)) {
            return reply.code(400).send({
                success: false,
                error: 'Invalid log type',
                message: 'Log type must be one of: startup, operational, alerts'
            });
        }

        try {
            embeddedLogger.clearMemoryLogs(logType);
            
            return reply.send({
                success: true,
                message: `${logType} logs cleared successfully`,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            return reply.code(500).send({
                success: false,
                error: 'Failed to clear logs',
                message: error.message
            });
        }
    });

    //----------------------------------------------------------------------------------------------------
    // POST /api/logs/test - Test endpoint to generate sample log entries
    //----------------------------------------------------------------------------------------------------
    fastify.post(API_ROOT + '/logs/test', async (request, reply) => {
        const { logType = 'operational', level = 'info', message = 'Test log entry' } = request.body || {};
        
        // Validate log type
        if (!['startup', 'operational', 'alerts'].includes(logType)) {
            return reply.code(400).send({
                success: false,
                error: 'Invalid log type',
                message: 'Log type must be one of: startup, operational, alerts'
            });
        }

        // Validate level
        if (!['info', 'warn', 'error', 'fatal'].includes(level)) {
            return reply.code(400).send({
                success: false,
                error: 'Invalid log level',
                message: 'Log level must be one of: info, warn, error, fatal'
            });
        }

        try {
            // Generate test log entry
            embeddedLogger[logType][level](message, { 
                source: 'test-api',
                testData: true,
                timestamp: new Date().toISOString()
            });
            
            return reply.send({
                success: true,
                message: `Test ${level} log entry added to ${logType} logs`,
                data: { logType, level, message },
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            return reply.code(500).send({
                success: false,
                error: 'Failed to create test log entry',
                message: error.message
            });

            
        }
    });

    done();
}

module.exports = logsRoutes;
