import{c as H,g as U,h as i,b as ge,d as bt,Q as Ve,a as c,s as mt,r as N,w as D,t as St,q as Te,L as Me,f as Ee,am as yt,al as ht,o as je,C as Ae,an as wt,ao as _t,aD as Ct,aE as Be,a5 as be,aF as Oe,a6 as qt,n as Ne,aG as Pt,as as Q,O as p}from"./index-CzmOWWdj.js";import{Q as kt}from"./QSeparator-D-fNGoQY.js";import{Q as Rt}from"./QList-yWBbetAh.js";import{a as me,c as Se}from"./use-key-composition-CoMUTTxZ.js";import{u as xt,b as Ft,c as Qe,Q as Tt}from"./QSelect-BNWcW_ch.js";import{Q as fe}from"./QCheckbox-BLQMX8bg.js";const Bt=H({name:"QTh",props:{props:Object,autoWidth:Boolean},emits:["click"],setup(e,{slots:a,emit:n}){const v=U(),{proxy:{$q:r}}=v,b=s=>{n("click",s)};return()=>{if(e.props===void 0)return i("th",{class:e.autoWidth===!0?"q-table--col-auto-width":"",onClick:b},ge(a.default));let s,u;const d=v.vnode.key;if(d){if(s=e.props.colsMap[d],s===void 0)return}else s=e.props.col;if(s.sortable===!0){const l=s.align==="right"?"unshift":"push";u=bt(a.default,[]),u[l](i(Ve,{class:s.__iconClass,name:r.iconSet.table.arrowUp}))}else u=ge(a.default);const y={class:s.__thClass+(e.autoWidth===!0?" q-table--col-auto-width":""),style:s.headerStyle,onClick:l=>{s.sortable===!0&&e.props.sort(s),b(l)}};return i("th",y,u)}}}),Ot=["horizontal","vertical","cell","none"],$t=H({name:"QMarkupTable",props:{...me,dense:Boolean,flat:Boolean,bordered:Boolean,square:Boolean,wrapCells:Boolean,separator:{type:String,default:"horizontal",validator:e=>Ot.includes(e)}},setup(e,{slots:a}){const n=U(),v=Se(e,n.proxy.$q),r=c(()=>`q-markup-table q-table__container q-table__card q-table--${e.separator}-separator`+(v.value===!0?" q-table--dark q-table__card--dark q-dark":"")+(e.dense===!0?" q-table--dense":"")+(e.flat===!0?" q-table--flat":"")+(e.bordered===!0?" q-table--bordered":"")+(e.square===!0?" q-table--square":"")+(e.wrapCells===!1?" q-table--no-wrap":""));return()=>i("div",{class:r.value},[i("table",{class:"q-table"},ge(a.default))])}});function ze(e,a){return i("div",e,[i("table",{class:"q-table"},a)])}const Lt={list:Rt,table:$t},Dt=["list","table","__qtable"],Vt=H({name:"QVirtualScroll",props:{...xt,type:{type:String,default:"list",validator:e=>Dt.includes(e)},items:{type:Array,default:()=>[]},itemsFn:Function,itemsSize:Number,scrollTarget:mt},setup(e,{slots:a,attrs:n}){let v;const r=N(null),b=c(()=>e.itemsSize>=0&&e.itemsFn!==void 0?parseInt(e.itemsSize,10):Array.isArray(e.items)?e.items.length:0),{virtualScrollSliceRange:s,localResetVirtualScroll:u,padVirtualScroll:d,onVirtualScrollEvt:y}=Ft({virtualScrollLength:b,getVirtualScrollTarget:q,getVirtualScrollEl:C}),l=c(()=>{if(b.value===0)return[];const B=(O,x)=>({index:s.value.from+x,item:O});return e.itemsFn===void 0?e.items.slice(s.value.from,s.value.to).map(B):e.itemsFn(s.value.from,s.value.to-s.value.from).map(B)}),S=c(()=>"q-virtual-scroll q-virtual-scroll"+(e.virtualScrollHorizontal===!0?"--horizontal":"--vertical")+(e.scrollTarget!==void 0?"":" scroll")),_=c(()=>e.scrollTarget!==void 0?{}:{tabindex:0});D(b,()=>{u()}),D(()=>e.scrollTarget,()=>{h(),w()});function C(){return r.value.$el||r.value}function q(){return v}function w(){v=St(C(),e.scrollTarget),v.addEventListener("scroll",y,Te.passive)}function h(){v!==void 0&&(v.removeEventListener("scroll",y,Te.passive),v=void 0)}function T(){let B=d(e.type==="list"?"div":"tbody",l.value.map(a.default));return a.before!==void 0&&(B=a.before().concat(B)),Ae(a.after,B)}return Me(()=>{u()}),Ee(()=>{w()}),yt(()=>{w()}),ht(()=>{h()}),je(()=>{h()}),()=>{if(a.default===void 0){console.error("QVirtualScroll: default scoped slot is required for rendering");return}return e.type==="__qtable"?ze({ref:r,class:"q-table__middle "+S.value},T()):i(Lt[e.type],{...n,ref:r,class:[n.class,S.value],..._.value},T)}}}),Mt={xs:2,sm:4,md:6,lg:10,xl:14};function $e(e,a,n){return{transform:a===!0?`translateX(${n.lang.rtl===!0?"-":""}100%) scale3d(${-e},1,1)`:`scale3d(${e},1,1)`}}const Et=H({name:"QLinearProgress",props:{...me,...wt,value:{type:Number,default:0},buffer:Number,color:String,trackColor:String,reverse:Boolean,stripe:Boolean,indeterminate:Boolean,query:Boolean,rounded:Boolean,animationSpeed:{type:[String,Number],default:2100},instantFeedback:Boolean},setup(e,{slots:a}){const{proxy:n}=U(),v=Se(e,n.$q),r=_t(e,Mt),b=c(()=>e.indeterminate===!0||e.query===!0),s=c(()=>e.reverse!==e.query),u=c(()=>({...r.value!==null?r.value:{},"--q-linear-progress-speed":`${e.animationSpeed}ms`})),d=c(()=>"q-linear-progress"+(e.color!==void 0?` text-${e.color}`:"")+(e.reverse===!0||e.query===!0?" q-linear-progress--reverse":"")+(e.rounded===!0?" rounded-borders":"")),y=c(()=>$e(e.buffer!==void 0?e.buffer:1,s.value,n.$q)),l=c(()=>`with${e.instantFeedback===!0?"out":""}-transition`),S=c(()=>`q-linear-progress__track absolute-full q-linear-progress__track--${l.value} q-linear-progress__track--${v.value===!0?"dark":"light"}`+(e.trackColor!==void 0?` bg-${e.trackColor}`:"")),_=c(()=>$e(b.value===!0?1:e.value,s.value,n.$q)),C=c(()=>`q-linear-progress__model absolute-full q-linear-progress__model--${l.value} q-linear-progress__model--${b.value===!0?"in":""}determinate`),q=c(()=>({width:`${e.value*100}%`})),w=c(()=>`q-linear-progress__stripe absolute-${e.reverse===!0?"right":"left"} q-linear-progress__stripe--${l.value}`);return()=>{const h=[i("div",{class:S.value,style:y.value}),i("div",{class:C.value,style:_.value})];return e.stripe===!0&&b.value===!1&&h.push(i("div",{class:w.value,style:q.value})),i("div",{class:d.value,style:u.value,role:"progressbar","aria-valuemin":0,"aria-valuemax":1,"aria-valuenow":e.indeterminate===!0?void 0:e.value},Ae(a.default,h))}}});let z=0;const jt={fullscreen:Boolean,noRouteFullscreenExit:Boolean},At=["update:fullscreen","fullscreen"];function Nt(){const e=U(),{props:a,emit:n,proxy:v}=e;let r,b,s;const u=N(!1);Ct(e)===!0&&D(()=>v.$route.fullPath,()=>{a.noRouteFullscreenExit!==!0&&l()}),D(()=>a.fullscreen,S=>{u.value!==S&&d()}),D(u,S=>{n("update:fullscreen",S),n("fullscreen",S)});function d(){u.value===!0?l():y()}function y(){u.value!==!0&&(u.value=!0,s=v.$el.parentNode,s.replaceChild(b,v.$el),document.body.appendChild(v.$el),z++,z===1&&document.body.classList.add("q-body--fullscreen-mixin"),r={handler:l},Be.add(r))}function l(){u.value===!0&&(r!==void 0&&(Be.remove(r),r=void 0),s.replaceChild(v.$el,b),u.value=!1,z=Math.max(0,z-1),z===0&&(document.body.classList.remove("q-body--fullscreen-mixin"),v.$el.scrollIntoView!==void 0&&setTimeout(()=>{v.$el.scrollIntoView()})))}return Me(()=>{b=document.createElement("span")}),Ee(()=>{a.fullscreen===!0&&y()}),je(l),Object.assign(v,{toggleFullscreen:d,setFullscreen:y,exitFullscreen:l}),{inFullscreen:u,toggleFullscreen:d}}function Qt(e,a){return new Date(e)-new Date(a)}const zt={sortMethod:Function,binaryStateSort:Boolean,columnSortOrder:{type:String,validator:e=>e==="ad"||e==="da",default:"ad"}};function Ht(e,a,n,v){const r=c(()=>{const{sortBy:u}=a.value;return u&&n.value.find(d=>d.name===u)||null}),b=c(()=>e.sortMethod!==void 0?e.sortMethod:(u,d,y)=>{const l=n.value.find(C=>C.name===d);if(l===void 0||l.field===void 0)return u;const S=y===!0?-1:1,_=typeof l.field=="function"?C=>l.field(C):C=>C[l.field];return u.sort((C,q)=>{let w=_(C),h=_(q);return l.rawSort!==void 0?l.rawSort(w,h,C,q)*S:w==null?-1*S:h==null?1*S:l.sort!==void 0?l.sort(w,h,C,q)*S:be(w)===!0&&be(h)===!0?(w-h)*S:Oe(w)===!0&&Oe(h)===!0?Qt(w,h)*S:typeof w=="boolean"&&typeof h=="boolean"?(w-h)*S:([w,h]=[w,h].map(T=>(T+"").toLocaleString().toLowerCase()),w<h?-1*S:w===h?0:S)})});function s(u){let d=e.columnSortOrder;if(qt(u)===!0)u.sortOrder&&(d=u.sortOrder),u=u.name;else{const S=n.value.find(_=>_.name===u);S?.sortOrder&&(d=S.sortOrder)}let{sortBy:y,descending:l}=a.value;y!==u?(y=u,l=d==="da"):e.binaryStateSort===!0?l=!l:l===!0?d==="ad"?y=null:l=!1:d==="ad"?l=!0:y=null,v({sortBy:y,descending:l,page:1})}return{columnToSort:r,computedSortMethod:b,sort:s}}const Ut={filter:[String,Object],filterMethod:Function};function It(e,a){const n=c(()=>e.filterMethod!==void 0?e.filterMethod:(v,r,b,s)=>{const u=r?r.toLowerCase():"";return v.filter(d=>b.some(y=>{const l=s(y,d)+"";return(l==="undefined"||l==="null"?"":l.toLowerCase()).indexOf(u)!==-1}))});return D(()=>e.filter,()=>{Ne(()=>{a({page:1},!0)})},{deep:!0}),{computedFilterMethod:n}}function Gt(e,a){for(const n in a)if(a[n]!==e[n])return!1;return!0}function Le(e){return e.page<1&&(e.page=1),e.rowsPerPage!==void 0&&e.rowsPerPage<1&&(e.rowsPerPage=0),e}const Kt={pagination:Object,rowsPerPageOptions:{type:Array,default:()=>[5,7,10,15,20,25,50,0]},"onUpdate:pagination":[Function,Array]};function Wt(e,a){const{props:n,emit:v}=e,r=N(Object.assign({sortBy:null,descending:!1,page:1,rowsPerPage:n.rowsPerPageOptions.length!==0?n.rowsPerPageOptions[0]:5},n.pagination)),b=c(()=>{const l=n["onUpdate:pagination"]!==void 0?{...r.value,...n.pagination}:r.value;return Le(l)}),s=c(()=>b.value.rowsNumber!==void 0);function u(l){d({pagination:l,filter:n.filter})}function d(l={}){Ne(()=>{v("request",{pagination:l.pagination||b.value,filter:l.filter||n.filter,getCellValue:a})})}function y(l,S){const _=Le({...b.value,...l});if(Gt(b.value,_)===!0){s.value===!0&&S===!0&&u(_);return}if(s.value===!0){u(_);return}n.pagination!==void 0&&n["onUpdate:pagination"]!==void 0?v("update:pagination",_):r.value=_}return{innerPagination:r,computedPagination:b,isServerSide:s,requestServerInteraction:d,setPagination:y}}function Xt(e,a,n,v,r,b){const{props:s,emit:u,proxy:{$q:d}}=e,y=c(()=>v.value===!0?n.value.rowsNumber||0:b.value),l=c(()=>{const{page:x,rowsPerPage:F}=n.value;return(x-1)*F}),S=c(()=>{const{page:x,rowsPerPage:F}=n.value;return x*F}),_=c(()=>n.value.page===1),C=c(()=>n.value.rowsPerPage===0?1:Math.max(1,Math.ceil(y.value/n.value.rowsPerPage))),q=c(()=>S.value===0?!0:n.value.page>=C.value),w=c(()=>(s.rowsPerPageOptions.includes(a.value.rowsPerPage)?s.rowsPerPageOptions:[a.value.rowsPerPage].concat(s.rowsPerPageOptions)).map(F=>({label:F===0?d.lang.table.allRows:""+F,value:F})));D(C,(x,F)=>{if(x===F)return;const I=n.value.page;x&&!I?r({page:1}):x<I&&r({page:x})});function h(){r({page:1})}function T(){const{page:x}=n.value;x>1&&r({page:x-1})}function B(){const{page:x,rowsPerPage:F}=n.value;S.value>0&&x*F<y.value&&r({page:x+1})}function O(){r({page:C.value})}return s["onUpdate:pagination"]!==void 0&&u("update:pagination",{...n.value}),{firstRowIndex:l,lastRowIndex:S,isFirstPage:_,isLastPage:q,pagesNumber:C,computedRowsPerPageOptions:w,computedRowsNumber:y,firstPage:h,prevPage:T,nextPage:B,lastPage:O}}const Jt={selection:{type:String,default:"none",validator:e=>["single","multiple","none"].includes(e)},selected:{type:Array,default:()=>[]}},Yt=["update:selected","selection"];function Zt(e,a,n,v){const r=c(()=>{const q={};return e.selected.map(v.value).forEach(w=>{q[w]=!0}),q}),b=c(()=>e.selection!=="none"),s=c(()=>e.selection==="single"),u=c(()=>e.selection==="multiple"),d=c(()=>n.value.length!==0&&n.value.every(q=>r.value[v.value(q)]===!0)),y=c(()=>d.value!==!0&&n.value.some(q=>r.value[v.value(q)]===!0)),l=c(()=>e.selected.length);function S(q){return r.value[q]===!0}function _(){a("update:selected",[])}function C(q,w,h,T){a("selection",{rows:w,added:h,keys:q,evt:T});const B=s.value===!0?h===!0?w:[]:h===!0?e.selected.concat(w):e.selected.filter(O=>q.includes(v.value(O))===!1);a("update:selected",B)}return{hasSelectionMode:b,singleSelection:s,multipleSelection:u,allRowsSelected:d,someRowsSelected:y,rowsSelectedNumber:l,isRowSelected:S,clearSelection:_,updateSelection:C}}function De(e){return Array.isArray(e)?e.slice():[]}const pt={expanded:Array},el=["update:expanded"];function tl(e,a){const n=N(De(e.expanded));D(()=>e.expanded,s=>{n.value=De(s)});function v(s){return n.value.includes(s)}function r(s){e.expanded!==void 0?a("update:expanded",s):n.value=s}function b(s,u){const d=n.value.slice(),y=d.indexOf(s);u===!0?y===-1&&(d.push(s),r(d)):y!==-1&&(d.splice(y,1),r(d))}return{isRowExpanded:v,setExpanded:r,updateExpanded:b}}const ll={visibleColumns:Array};function al(e,a,n){const v=c(()=>{if(e.columns!==void 0)return e.columns;const u=e.rows[0];return u!==void 0?Object.keys(u).map(d=>({name:d,label:d.toUpperCase(),field:d,align:be(u[d])?"right":"left",sortable:!0})):[]}),r=c(()=>{const{sortBy:u,descending:d}=a.value;return(e.visibleColumns!==void 0?v.value.filter(l=>l.required===!0||e.visibleColumns.includes(l.name)===!0):v.value).map(l=>{const S=l.align||"right",_=`text-${S}`;return{...l,align:S,__iconClass:`q-table__sort-icon q-table__sort-icon--${S}`,__thClass:_+(l.headerClasses!==void 0?" "+l.headerClasses:"")+(l.sortable===!0?" sortable":"")+(l.name===u?` sorted ${d===!0?"sort-desc":""}`:""),__tdStyle:l.style!==void 0?typeof l.style!="function"?()=>l.style:l.style:()=>null,__tdClass:l.classes!==void 0?typeof l.classes!="function"?()=>_+" "+l.classes:C=>_+" "+l.classes(C):()=>_}})}),b=c(()=>{const u={};return r.value.forEach(d=>{u[d.name]=d}),u}),s=c(()=>e.tableColspan!==void 0?e.tableColspan:r.value.length+(n.value===!0?1:0));return{colList:v,computedCols:r,computedColsMap:b,computedColspan:s}}const ee="q-table__bottom row items-center",He={};Qe.forEach(e=>{He[e]={}});const cl=H({name:"QTable",props:{rows:{type:Array,required:!0},rowKey:{type:[String,Function],default:"id"},columns:Array,loading:Boolean,iconFirstPage:String,iconPrevPage:String,iconNextPage:String,iconLastPage:String,title:String,hideHeader:Boolean,grid:Boolean,gridHeader:Boolean,dense:Boolean,flat:Boolean,bordered:Boolean,square:Boolean,separator:{type:String,default:"horizontal",validator:e=>["horizontal","vertical","cell","none"].includes(e)},wrapCells:Boolean,virtualScroll:Boolean,virtualScrollTarget:{},...He,noDataLabel:String,noResultsLabel:String,loadingLabel:String,selectedRowsLabel:Function,rowsPerPageLabel:String,paginationLabel:Function,color:{type:String,default:"grey-8"},titleClass:[String,Array,Object],tableStyle:[String,Array,Object],tableClass:[String,Array,Object],tableHeaderStyle:[String,Array,Object],tableHeaderClass:[String,Array,Object],tableRowStyleFn:Function,tableRowClassFn:Function,cardContainerClass:[String,Array,Object],cardContainerStyle:[String,Array,Object],cardStyle:[String,Array,Object],cardClass:[String,Array,Object],cardStyleFn:Function,cardClassFn:Function,hideBottom:Boolean,hideSelectedBanner:Boolean,hideNoData:Boolean,hidePagination:Boolean,onRowClick:Function,onRowDblclick:Function,onRowContextmenu:Function,...me,...jt,...ll,...Ut,...Kt,...pt,...Jt,...zt},emits:["request","virtualScroll",...At,...el,...Yt],setup(e,{slots:a,emit:n}){const v=U(),{proxy:{$q:r}}=v,b=Se(e,r),{inFullscreen:s,toggleFullscreen:u}=Nt(),d=c(()=>typeof e.rowKey=="function"?e.rowKey:t=>t[e.rowKey]),y=N(null),l=N(null),S=c(()=>e.grid!==!0&&e.virtualScroll===!0),_=c(()=>" q-table__card"+(b.value===!0?" q-table__card--dark q-dark":"")+(e.square===!0?" q-table--square":"")+(e.flat===!0?" q-table--flat":"")+(e.bordered===!0?" q-table--bordered":"")),C=c(()=>`q-table__container q-table--${e.separator}-separator column no-wrap`+(e.grid===!0?" q-table--grid":_.value)+(b.value===!0?" q-table--dark":"")+(e.dense===!0?" q-table--dense":"")+(e.wrapCells===!1?" q-table--no-wrap":"")+(s.value===!0?" fullscreen scroll":"")),q=c(()=>C.value+(e.loading===!0?" q-table--loading":""));D(()=>e.tableStyle+e.tableClass+e.tableHeaderStyle+e.tableHeaderClass+C.value,()=>{S.value===!0&&l.value?.reset()});const{innerPagination:w,computedPagination:h,isServerSide:T,requestServerInteraction:B,setPagination:O}=Wt(v,j),{computedFilterMethod:x}=It(e,O),{isRowExpanded:F,setExpanded:I,updateExpanded:Ue}=tl(e,n),te=c(()=>{let t=e.rows;if(T.value===!0||t.length===0)return t;const{sortBy:o,descending:f}=h.value;return e.filter&&(t=x.value(t,e.filter,L.value,j)),Xe.value!==null&&(t=Je.value(e.rows===t?t.slice():t,o,f)),t}),ye=c(()=>te.value.length),V=c(()=>{let t=te.value;if(T.value===!0)return t;const{rowsPerPage:o}=h.value;return o!==0&&(K.value===0&&e.rows!==t?t.length>W.value&&(t=t.slice(0,W.value)):t=t.slice(K.value,W.value)),t}),{hasSelectionMode:E,singleSelection:Ie,multipleSelection:he,allRowsSelected:Ge,someRowsSelected:we,rowsSelectedNumber:le,isRowSelected:ae,clearSelection:Ke,updateSelection:G}=Zt(e,n,V,d),{colList:We,computedCols:L,computedColsMap:_e,computedColspan:Ce}=al(e,h,E),{columnToSort:Xe,computedSortMethod:Je,sort:ne}=Ht(e,h,We,O),{firstRowIndex:K,lastRowIndex:W,isFirstPage:re,isLastPage:oe,pagesNumber:X,computedRowsPerPageOptions:Ye,computedRowsNumber:J,firstPage:ie,prevPage:se,nextPage:ue,lastPage:ce}=Xt(v,w,h,T,O,ye),Ze=c(()=>V.value.length===0),pe=c(()=>{const t={};return Qe.forEach(o=>{t[o]=e[o]}),t.virtualScrollItemSize===void 0&&(t.virtualScrollItemSize=e.dense===!0?28:48),t});function et(){S.value===!0&&l.value.reset()}function tt(){if(e.grid===!0)return ft();const t=e.hideHeader!==!0?xe:null;if(S.value===!0){const f=a["top-row"],g=a["bottom-row"],m={default:R=>Pe(R.item,a.body,R.index)};if(f!==void 0){const R=i("tbody",f({cols:L.value}));m.before=t===null?()=>R:()=>[t()].concat(R)}else t!==null&&(m.before=t);return g!==void 0&&(m.after=()=>i("tbody",g({cols:L.value}))),i(Vt,{ref:l,class:e.tableClass,style:e.tableStyle,...pe.value,scrollTarget:e.virtualScrollTarget,items:V.value,type:"__qtable",tableColspan:Ce.value,onVirtualScroll:at},m)}const o=[nt()];return t!==null&&o.unshift(t()),ze({class:["q-table__middle scroll",e.tableClass],style:e.tableStyle},o)}function lt(t,o){if(l.value!==null){l.value.scrollTo(t,o);return}t=parseInt(t,10);const f=y.value.querySelector(`tbody tr:nth-of-type(${t+1})`);if(f!==null){const g=y.value.querySelector(".q-table__middle.scroll"),m=f.offsetTop-e.virtualScrollStickySizeStart,R=m<g.scrollTop?"decrease":"increase";g.scrollTop=m,n("virtualScroll",{index:t,from:0,to:w.value.rowsPerPage-1,direction:R})}}function at(t){n("virtualScroll",t)}function qe(){return[i(Et,{class:"q-table__linear-progress",color:e.color,dark:b.value,indeterminate:!0,trackColor:"transparent"})]}function Pe(t,o,f){const g=d.value(t),m=ae(g);if(o!==void 0){const k={key:g,row:t,pageIndex:f,__trClass:m?"selected":""};if(e.tableRowStyleFn!==void 0&&(k.__trStyle=e.tableRowStyleFn(t)),e.tableRowClassFn!==void 0){const M=e.tableRowClassFn(t);M&&(k.__trClass=`${M} ${k.__trClass}`)}return o(ke(k))}const R=a["body-cell"],P=L.value.map(k=>{const M=a[`body-cell-${k.name}`],Z=M!==void 0?M:R;return Z!==void 0?Z(rt({key:g,row:t,pageIndex:f,col:k})):i("td",{class:k.__tdClass(t),style:k.__tdStyle(t)},j(k,t))});if(E.value===!0){const k=a["body-selection"],M=k!==void 0?k(ot({key:g,row:t,pageIndex:f})):[i(fe,{modelValue:m,color:e.color,dark:b.value,dense:e.dense,"onUpdate:modelValue":(Z,gt)=>{G([g],[t],Z,gt)}})];P.unshift(i("td",{class:"q-table--col-auto-width"},M))}const $={key:g,class:{selected:m}};if(e.onRowClick!==void 0&&($.class["cursor-pointer"]=!0,$.onClick=k=>{n("rowClick",k,t,f)}),e.onRowDblclick!==void 0&&($.class["cursor-pointer"]=!0,$.onDblclick=k=>{n("rowDblclick",k,t,f)}),e.onRowContextmenu!==void 0&&($.class["cursor-pointer"]=!0,$.onContextmenu=k=>{n("rowContextmenu",k,t,f)}),e.tableRowStyleFn!==void 0&&($.style=e.tableRowStyleFn(t)),e.tableRowClassFn!==void 0){const k=e.tableRowClassFn(t);k&&($.class[k]=!0)}return i("tr",$,P)}function nt(){const t=a.body,o=a["top-row"],f=a["bottom-row"];let g=V.value.map((m,R)=>Pe(m,t,R));return o!==void 0&&(g=o({cols:L.value}).concat(g)),f!==void 0&&(g=g.concat(f({cols:L.value}))),i("tbody",g)}function ke(t){return de(t),t.cols=t.cols.map(o=>Q({...o},"value",()=>j(o,t.row))),t}function rt(t){return de(t),Q(t,"value",()=>j(t.col,t.row)),t}function ot(t){return de(t),t}function de(t){Object.assign(t,{cols:L.value,colsMap:_e.value,sort:ne,rowIndex:K.value+t.pageIndex,color:e.color,dark:b.value,dense:e.dense}),E.value===!0&&Q(t,"selected",()=>ae(t.key),(o,f)=>{G([t.key],[t.row],o,f)}),Q(t,"expand",()=>F(t.key),o=>{Ue(t.key,o)})}function j(t,o){const f=typeof t.field=="function"?t.field(o):o[t.field];return t.format!==void 0?t.format(f,o):f}const A=c(()=>({pagination:h.value,pagesNumber:X.value,isFirstPage:re.value,isLastPage:oe.value,firstPage:ie,prevPage:se,nextPage:ue,lastPage:ce,inFullscreen:s.value,toggleFullscreen:u}));function it(){const t=a.top,o=a["top-left"],f=a["top-right"],g=a["top-selection"],m=E.value===!0&&g!==void 0&&le.value>0,R="q-table__top relative-position row items-center";if(t!==void 0)return i("div",{class:R},[t(A.value)]);let P;if(m===!0?P=g(A.value).slice():(P=[],o!==void 0?P.push(i("div",{class:"q-table__control"},[o(A.value)])):e.title&&P.push(i("div",{class:"q-table__control"},[i("div",{class:["q-table__title",e.titleClass]},e.title)]))),f!==void 0&&(P.push(i("div",{class:"q-table__separator col"})),P.push(i("div",{class:"q-table__control"},[f(A.value)]))),P.length!==0)return i("div",{class:R},P)}const Re=c(()=>we.value===!0?null:Ge.value);function xe(){const t=st();return e.loading===!0&&a.loading===void 0&&t.push(i("tr",{class:"q-table__progress"},[i("th",{class:"relative-position",colspan:Ce.value},qe())])),i("thead",t)}function st(){const t=a.header,o=a["header-cell"];if(t!==void 0)return t(ve({header:!0})).slice();const f=L.value.map(g=>{const m=a[`header-cell-${g.name}`],R=m!==void 0?m:o,P=ve({col:g});return R!==void 0?R(P):i(Bt,{key:g.name,props:P},()=>g.label)});if(Ie.value===!0&&e.grid!==!0)f.unshift(i("th",{class:"q-table--col-auto-width"}," "));else if(he.value===!0){const g=a["header-selection"],m=g!==void 0?g(ve({})):[i(fe,{color:e.color,modelValue:Re.value,dark:b.value,dense:e.dense,"onUpdate:modelValue":Fe})];f.unshift(i("th",{class:"q-table--col-auto-width"},m))}return[i("tr",{class:e.tableHeaderClass,style:e.tableHeaderStyle},f)]}function ve(t){return Object.assign(t,{cols:L.value,sort:ne,colsMap:_e.value,color:e.color,dark:b.value,dense:e.dense}),he.value===!0&&Q(t,"selected",()=>Re.value,Fe),t}function Fe(t){we.value===!0&&(t=!1),G(V.value.map(d.value),V.value,t)}const Y=c(()=>{const t=[e.iconFirstPage||r.iconSet.table.firstPage,e.iconPrevPage||r.iconSet.table.prevPage,e.iconNextPage||r.iconSet.table.nextPage,e.iconLastPage||r.iconSet.table.lastPage];return r.lang.rtl===!0?t.reverse():t});function ut(){if(e.hideBottom===!0)return;if(Ze.value===!0){if(e.hideNoData===!0)return;const f=e.loading===!0?e.loadingLabel||r.lang.table.loading:e.filter?e.noResultsLabel||r.lang.table.noResults:e.noDataLabel||r.lang.table.noData,g=a["no-data"],m=g!==void 0?[g({message:f,icon:r.iconSet.table.warning,filter:e.filter})]:[i(Ve,{class:"q-table__bottom-nodata-icon",name:r.iconSet.table.warning}),f];return i("div",{class:ee+" q-table__bottom--nodata"},m)}const t=a.bottom;if(t!==void 0)return i("div",{class:ee},[t(A.value)]);const o=e.hideSelectedBanner!==!0&&E.value===!0&&le.value>0?[i("div",{class:"q-table__control"},[i("div",[(e.selectedRowsLabel||r.lang.table.selectedRecords)(le.value)])])]:[];if(e.hidePagination!==!0)return i("div",{class:ee+" justify-end"},dt(o));if(o.length!==0)return i("div",{class:ee},o)}function ct(t){O({page:1,rowsPerPage:t.value})}function dt(t){let o;const{rowsPerPage:f}=h.value,g=e.paginationLabel||r.lang.table.pagination,m=a.pagination,R=e.rowsPerPageOptions.length>1;if(t.push(i("div",{class:"q-table__separator col"})),R===!0&&t.push(i("div",{class:"q-table__control"},[i("span",{class:"q-table__bottom-item"},[e.rowsPerPageLabel||r.lang.table.recordsPerPage]),i(Tt,{class:"q-table__select inline q-table__bottom-item",color:e.color,modelValue:f,options:Ye.value,displayValue:f===0?r.lang.table.allRows:f,dark:b.value,borderless:!0,dense:!0,optionsDense:!0,optionsCover:!0,"onUpdate:modelValue":ct})])),m!==void 0)o=m(A.value);else if(o=[i("span",f!==0?{class:"q-table__bottom-item"}:{},[f?g(K.value+1,Math.min(W.value,J.value),J.value):g(1,ye.value,J.value)])],f!==0&&X.value>1){const P={color:e.color,round:!0,dense:!0,flat:!0};e.dense===!0&&(P.size="sm"),X.value>2&&o.push(i(p,{key:"pgFirst",...P,icon:Y.value[0],disable:re.value,ariaLabel:r.lang.pagination.first,onClick:ie})),o.push(i(p,{key:"pgPrev",...P,icon:Y.value[1],disable:re.value,ariaLabel:r.lang.pagination.prev,onClick:se}),i(p,{key:"pgNext",...P,icon:Y.value[2],disable:oe.value,ariaLabel:r.lang.pagination.next,onClick:ue})),X.value>2&&o.push(i(p,{key:"pgLast",...P,icon:Y.value[3],disable:oe.value,ariaLabel:r.lang.pagination.last,onClick:ce}))}return t.push(i("div",{class:"q-table__control"},o)),t}function vt(){const t=e.gridHeader===!0?[i("table",{class:"q-table"},[xe()])]:e.loading===!0&&a.loading===void 0?qe():void 0;return i("div",{class:"q-table__middle"},t)}function ft(){const t=a.item!==void 0?a.item:o=>{const f=o.cols.map(m=>i("div",{class:"q-table__grid-item-row"},[i("div",{class:"q-table__grid-item-title"},[m.label]),i("div",{class:"q-table__grid-item-value"},[m.value])]));if(E.value===!0){const m=a["body-selection"],R=m!==void 0?m(o):[i(fe,{modelValue:o.selected,color:e.color,dark:b.value,dense:e.dense,"onUpdate:modelValue":(P,$)=>{G([o.key],[o.row],P,$)}})];f.unshift(i("div",{class:"q-table__grid-item-row"},R),i(kt,{dark:b.value}))}const g={class:["q-table__grid-item-card"+_.value,e.cardClass],style:e.cardStyle};if(e.cardStyleFn!==void 0&&(g.style=[g.style,e.cardStyleFn(o.row)]),e.cardClassFn!==void 0){const m=e.cardClassFn(o.row);m&&(g.class[0]+=` ${m}`)}return(e.onRowClick!==void 0||e.onRowDblclick!==void 0||e.onRowContextmenu!==void 0)&&(g.class[0]+=" cursor-pointer",e.onRowClick!==void 0&&(g.onClick=m=>{n("RowClick",m,o.row,o.pageIndex)}),e.onRowDblclick!==void 0&&(g.onDblclick=m=>{n("RowDblclick",m,o.row,o.pageIndex)}),e.onRowContextmenu!==void 0&&(g.onContextmenu=m=>{n("rowContextmenu",m,o.row,o.pageIndex)})),i("div",{class:"q-table__grid-item col-xs-12 col-sm-6 col-md-4 col-lg-3"+(o.selected===!0?" q-table__grid-item--selected":"")},[i("div",g,f)])};return i("div",{class:["q-table__grid-content row",e.cardContainerClass],style:e.cardContainerStyle},V.value.map((o,f)=>t(ke({key:d.value(o),row:o,pageIndex:f}))))}return Object.assign(v.proxy,{requestServerInteraction:B,setPagination:O,firstPage:ie,prevPage:se,nextPage:ue,lastPage:ce,isRowSelected:ae,clearSelection:Ke,isRowExpanded:F,setExpanded:I,sort:ne,resetVirtualScroll:et,scrollTo:lt,getCellValue:j}),Pt(v.proxy,{filteredSortedRows:()=>te.value,computedRows:()=>V.value,computedRowsNumber:()=>J.value}),()=>{const t=[it()],o={ref:y,class:q.value};return e.grid===!0?t.push(vt()):Object.assign(o,{class:[o.class,e.cardClass],style:e.cardStyle}),t.push(tt(),ut()),e.loading===!0&&a.loading!==void 0&&t.push(a.loading()),i("div",o,t)}}});export{cl as Q};
