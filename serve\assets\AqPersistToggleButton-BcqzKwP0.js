import{Q as n}from"./use-key-composition-CoMUTTxZ.js";import{a as g,f as S,D as a,F as t,G as r,E as i,I as c,J as p,M as d,O as f}from"./index-CzmOWWdj.js";const b={__name:"AqPersistToggleButton",props:{modelValue:{required:!0},defaultValue:{type:Boolean,default:!1},localStoreKey:{type:String,required:!0},colorTrue:{type:[String,Boolean],default:!1},colorFalse:{type:[String,Boolean],default:!1},iconTrue:{type:[String,Boolean],default:!1},iconFalse:{type:[String,Boolean],default:!1},labelTrue:{type:[String,Boolean],default:!1},labelFalse:{type:[String,Boolean],default:!1},tooltipTrue:{type:[String,Boolean],default:!1},tooltipFalse:{type:[String,Boolean],default:!1}},emits:["update:modelValue"],setup(y,{emit:m}){const e=y,u=m,l=g(()=>!!e.modelValue),s=()=>{localStorage.setItem(e.localStoreKey,JSON.stringify(!l.value)),u("update:modelValue",!l.value)};return S(()=>{const o=JSON.parse(localStorage.getItem(e.localStoreKey)??!!e.defaultValue);u("update:modelValue",o)}),(o,T)=>e.iconTrue&&e.iconFalse?(t(),a(f,d({key:0,color:(l.value?e.colorTrue:e.colorFalse)||null,icon:(l.value?e.iconTrue:e.iconFalse)||null,label:(l.value?e.labelTrue:e.labelFalse)||null,onClick:s},o.$attrs),{default:r(()=>[(l.value?e.tooltipTrue:e.tooltipFalse)?(t(),a(n,{key:0,class:"text-body2"},{default:r(()=>[c(p(l.value?e.tooltipTrue:e.tooltipFalse),1)]),_:1})):i("",!0)]),_:1},16,["color","icon","label"])):(t(),a(f,d({key:1,color:(l.value?e.colorTrue:e.colorFalse)||null,label:(l.value?e.labelTrue:e.labelFalse)||null,onClick:s},o.$attrs),{default:r(()=>[(l.value?e.tooltipTrue:e.tooltipFalse)?(t(),a(n,{key:0,class:"text-body2"},{default:r(()=>[c(p(l.value?e.tooltipTrue:e.tooltipFalse),1)]),_:1})):i("",!0)]),_:1},16,["color","label"]))}};export{b as _};
