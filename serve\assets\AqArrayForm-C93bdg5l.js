import{S as bn,Q as yn}from"./SaveButton-DQ7yxOUN.js";import{ae as wn,a as gt,af as Sn,S as $,r as _n,A as Dn,h as En,g as Dt,w as Cn,f as Tn,n as Lt,B as xn,ag as ot,T as ce,F as ee,X as J,a2 as On,D as mt,G as z,H as O,E as we,a1 as vt,I as Te,Q as Re,J as An,M as Ut,O as In,U as Nn,Y as Mn}from"./index-CzmOWWdj.js";import{_ as Qt}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{a as Xe,Q as Fe}from"./focusout-C-pmmZED.js";import{Q as rt}from"./QSeparator-D-fNGoQY.js";import{Q as kn}from"./QMenu-D3shCIOy.js";import{Q as Pn}from"./use-key-composition-CoMUTTxZ.js";var Bn=Object.defineProperty,Qe=Object.getOwnPropertySymbols,Gt=Object.prototype.hasOwnProperty,Jt=Object.prototype.propertyIsEnumerable,Ot=(e,n,t)=>n in e?Bn(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,_e=(e,n)=>{for(var t in n||(n={}))Gt.call(n,t)&&Ot(e,t,n[t]);if(Qe)for(var t of Qe(n))Jt.call(n,t)&&Ot(e,t,n[t]);return e},Zt=(e,n)=>{var t={};for(var o in e)Gt.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(e!=null&&Qe)for(var o of Qe(e))n.indexOf(o)<0&&Jt.call(e,o)&&(t[o]=e[o]);return t};const Kt="[vue-draggable-plus]: ";function Yn(e){console.warn(Kt+e)}function Rn(e){console.error(Kt+e)}function At(e,n,t){return t>=0&&t<e.length&&e.splice(t,0,e.splice(n,1)[0]),e}function Xn(e){return e.replace(/-(\w)/g,(n,t)=>t?t.toUpperCase():"")}function Fn(e){return Object.keys(e).reduce((n,t)=>(typeof e[t]<"u"&&(n[Xn(t)]=e[t]),n),{})}function It(e,n){return Array.isArray(e)&&e.splice(n,1),e}function Nt(e,n,t){return Array.isArray(e)&&e.splice(n,0,t),e}function qn(e){return typeof e>"u"}function Hn(e){return typeof e=="string"}function Mt(e,n,t){const o=e.children[t];e.insertBefore(n,o)}function at(e){e.parentNode&&e.parentNode.removeChild(e)}function jn(e,n=document){var t;let o=null;return typeof n?.querySelector=="function"?o=(t=n?.querySelector)==null?void 0:t.call(n,e):o=document.querySelector(e),o||Yn(`Element not found: ${e}`),o}function $n(e,n,t=null){return function(...o){return e.apply(t,o),n.apply(t,o)}}function Vn(e,n){const t=_e({},e);return Object.keys(n).forEach(o=>{t[o]?t[o]=$n(e[o],n[o]):t[o]=n[o]}),t}function Wn(e){return e instanceof HTMLElement}function kt(e,n){Object.keys(e).forEach(t=>{n(t,e[t])})}function zn(e){return e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97)}const Ln=Object.assign;/**!
 * Sortable 1.15.2
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function Pt(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);n&&(o=o.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,o)}return t}function re(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Pt(Object(t),!0).forEach(function(o){Un(e,o,t[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Pt(Object(t)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(t,o))})}return e}function Ve(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Ve=function(n){return typeof n}:Ve=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},Ve(e)}function Un(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function se(){return se=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])}return e},se.apply(this,arguments)}function Qn(e,n){if(e==null)return{};var t={},o=Object.keys(e),r,a;for(a=0;a<o.length;a++)r=o[a],!(n.indexOf(r)>=0)&&(t[r]=e[r]);return t}function Gn(e,n){if(e==null)return{};var t=Qn(e,n),o,r;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)o=a[r],!(n.indexOf(o)>=0)&&Object.prototype.propertyIsEnumerable.call(e,o)&&(t[o]=e[o])}return t}var Jn="1.15.2";function le(e){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(e)}var ue=le(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),Be=le(/Edge/i),Bt=le(/firefox/i),Ie=le(/safari/i)&&!le(/chrome/i)&&!le(/android/i),en=le(/iP(ad|od|hone)/i),tn=le(/chrome/i)&&le(/android/i),nn={capture:!1,passive:!1};function S(e,n,t){e.addEventListener(n,t,!ue&&nn)}function y(e,n,t){e.removeEventListener(n,t,!ue&&nn)}function Ge(e,n){if(n){if(n[0]===">"&&(n=n.substring(1)),e)try{if(e.matches)return e.matches(n);if(e.msMatchesSelector)return e.msMatchesSelector(n);if(e.webkitMatchesSelector)return e.webkitMatchesSelector(n)}catch{return!1}return!1}}function Zn(e){return e.host&&e!==document&&e.host.nodeType?e.host:e.parentNode}function te(e,n,t,o){if(e){t=t||document;do{if(n!=null&&(n[0]===">"?e.parentNode===t&&Ge(e,n):Ge(e,n))||o&&e===t)return e;if(e===t)break}while(e=Zn(e))}return null}var Yt=/\s+/g;function W(e,n,t){if(e&&n)if(e.classList)e.classList[t?"add":"remove"](n);else{var o=(" "+e.className+" ").replace(Yt," ").replace(" "+n+" "," ");e.className=(o+(t?" "+n:"")).replace(Yt," ")}}function p(e,n,t){var o=e&&e.style;if(o){if(t===void 0)return document.defaultView&&document.defaultView.getComputedStyle?t=document.defaultView.getComputedStyle(e,""):e.currentStyle&&(t=e.currentStyle),n===void 0?t:t[n];!(n in o)&&n.indexOf("webkit")===-1&&(n="-webkit-"+n),o[n]=t+(typeof t=="string"?"":"px")}}function Ee(e,n){var t="";if(typeof e=="string")t=e;else do{var o=p(e,"transform");o&&o!=="none"&&(t=o+" "+t)}while(!n&&(e=e.parentNode));var r=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return r&&new r(t)}function on(e,n,t){if(e){var o=e.getElementsByTagName(n),r=0,a=o.length;if(t)for(;r<a;r++)t(o[r],r);return o}return[]}function oe(){var e=document.scrollingElement;return e||document.documentElement}function k(e,n,t,o,r){if(!(!e.getBoundingClientRect&&e!==window)){var a,i,s,l,u,d,f;if(e!==window&&e.parentNode&&e!==oe()?(a=e.getBoundingClientRect(),i=a.top,s=a.left,l=a.bottom,u=a.right,d=a.height,f=a.width):(i=0,s=0,l=window.innerHeight,u=window.innerWidth,d=window.innerHeight,f=window.innerWidth),(n||t)&&e!==window&&(r=r||e.parentNode,!ue))do if(r&&r.getBoundingClientRect&&(p(r,"transform")!=="none"||t&&p(r,"position")!=="static")){var _=r.getBoundingClientRect();i-=_.top+parseInt(p(r,"border-top-width")),s-=_.left+parseInt(p(r,"border-left-width")),l=i+a.height,u=s+a.width;break}while(r=r.parentNode);if(o&&e!==window){var h=Ee(r||e),E=h&&h.a,w=h&&h.d;h&&(i/=w,s/=E,f/=E,d/=w,l=i+d,u=s+f)}return{top:i,left:s,bottom:l,right:u,width:f,height:d}}}function Rt(e,n,t){for(var o=he(e,!0),r=k(e)[n];o;){var a=k(o)[t],i=void 0;if(i=r>=a,!i)return o;if(o===oe())break;o=he(o,!1)}return!1}function Ce(e,n,t,o){for(var r=0,a=0,i=e.children;a<i.length;){if(i[a].style.display!=="none"&&i[a]!==g.ghost&&(o||i[a]!==g.dragged)&&te(i[a],t.draggable,e,!1)){if(r===n)return i[a];r++}a++}return null}function Et(e,n){for(var t=e.lastElementChild;t&&(t===g.ghost||p(t,"display")==="none"||n&&!Ge(t,n));)t=t.previousElementSibling;return t||null}function G(e,n){var t=0;if(!e||!e.parentNode)return-1;for(;e=e.previousElementSibling;)e.nodeName.toUpperCase()!=="TEMPLATE"&&e!==g.clone&&(!n||Ge(e,n))&&t++;return t}function Xt(e){var n=0,t=0,o=oe();if(e)do{var r=Ee(e),a=r.a,i=r.d;n+=e.scrollLeft*a,t+=e.scrollTop*i}while(e!==o&&(e=e.parentNode));return[n,t]}function Kn(e,n){for(var t in e)if(e.hasOwnProperty(t)){for(var o in n)if(n.hasOwnProperty(o)&&n[o]===e[t][o])return Number(t)}return-1}function he(e,n){if(!e||!e.getBoundingClientRect)return oe();var t=e,o=!1;do if(t.clientWidth<t.scrollWidth||t.clientHeight<t.scrollHeight){var r=p(t);if(t.clientWidth<t.scrollWidth&&(r.overflowX=="auto"||r.overflowX=="scroll")||t.clientHeight<t.scrollHeight&&(r.overflowY=="auto"||r.overflowY=="scroll")){if(!t.getBoundingClientRect||t===document.body)return oe();if(o||n)return t;o=!0}}while(t=t.parentNode);return oe()}function eo(e,n){if(e&&n)for(var t in n)n.hasOwnProperty(t)&&(e[t]=n[t]);return e}function it(e,n){return Math.round(e.top)===Math.round(n.top)&&Math.round(e.left)===Math.round(n.left)&&Math.round(e.height)===Math.round(n.height)&&Math.round(e.width)===Math.round(n.width)}var Ne;function rn(e,n){return function(){if(!Ne){var t=arguments,o=this;t.length===1?e.call(o,t[0]):e.apply(o,t),Ne=setTimeout(function(){Ne=void 0},n)}}}function to(){clearTimeout(Ne),Ne=void 0}function an(e,n,t){e.scrollLeft+=n,e.scrollTop+=t}function ln(e){var n=window.Polymer,t=window.jQuery||window.Zepto;return n&&n.dom?n.dom(e).cloneNode(!0):t?t(e).clone(!0)[0]:e.cloneNode(!0)}function sn(e,n,t){var o={};return Array.from(e.children).forEach(function(r){var a,i,s,l;if(!(!te(r,n.draggable,e,!1)||r.animated||r===t)){var u=k(r);o.left=Math.min((a=o.left)!==null&&a!==void 0?a:1/0,u.left),o.top=Math.min((i=o.top)!==null&&i!==void 0?i:1/0,u.top),o.right=Math.max((s=o.right)!==null&&s!==void 0?s:-1/0,u.right),o.bottom=Math.max((l=o.bottom)!==null&&l!==void 0?l:-1/0,u.bottom)}}),o.width=o.right-o.left,o.height=o.bottom-o.top,o.x=o.left,o.y=o.top,o}var U="Sortable"+new Date().getTime();function no(){var e=[],n;return{captureAnimationState:function(){if(e=[],!!this.options.animation){var t=[].slice.call(this.el.children);t.forEach(function(o){if(!(p(o,"display")==="none"||o===g.ghost)){e.push({target:o,rect:k(o)});var r=re({},e[e.length-1].rect);if(o.thisAnimationDuration){var a=Ee(o,!0);a&&(r.top-=a.f,r.left-=a.e)}o.fromRect=r}})}},addAnimationState:function(t){e.push(t)},removeAnimationState:function(t){e.splice(Kn(e,{target:t}),1)},animateAll:function(t){var o=this;if(!this.options.animation){clearTimeout(n),typeof t=="function"&&t();return}var r=!1,a=0;e.forEach(function(i){var s=0,l=i.target,u=l.fromRect,d=k(l),f=l.prevFromRect,_=l.prevToRect,h=i.rect,E=Ee(l,!0);E&&(d.top-=E.f,d.left-=E.e),l.toRect=d,l.thisAnimationDuration&&it(f,d)&&!it(u,d)&&(h.top-d.top)/(h.left-d.left)===(u.top-d.top)/(u.left-d.left)&&(s=ro(h,f,_,o.options)),it(d,u)||(l.prevFromRect=u,l.prevToRect=d,s||(s=o.options.animation),o.animate(l,h,d,s)),s&&(r=!0,a=Math.max(a,s),clearTimeout(l.animationResetTimer),l.animationResetTimer=setTimeout(function(){l.animationTime=0,l.prevFromRect=null,l.fromRect=null,l.prevToRect=null,l.thisAnimationDuration=null},s),l.thisAnimationDuration=s)}),clearTimeout(n),r?n=setTimeout(function(){typeof t=="function"&&t()},a):typeof t=="function"&&t(),e=[]},animate:function(t,o,r,a){if(a){p(t,"transition",""),p(t,"transform","");var i=Ee(this.el),s=i&&i.a,l=i&&i.d,u=(o.left-r.left)/(s||1),d=(o.top-r.top)/(l||1);t.animatingX=!!u,t.animatingY=!!d,p(t,"transform","translate3d("+u+"px,"+d+"px,0)"),this.forRepaintDummy=oo(t),p(t,"transition","transform "+a+"ms"+(this.options.easing?" "+this.options.easing:"")),p(t,"transform","translate3d(0,0,0)"),typeof t.animated=="number"&&clearTimeout(t.animated),t.animated=setTimeout(function(){p(t,"transition",""),p(t,"transform",""),t.animated=!1,t.animatingX=!1,t.animatingY=!1},a)}}}}function oo(e){return e.offsetWidth}function ro(e,n,t,o){return Math.sqrt(Math.pow(n.top-e.top,2)+Math.pow(n.left-e.left,2))/Math.sqrt(Math.pow(n.top-t.top,2)+Math.pow(n.left-t.left,2))*o.animation}var be=[],lt={initializeByDefault:!0},Ye={mount:function(e){for(var n in lt)lt.hasOwnProperty(n)&&!(n in e)&&(e[n]=lt[n]);be.forEach(function(t){if(t.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),be.push(e)},pluginEvent:function(e,n,t){var o=this;this.eventCanceled=!1,t.cancel=function(){o.eventCanceled=!0};var r=e+"Global";be.forEach(function(a){n[a.pluginName]&&(n[a.pluginName][r]&&n[a.pluginName][r](re({sortable:n},t)),n.options[a.pluginName]&&n[a.pluginName][e]&&n[a.pluginName][e](re({sortable:n},t)))})},initializePlugins:function(e,n,t,o){be.forEach(function(i){var s=i.pluginName;if(!(!e.options[s]&&!i.initializeByDefault)){var l=new i(e,n,e.options);l.sortable=e,l.options=e.options,e[s]=l,se(t,l.defaults)}});for(var r in e.options)if(e.options.hasOwnProperty(r)){var a=this.modifyOption(e,r,e.options[r]);typeof a<"u"&&(e.options[r]=a)}},getEventProperties:function(e,n){var t={};return be.forEach(function(o){typeof o.eventProperties=="function"&&se(t,o.eventProperties.call(n[o.pluginName],e))}),t},modifyOption:function(e,n,t){var o;return be.forEach(function(r){e[r.pluginName]&&r.optionListeners&&typeof r.optionListeners[n]=="function"&&(o=r.optionListeners[n].call(e[r.pluginName],t))}),o}};function ao(e){var n=e.sortable,t=e.rootEl,o=e.name,r=e.targetEl,a=e.cloneEl,i=e.toEl,s=e.fromEl,l=e.oldIndex,u=e.newIndex,d=e.oldDraggableIndex,f=e.newDraggableIndex,_=e.originalEvent,h=e.putSortable,E=e.extraEventProperties;if(n=n||t&&t[U],!!n){var w,V=n.options,I="on"+o.charAt(0).toUpperCase()+o.substr(1);window.CustomEvent&&!ue&&!Be?w=new CustomEvent(o,{bubbles:!0,cancelable:!0}):(w=document.createEvent("Event"),w.initEvent(o,!0,!0)),w.to=i||t,w.from=s||t,w.item=r||t,w.clone=a,w.oldIndex=l,w.newIndex=u,w.oldDraggableIndex=d,w.newDraggableIndex=f,w.originalEvent=_,w.pullMode=h?h.lastPutMode:void 0;var Z=re(re({},E),Ye.getEventProperties(o,n));for(var P in Z)w[P]=Z[P];t&&t.dispatchEvent(w),V[I]&&V[I].call(n,w)}}var io=["evt"],j=function(e,n){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},o=t.evt,r=Gn(t,io);Ye.pluginEvent.bind(g)(e,n,re({dragEl:c,parentEl:x,ghostEl:m,rootEl:C,nextEl:ve,lastDownEl:We,cloneEl:T,cloneHidden:fe,dragStarted:xe,putSortable:X,activeSortable:g.active,originalEvent:o,oldIndex:De,oldDraggableIndex:Me,newIndex:L,newDraggableIndex:de,hideGhostForTarget:fn,unhideGhostForTarget:hn,cloneNowHidden:function(){fe=!0},cloneNowShown:function(){fe=!1},dispatchSortableEvent:function(a){q({sortable:n,name:a,originalEvent:o})}},r))};function q(e){ao(re({putSortable:X,cloneEl:T,targetEl:c,rootEl:C,oldIndex:De,oldDraggableIndex:Me,newIndex:L,newDraggableIndex:de},e))}var c,x,m,C,ve,We,T,fe,De,L,Me,de,qe,X,Se=!1,Je=!1,Ze=[],ge,K,st,ut,Ft,qt,xe,ye,ke,Pe=!1,He=!1,ze,F,ct=[],bt=!1,Ke=[],tt=typeof document<"u",je=en,Ht=Be||ue?"cssFloat":"float",lo=tt&&!tn&&!en&&"draggable"in document.createElement("div"),un=function(){if(tt){if(ue)return!1;var e=document.createElement("x");return e.style.cssText="pointer-events:auto",e.style.pointerEvents==="auto"}}(),cn=function(e,n){var t=p(e),o=parseInt(t.width)-parseInt(t.paddingLeft)-parseInt(t.paddingRight)-parseInt(t.borderLeftWidth)-parseInt(t.borderRightWidth),r=Ce(e,0,n),a=Ce(e,1,n),i=r&&p(r),s=a&&p(a),l=i&&parseInt(i.marginLeft)+parseInt(i.marginRight)+k(r).width,u=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+k(a).width;if(t.display==="flex")return t.flexDirection==="column"||t.flexDirection==="column-reverse"?"vertical":"horizontal";if(t.display==="grid")return t.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(r&&i.float&&i.float!=="none"){var d=i.float==="left"?"left":"right";return a&&(s.clear==="both"||s.clear===d)?"vertical":"horizontal"}return r&&(i.display==="block"||i.display==="flex"||i.display==="table"||i.display==="grid"||l>=o&&t[Ht]==="none"||a&&t[Ht]==="none"&&l+u>o)?"vertical":"horizontal"},so=function(e,n,t){var o=t?e.left:e.top,r=t?e.right:e.bottom,a=t?e.width:e.height,i=t?n.left:n.top,s=t?n.right:n.bottom,l=t?n.width:n.height;return o===i||r===s||o+a/2===i+l/2},uo=function(e,n){var t;return Ze.some(function(o){var r=o[U].options.emptyInsertThreshold;if(!(!r||Et(o))){var a=k(o),i=e>=a.left-r&&e<=a.right+r,s=n>=a.top-r&&n<=a.bottom+r;if(i&&s)return t=o}}),t},dn=function(e){function n(r,a){return function(i,s,l,u){var d=i.options.group.name&&s.options.group.name&&i.options.group.name===s.options.group.name;if(r==null&&(a||d))return!0;if(r==null||r===!1)return!1;if(a&&r==="clone")return r;if(typeof r=="function")return n(r(i,s,l,u),a)(i,s,l,u);var f=(a?i:s).options.group.name;return r===!0||typeof r=="string"&&r===f||r.join&&r.indexOf(f)>-1}}var t={},o=e.group;(!o||Ve(o)!="object")&&(o={name:o}),t.name=o.name,t.checkPull=n(o.pull,!0),t.checkPut=n(o.put),t.revertClone=o.revertClone,e.group=t},fn=function(){!un&&m&&p(m,"display","none")},hn=function(){!un&&m&&p(m,"display","")};tt&&!tn&&document.addEventListener("click",function(e){if(Je)return e.preventDefault(),e.stopPropagation&&e.stopPropagation(),e.stopImmediatePropagation&&e.stopImmediatePropagation(),Je=!1,!1},!0);var me=function(e){if(c){e=e.touches?e.touches[0]:e;var n=uo(e.clientX,e.clientY);if(n){var t={};for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o]);t.target=t.rootEl=n,t.preventDefault=void 0,t.stopPropagation=void 0,n[U]._onDragOver(t)}}},co=function(e){c&&c.parentNode[U]._isOutsideThisEl(e.target)};function g(e,n){if(!(e&&e.nodeType&&e.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(e));this.el=e,this.options=n=se({},n),e[U]=this;var t={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(e.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return cn(e,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(a,i){a.setData("Text",i.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:g.supportPointer!==!1&&"PointerEvent"in window&&!Ie,emptyInsertThreshold:5};Ye.initializePlugins(this,e,t);for(var o in t)!(o in n)&&(n[o]=t[o]);dn(n);for(var r in this)r.charAt(0)==="_"&&typeof this[r]=="function"&&(this[r]=this[r].bind(this));this.nativeDraggable=n.forceFallback?!1:lo,this.nativeDraggable&&(this.options.touchStartThreshold=1),n.supportPointer?S(e,"pointerdown",this._onTapStart):(S(e,"mousedown",this._onTapStart),S(e,"touchstart",this._onTapStart)),this.nativeDraggable&&(S(e,"dragover",this),S(e,"dragenter",this)),Ze.push(this.el),n.store&&n.store.get&&this.sort(n.store.get(this)||[]),se(this,no())}g.prototype={constructor:g,_isOutsideThisEl:function(e){!this.el.contains(e)&&e!==this.el&&(ye=null)},_getDirection:function(e,n){return typeof this.options.direction=="function"?this.options.direction.call(this,e,n,c):this.options.direction},_onTapStart:function(e){if(e.cancelable){var n=this,t=this.el,o=this.options,r=o.preventOnFilter,a=e.type,i=e.touches&&e.touches[0]||e.pointerType&&e.pointerType==="touch"&&e,s=(i||e).target,l=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||s,u=o.filter;if(yo(t),!c&&!(/mousedown|pointerdown/.test(a)&&e.button!==0||o.disabled)&&!l.isContentEditable&&!(!this.nativeDraggable&&Ie&&s&&s.tagName.toUpperCase()==="SELECT")&&(s=te(s,o.draggable,t,!1),!(s&&s.animated)&&We!==s)){if(De=G(s),Me=G(s,o.draggable),typeof u=="function"){if(u.call(this,e,s,this)){q({sortable:n,rootEl:l,name:"filter",targetEl:s,toEl:t,fromEl:t}),j("filter",n,{evt:e}),r&&e.cancelable&&e.preventDefault();return}}else if(u&&(u=u.split(",").some(function(d){if(d=te(l,d.trim(),t,!1),d)return q({sortable:n,rootEl:d,name:"filter",targetEl:s,fromEl:t,toEl:t}),j("filter",n,{evt:e}),!0}),u)){r&&e.cancelable&&e.preventDefault();return}o.handle&&!te(l,o.handle,t,!1)||this._prepareDragStart(e,i,s)}}},_prepareDragStart:function(e,n,t){var o=this,r=o.el,a=o.options,i=r.ownerDocument,s;if(t&&!c&&t.parentNode===r){var l=k(t);if(C=r,c=t,x=c.parentNode,ve=c.nextSibling,We=t,qe=a.group,g.dragged=c,ge={target:c,clientX:(n||e).clientX,clientY:(n||e).clientY},Ft=ge.clientX-l.left,qt=ge.clientY-l.top,this._lastX=(n||e).clientX,this._lastY=(n||e).clientY,c.style["will-change"]="all",s=function(){if(j("delayEnded",o,{evt:e}),g.eventCanceled){o._onDrop();return}o._disableDelayedDragEvents(),!Bt&&o.nativeDraggable&&(c.draggable=!0),o._triggerDragStart(e,n),q({sortable:o,name:"choose",originalEvent:e}),W(c,a.chosenClass,!0)},a.ignore.split(",").forEach(function(u){on(c,u.trim(),dt)}),S(i,"dragover",me),S(i,"mousemove",me),S(i,"touchmove",me),S(i,"mouseup",o._onDrop),S(i,"touchend",o._onDrop),S(i,"touchcancel",o._onDrop),Bt&&this.nativeDraggable&&(this.options.touchStartThreshold=4,c.draggable=!0),j("delayStart",this,{evt:e}),a.delay&&(!a.delayOnTouchOnly||n)&&(!this.nativeDraggable||!(Be||ue))){if(g.eventCanceled){this._onDrop();return}S(i,"mouseup",o._disableDelayedDrag),S(i,"touchend",o._disableDelayedDrag),S(i,"touchcancel",o._disableDelayedDrag),S(i,"mousemove",o._delayedDragTouchMoveHandler),S(i,"touchmove",o._delayedDragTouchMoveHandler),a.supportPointer&&S(i,"pointermove",o._delayedDragTouchMoveHandler),o._dragStartTimer=setTimeout(s,a.delay)}else s()}},_delayedDragTouchMoveHandler:function(e){var n=e.touches?e.touches[0]:e;Math.max(Math.abs(n.clientX-this._lastX),Math.abs(n.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){c&&dt(c),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;y(e,"mouseup",this._disableDelayedDrag),y(e,"touchend",this._disableDelayedDrag),y(e,"touchcancel",this._disableDelayedDrag),y(e,"mousemove",this._delayedDragTouchMoveHandler),y(e,"touchmove",this._delayedDragTouchMoveHandler),y(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,n){n=n||e.pointerType=="touch"&&e,!this.nativeDraggable||n?this.options.supportPointer?S(document,"pointermove",this._onTouchMove):n?S(document,"touchmove",this._onTouchMove):S(document,"mousemove",this._onTouchMove):(S(c,"dragend",this),S(C,"dragstart",this._onDragStart));try{document.selection?Le(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(e,n){if(Se=!1,C&&c){j("dragStarted",this,{evt:n}),this.nativeDraggable&&S(document,"dragover",co);var t=this.options;!e&&W(c,t.dragClass,!1),W(c,t.ghostClass,!0),g.active=this,e&&this._appendGhost(),q({sortable:this,name:"start",originalEvent:n})}else this._nulling()},_emulateDragOver:function(){if(K){this._lastX=K.clientX,this._lastY=K.clientY,fn();for(var e=document.elementFromPoint(K.clientX,K.clientY),n=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(K.clientX,K.clientY),e!==n);)n=e;if(c.parentNode[U]._isOutsideThisEl(e),n)do{if(n[U]){var t=void 0;if(t=n[U]._onDragOver({clientX:K.clientX,clientY:K.clientY,target:e,rootEl:n}),t&&!this.options.dragoverBubble)break}e=n}while(n=n.parentNode);hn()}},_onTouchMove:function(e){if(ge){var n=this.options,t=n.fallbackTolerance,o=n.fallbackOffset,r=e.touches?e.touches[0]:e,a=m&&Ee(m,!0),i=m&&a&&a.a,s=m&&a&&a.d,l=je&&F&&Xt(F),u=(r.clientX-ge.clientX+o.x)/(i||1)+(l?l[0]-ct[0]:0)/(i||1),d=(r.clientY-ge.clientY+o.y)/(s||1)+(l?l[1]-ct[1]:0)/(s||1);if(!g.active&&!Se){if(t&&Math.max(Math.abs(r.clientX-this._lastX),Math.abs(r.clientY-this._lastY))<t)return;this._onDragStart(e,!0)}if(m){a?(a.e+=u-(st||0),a.f+=d-(ut||0)):a={a:1,b:0,c:0,d:1,e:u,f:d};var f="matrix(".concat(a.a,",").concat(a.b,",").concat(a.c,",").concat(a.d,",").concat(a.e,",").concat(a.f,")");p(m,"webkitTransform",f),p(m,"mozTransform",f),p(m,"msTransform",f),p(m,"transform",f),st=u,ut=d,K=r}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!m){var e=this.options.fallbackOnBody?document.body:C,n=k(c,!0,je,!0,e),t=this.options;if(je){for(F=e;p(F,"position")==="static"&&p(F,"transform")==="none"&&F!==document;)F=F.parentNode;F!==document.body&&F!==document.documentElement?(F===document&&(F=oe()),n.top+=F.scrollTop,n.left+=F.scrollLeft):F=oe(),ct=Xt(F)}m=c.cloneNode(!0),W(m,t.ghostClass,!1),W(m,t.fallbackClass,!0),W(m,t.dragClass,!0),p(m,"transition",""),p(m,"transform",""),p(m,"box-sizing","border-box"),p(m,"margin",0),p(m,"top",n.top),p(m,"left",n.left),p(m,"width",n.width),p(m,"height",n.height),p(m,"opacity","0.8"),p(m,"position",je?"absolute":"fixed"),p(m,"zIndex","100000"),p(m,"pointerEvents","none"),g.ghost=m,e.appendChild(m),p(m,"transform-origin",Ft/parseInt(m.style.width)*100+"% "+qt/parseInt(m.style.height)*100+"%")}},_onDragStart:function(e,n){var t=this,o=e.dataTransfer,r=t.options;if(j("dragStart",this,{evt:e}),g.eventCanceled){this._onDrop();return}j("setupClone",this),g.eventCanceled||(T=ln(c),T.removeAttribute("id"),T.draggable=!1,T.style["will-change"]="",this._hideClone(),W(T,this.options.chosenClass,!1),g.clone=T),t.cloneId=Le(function(){j("clone",t),!g.eventCanceled&&(t.options.removeCloneOnHide||C.insertBefore(T,c),t._hideClone(),q({sortable:t,name:"clone"}))}),!n&&W(c,r.dragClass,!0),n?(Je=!0,t._loopId=setInterval(t._emulateDragOver,50)):(y(document,"mouseup",t._onDrop),y(document,"touchend",t._onDrop),y(document,"touchcancel",t._onDrop),o&&(o.effectAllowed="move",r.setData&&r.setData.call(t,o,c)),S(document,"drop",t),p(c,"transform","translateZ(0)")),Se=!0,t._dragStartId=Le(t._dragStarted.bind(t,n,e)),S(document,"selectstart",t),xe=!0,Ie&&p(document.body,"user-select","none")},_onDragOver:function(e){var n=this.el,t=e.target,o,r,a,i=this.options,s=i.group,l=g.active,u=qe===s,d=i.sort,f=X||l,_,h=this,E=!1;if(bt)return;function w(ie,nt){j(ie,h,re({evt:e,isOwner:u,axis:_?"vertical":"horizontal",revert:a,dragRect:o,targetRect:r,canSort:d,fromSortable:f,target:t,completed:I,onMove:function(xt,vn){return $e(C,n,c,o,xt,k(xt),e,vn)},changed:Z},nt))}function V(){w("dragOverAnimationCapture"),h.captureAnimationState(),h!==f&&f.captureAnimationState()}function I(ie){return w("dragOverCompleted",{insertion:ie}),ie&&(u?l._hideClone():l._showClone(h),h!==f&&(W(c,X?X.options.ghostClass:l.options.ghostClass,!1),W(c,i.ghostClass,!0)),X!==h&&h!==g.active?X=h:h===g.active&&X&&(X=null),f===h&&(h._ignoreWhileAnimating=t),h.animateAll(function(){w("dragOverAnimationComplete"),h._ignoreWhileAnimating=null}),h!==f&&(f.animateAll(),f._ignoreWhileAnimating=null)),(t===c&&!c.animated||t===n&&!t.animated)&&(ye=null),!i.dragoverBubble&&!e.rootEl&&t!==document&&(c.parentNode[U]._isOutsideThisEl(e.target),!ie&&me(e)),!i.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),E=!0}function Z(){L=G(c),de=G(c,i.draggable),q({sortable:h,name:"change",toEl:n,newIndex:L,newDraggableIndex:de,originalEvent:e})}if(e.preventDefault!==void 0&&e.cancelable&&e.preventDefault(),t=te(t,i.draggable,n,!0),w("dragOver"),g.eventCanceled)return E;if(c.contains(e.target)||t.animated&&t.animatingX&&t.animatingY||h._ignoreWhileAnimating===t)return I(!1);if(Je=!1,l&&!i.disabled&&(u?d||(a=x!==C):X===this||(this.lastPutMode=qe.checkPull(this,l,c,e))&&s.checkPut(this,l,c,e))){if(_=this._getDirection(e,t)==="vertical",o=k(c),w("dragOverValid"),g.eventCanceled)return E;if(a)return x=C,V(),this._hideClone(),w("revert"),g.eventCanceled||(ve?C.insertBefore(c,ve):C.appendChild(c)),I(!0);var P=Et(n,i.draggable);if(!P||go(e,_,this)&&!P.animated){if(P===c)return I(!1);if(P&&n===e.target&&(t=P),t&&(r=k(t)),$e(C,n,c,o,t,r,e,!!t)!==!1)return V(),P&&P.nextSibling?n.insertBefore(c,P.nextSibling):n.appendChild(c),x=n,Z(),I(!0)}else if(P&&po(e,_,this)){var ne=Ce(n,0,i,!0);if(ne===c)return I(!1);if(t=ne,r=k(t),$e(C,n,c,o,t,r,e,!1)!==!1)return V(),n.insertBefore(c,ne),x=n,Z(),I(!0)}else if(t.parentNode===n){r=k(t);var Y=0,ae,pe=c.parentNode!==n,v=!so(c.animated&&c.toRect||o,t.animated&&t.toRect||r,_),b=_?"top":"left",N=Rt(t,"top","top")||Rt(c,"top","top"),H=N?N.scrollTop:void 0;ye!==t&&(ae=r[b],Pe=!1,He=!v&&i.invertSwap||pe),Y=mo(e,t,r,_,v?1:i.swapThreshold,i.invertedSwapThreshold==null?i.swapThreshold:i.invertedSwapThreshold,He,ye===t);var D;if(Y!==0){var A=G(c);do A-=Y,D=x.children[A];while(D&&(p(D,"display")==="none"||D===m))}if(Y===0||D===t)return I(!1);ye=t,ke=Y;var R=t.nextElementSibling,B=!1;B=Y===1;var Q=$e(C,n,c,o,t,r,e,B);if(Q!==!1)return(Q===1||Q===-1)&&(B=Q===1),bt=!0,setTimeout(ho,30),V(),B&&!R?n.appendChild(c):t.parentNode.insertBefore(c,B?R:t),N&&an(N,0,H-N.scrollTop),x=c.parentNode,ae!==void 0&&!He&&(ze=Math.abs(ae-k(t)[b])),Z(),I(!0)}if(n.contains(c))return I(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){y(document,"mousemove",this._onTouchMove),y(document,"touchmove",this._onTouchMove),y(document,"pointermove",this._onTouchMove),y(document,"dragover",me),y(document,"mousemove",me),y(document,"touchmove",me)},_offUpEvents:function(){var e=this.el.ownerDocument;y(e,"mouseup",this._onDrop),y(e,"touchend",this._onDrop),y(e,"pointerup",this._onDrop),y(e,"touchcancel",this._onDrop),y(document,"selectstart",this)},_onDrop:function(e){var n=this.el,t=this.options;if(L=G(c),de=G(c,t.draggable),j("drop",this,{evt:e}),x=c&&c.parentNode,L=G(c),de=G(c,t.draggable),g.eventCanceled){this._nulling();return}Se=!1,He=!1,Pe=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),yt(this.cloneId),yt(this._dragStartId),this.nativeDraggable&&(y(document,"drop",this),y(n,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Ie&&p(document.body,"user-select",""),p(c,"transform",""),e&&(xe&&(e.cancelable&&e.preventDefault(),!t.dropBubble&&e.stopPropagation()),m&&m.parentNode&&m.parentNode.removeChild(m),(C===x||X&&X.lastPutMode!=="clone")&&T&&T.parentNode&&T.parentNode.removeChild(T),c&&(this.nativeDraggable&&y(c,"dragend",this),dt(c),c.style["will-change"]="",xe&&!Se&&W(c,X?X.options.ghostClass:this.options.ghostClass,!1),W(c,this.options.chosenClass,!1),q({sortable:this,name:"unchoose",toEl:x,newIndex:null,newDraggableIndex:null,originalEvent:e}),C!==x?(L>=0&&(q({rootEl:x,name:"add",toEl:x,fromEl:C,originalEvent:e}),q({sortable:this,name:"remove",toEl:x,originalEvent:e}),q({rootEl:x,name:"sort",toEl:x,fromEl:C,originalEvent:e}),q({sortable:this,name:"sort",toEl:x,originalEvent:e})),X&&X.save()):L!==De&&L>=0&&(q({sortable:this,name:"update",toEl:x,originalEvent:e}),q({sortable:this,name:"sort",toEl:x,originalEvent:e})),g.active&&((L==null||L===-1)&&(L=De,de=Me),q({sortable:this,name:"end",toEl:x,originalEvent:e}),this.save()))),this._nulling()},_nulling:function(){j("nulling",this),C=c=x=m=ve=T=We=fe=ge=K=xe=L=de=De=Me=ye=ke=X=qe=g.dragged=g.ghost=g.clone=g.active=null,Ke.forEach(function(e){e.checked=!0}),Ke.length=st=ut=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":c&&(this._onDragOver(e),fo(e));break;case"selectstart":e.preventDefault();break}},toArray:function(){for(var e=[],n,t=this.el.children,o=0,r=t.length,a=this.options;o<r;o++)n=t[o],te(n,a.draggable,this.el,!1)&&e.push(n.getAttribute(a.dataIdAttr)||bo(n));return e},sort:function(e,n){var t={},o=this.el;this.toArray().forEach(function(r,a){var i=o.children[a];te(i,this.options.draggable,o,!1)&&(t[r]=i)},this),n&&this.captureAnimationState(),e.forEach(function(r){t[r]&&(o.removeChild(t[r]),o.appendChild(t[r]))}),n&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,n){return te(e,n||this.options.draggable,this.el,!1)},option:function(e,n){var t=this.options;if(n===void 0)return t[e];var o=Ye.modifyOption(this,e,n);typeof o<"u"?t[e]=o:t[e]=n,e==="group"&&dn(t)},destroy:function(){j("destroy",this);var e=this.el;e[U]=null,y(e,"mousedown",this._onTapStart),y(e,"touchstart",this._onTapStart),y(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(y(e,"dragover",this),y(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(n){n.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Ze.splice(Ze.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!fe){if(j("hideClone",this),g.eventCanceled)return;p(T,"display","none"),this.options.removeCloneOnHide&&T.parentNode&&T.parentNode.removeChild(T),fe=!0}},_showClone:function(e){if(e.lastPutMode!=="clone"){this._hideClone();return}if(fe){if(j("showClone",this),g.eventCanceled)return;c.parentNode==C&&!this.options.group.revertClone?C.insertBefore(T,c):ve?C.insertBefore(T,ve):C.appendChild(T),this.options.group.revertClone&&this.animate(c,T),p(T,"display",""),fe=!1}}};function fo(e){e.dataTransfer&&(e.dataTransfer.dropEffect="move"),e.cancelable&&e.preventDefault()}function $e(e,n,t,o,r,a,i,s){var l,u=e[U],d=u.options.onMove,f;return window.CustomEvent&&!ue&&!Be?l=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(l=document.createEvent("Event"),l.initEvent("move",!0,!0)),l.to=n,l.from=e,l.dragged=t,l.draggedRect=o,l.related=r||n,l.relatedRect=a||k(n),l.willInsertAfter=s,l.originalEvent=i,e.dispatchEvent(l),d&&(f=d.call(u,l,i)),f}function dt(e){e.draggable=!1}function ho(){bt=!1}function po(e,n,t){var o=k(Ce(t.el,0,t.options,!0)),r=sn(t.el,t.options,m),a=10;return n?e.clientX<r.left-a||e.clientY<o.top&&e.clientX<o.right:e.clientY<r.top-a||e.clientY<o.bottom&&e.clientX<o.left}function go(e,n,t){var o=k(Et(t.el,t.options.draggable)),r=sn(t.el,t.options,m),a=10;return n?e.clientX>r.right+a||e.clientY>o.bottom&&e.clientX>o.left:e.clientY>r.bottom+a||e.clientX>o.right&&e.clientY>o.top}function mo(e,n,t,o,r,a,i,s){var l=o?e.clientY:e.clientX,u=o?t.height:t.width,d=o?t.top:t.left,f=o?t.bottom:t.right,_=!1;if(!i){if(s&&ze<u*r){if(!Pe&&(ke===1?l>d+u*a/2:l<f-u*a/2)&&(Pe=!0),Pe)_=!0;else if(ke===1?l<d+ze:l>f-ze)return-ke}else if(l>d+u*(1-r)/2&&l<f-u*(1-r)/2)return vo(n)}return _=_||i,_&&(l<d+u*a/2||l>f-u*a/2)?l>d+u/2?1:-1:0}function vo(e){return G(c)<G(e)?1:-1}function bo(e){for(var n=e.tagName+e.className+e.src+e.href+e.textContent,t=n.length,o=0;t--;)o+=n.charCodeAt(t);return o.toString(36)}function yo(e){Ke.length=0;for(var n=e.getElementsByTagName("input"),t=n.length;t--;){var o=n[t];o.checked&&Ke.push(o)}}function Le(e){return setTimeout(e,0)}function yt(e){return clearTimeout(e)}tt&&S(document,"touchmove",function(e){(g.active||Se)&&e.cancelable&&e.preventDefault()});g.utils={on:S,off:y,css:p,find:on,is:function(e,n){return!!te(e,n,e,!1)},extend:eo,throttle:rn,closest:te,toggleClass:W,clone:ln,index:G,nextTick:Le,cancelNextTick:yt,detectDirection:cn,getChild:Ce};g.get=function(e){return e[U]};g.mount=function(){for(var e=arguments.length,n=new Array(e),t=0;t<e;t++)n[t]=arguments[t];n[0].constructor===Array&&(n=n[0]),n.forEach(function(o){if(!o.prototype||!o.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(o));o.utils&&(g.utils=re(re({},g.utils),o.utils)),Ye.mount(o)})};g.create=function(e,n){return new g(e,n)};g.version=Jn;var M=[],Oe,wt,St=!1,ft,ht,et,Ae;function wo(){function e(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var n in this)n.charAt(0)==="_"&&typeof this[n]=="function"&&(this[n]=this[n].bind(this))}return e.prototype={dragStarted:function(n){var t=n.originalEvent;this.sortable.nativeDraggable?S(document,"dragover",this._handleAutoScroll):this.options.supportPointer?S(document,"pointermove",this._handleFallbackAutoScroll):t.touches?S(document,"touchmove",this._handleFallbackAutoScroll):S(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(n){var t=n.originalEvent;!this.options.dragOverBubble&&!t.rootEl&&this._handleAutoScroll(t)},drop:function(){this.sortable.nativeDraggable?y(document,"dragover",this._handleAutoScroll):(y(document,"pointermove",this._handleFallbackAutoScroll),y(document,"touchmove",this._handleFallbackAutoScroll),y(document,"mousemove",this._handleFallbackAutoScroll)),jt(),Ue(),to()},nulling:function(){et=wt=Oe=St=Ae=ft=ht=null,M.length=0},_handleFallbackAutoScroll:function(n){this._handleAutoScroll(n,!0)},_handleAutoScroll:function(n,t){var o=this,r=(n.touches?n.touches[0]:n).clientX,a=(n.touches?n.touches[0]:n).clientY,i=document.elementFromPoint(r,a);if(et=n,t||this.options.forceAutoScrollFallback||Be||ue||Ie){pt(n,this.options,i,t);var s=he(i,!0);St&&(!Ae||r!==ft||a!==ht)&&(Ae&&jt(),Ae=setInterval(function(){var l=he(document.elementFromPoint(r,a),!0);l!==s&&(s=l,Ue()),pt(n,o.options,l,t)},10),ft=r,ht=a)}else{if(!this.options.bubbleScroll||he(i,!0)===oe()){Ue();return}pt(n,this.options,he(i,!1),!1)}}},se(e,{pluginName:"scroll",initializeByDefault:!0})}function Ue(){M.forEach(function(e){clearInterval(e.pid)}),M=[]}function jt(){clearInterval(Ae)}var pt=rn(function(e,n,t,o){if(n.scroll){var r=(e.touches?e.touches[0]:e).clientX,a=(e.touches?e.touches[0]:e).clientY,i=n.scrollSensitivity,s=n.scrollSpeed,l=oe(),u=!1,d;wt!==t&&(wt=t,Ue(),Oe=n.scroll,d=n.scrollFn,Oe===!0&&(Oe=he(t,!0)));var f=0,_=Oe;do{var h=_,E=k(h),w=E.top,V=E.bottom,I=E.left,Z=E.right,P=E.width,ne=E.height,Y=void 0,ae=void 0,pe=h.scrollWidth,v=h.scrollHeight,b=p(h),N=h.scrollLeft,H=h.scrollTop;h===l?(Y=P<pe&&(b.overflowX==="auto"||b.overflowX==="scroll"||b.overflowX==="visible"),ae=ne<v&&(b.overflowY==="auto"||b.overflowY==="scroll"||b.overflowY==="visible")):(Y=P<pe&&(b.overflowX==="auto"||b.overflowX==="scroll"),ae=ne<v&&(b.overflowY==="auto"||b.overflowY==="scroll"));var D=Y&&(Math.abs(Z-r)<=i&&N+P<pe)-(Math.abs(I-r)<=i&&!!N),A=ae&&(Math.abs(V-a)<=i&&H+ne<v)-(Math.abs(w-a)<=i&&!!H);if(!M[f])for(var R=0;R<=f;R++)M[R]||(M[R]={});(M[f].vx!=D||M[f].vy!=A||M[f].el!==h)&&(M[f].el=h,M[f].vx=D,M[f].vy=A,clearInterval(M[f].pid),(D!=0||A!=0)&&(u=!0,M[f].pid=setInterval((function(){o&&this.layer===0&&g.active._onTouchMove(et);var B=M[this.layer].vy?M[this.layer].vy*s:0,Q=M[this.layer].vx?M[this.layer].vx*s:0;typeof d=="function"&&d.call(g.dragged.parentNode[U],Q,B,e,et,M[this.layer].el)!=="continue"||an(M[this.layer].el,Q,B)}).bind({layer:f}),24))),f++}while(n.bubbleScroll&&_!==l&&(_=he(_,!1)));St=u}},30),pn=function(e){var n=e.originalEvent,t=e.putSortable,o=e.dragEl,r=e.activeSortable,a=e.dispatchSortableEvent,i=e.hideGhostForTarget,s=e.unhideGhostForTarget;if(n){var l=t||r;i();var u=n.changedTouches&&n.changedTouches.length?n.changedTouches[0]:n,d=document.elementFromPoint(u.clientX,u.clientY);s(),l&&!l.el.contains(d)&&(a("spill"),this.onSpill({dragEl:o,putSortable:t}))}};function Ct(){}Ct.prototype={startIndex:null,dragStart:function(e){var n=e.oldDraggableIndex;this.startIndex=n},onSpill:function(e){var n=e.dragEl,t=e.putSortable;this.sortable.captureAnimationState(),t&&t.captureAnimationState();var o=Ce(this.sortable.el,this.startIndex,this.options);o?this.sortable.el.insertBefore(n,o):this.sortable.el.appendChild(n),this.sortable.animateAll(),t&&t.animateAll()},drop:pn};se(Ct,{pluginName:"revertOnSpill"});function Tt(){}Tt.prototype={onSpill:function(e){var n=e.dragEl,t=e.putSortable,o=t||this.sortable;o.captureAnimationState(),n.parentNode&&n.parentNode.removeChild(n),o.animateAll()},drop:pn};se(Tt,{pluginName:"removeOnSpill"});g.mount(new wo);g.mount(Tt,Ct);function So(e){return e==null?e:JSON.parse(JSON.stringify(e))}function _o(e){Dt()&&xn(e)}function Do(e){Dt()?Tn(e):Lt(e)}let gn=null,mn=null;function $t(e=null,n=null){gn=e,mn=n}function Eo(){return{data:gn,clonedData:mn}}const Vt=Symbol("cloneElement");function Co(...e){var n,t;const o=(n=Dt())==null?void 0:n.proxy;let r=null;const a=e[0];let[,i,s]=e;Array.isArray($(i))||(s=i,i=null);let l=null;const{immediate:u=!0,clone:d=So,customUpdate:f}=(t=$(s))!=null?t:{};function _(v){var b;const{from:N,oldIndex:H,item:D}=v;r=Array.from(N.childNodes);const A=$((b=$(i))==null?void 0:b[H]),R=d(A);$t(A,R),D[Vt]=R}function h(v){const b=v.item[Vt];if(!qn(b)){if(at(v.item),ot(i)){const N=[...$(i)];i.value=Nt(N,v.newDraggableIndex,b);return}Nt($(i),v.newDraggableIndex,b)}}function E(v){const{from:b,item:N,oldIndex:H,oldDraggableIndex:D,pullMode:A,clone:R}=v;if(Mt(b,N,H),A==="clone"){at(R);return}if(ot(i)){const B=[...$(i)];i.value=It(B,D);return}It($(i),D)}function w(v){if(f){f(v);return}const{from:b,item:N,oldIndex:H,oldDraggableIndex:D,newDraggableIndex:A}=v;if(at(N),Mt(b,N,H),ot(i)){const R=[...$(i)];i.value=At(R,D,A);return}At($(i),D,A)}function V(v){const{newIndex:b,oldIndex:N,from:H,to:D}=v;let A=null;const R=b===N&&H===D;try{if(R){let B=null;r?.some((Q,ie)=>{if(B&&r?.length!==D.childNodes.length)return H.insertBefore(B,Q.nextSibling),!0;const nt=D.childNodes[ie];B=D?.replaceChild(Q,nt)})}}catch(B){A=B}finally{r=null}Lt(()=>{if($t(),A)throw A})}const I={onUpdate:w,onStart:_,onAdd:h,onRemove:E,onEnd:V};function Z(v){const b=$(a);return v||(v=Hn(b)?jn(b,o?.$el):b),v&&!Wn(v)&&(v=v.$el),v||Rn("Root element not found"),v}function P(){var v;const b=(v=$(s))!=null?v:{},{immediate:N,clone:H}=b,D=Zt(b,["immediate","clone"]);return kt(D,(A,R)=>{zn(A)&&(D[A]=(B,...Q)=>{const ie=Eo();return Ln(B,ie),R(B,...Q)})}),Vn(i===null?{}:I,D)}const ne=v=>{v=Z(v),l&&Y.destroy(),l=new g(v,P())};Cn(()=>s,()=>{l&&kt(P(),(v,b)=>{l?.option(v,b)})},{deep:!0});const Y={option:(v,b)=>l?.option(v,b),destroy:()=>{l?.destroy(),l=null},save:()=>l?.save(),toArray:()=>l?.toArray(),closest:(...v)=>l?.closest(...v)},ae=()=>Y?.option("disabled",!0),pe=()=>Y?.option("disabled",!1);return Do(()=>{u&&ne()}),_o(Y.destroy),_e({start:ne,pause:ae,resume:pe},Y)}const _t=["update","start","add","remove","choose","unchoose","end","sort","filter","clone","move","change"],To=["clone","animation","ghostClass","group","sort","disabled","store","handle","draggable","swapThreshold","invertSwap","invertedSwapThreshold","removeCloneOnHide","direction","chosenClass","dragClass","ignore","filter","preventOnFilter","easing","setData","dropBubble","dragoverBubble","dataIdAttr","delay","delayOnTouchOnly","touchStartThreshold","forceFallback","fallbackClass","fallbackOnBody","fallbackTolerance","fallbackOffset","supportPointer","emptyInsertThreshold","scroll","forceAutoScrollFallback","scrollSensitivity","scrollSpeed","bubbleScroll","modelValue","tag","target","customUpdate",..._t.map(e=>`on${e.replace(/^\S/,n=>n.toUpperCase())}`)],xo=wn({name:"VueDraggable",model:{prop:"modelValue",event:"update:modelValue"},props:To,emits:["update:modelValue",..._t],setup(e,{slots:n,emit:t,expose:o,attrs:r}){const a=_t.reduce((d,f)=>{const _=`on${f.replace(/^\S/,h=>h.toUpperCase())}`;return d[_]=(...h)=>t(f,...h),d},{}),i=gt(()=>{const d=Sn(e),{modelValue:f}=d,_=Zt(d,["modelValue"]),h=Object.entries(_).reduce((E,[w,V])=>{const I=$(V);return I!==void 0&&(E[w]=I),E},{});return _e(_e({},a),Fn(_e(_e({},r),h)))}),s=gt({get:()=>e.modelValue,set:d=>t("update:modelValue",d)}),l=_n(),u=Dn(Co(e.target||l,s,i));return o(u),()=>{var d;return En(e.tag||"div",{ref:l},(d=n?.default)==null?void 0:d.call(n,u))}}}),Oo={__name:"AqDragHandle",props:{displayNone:{type:Boolean,default:!1}},setup(e){const n=e;return(t,o)=>(ee(),ce("div",{class:On(["aq-drag-handle",{"aq-drag-disable":n.displayNone}])},o[0]||(o[0]=[J("div",null,null,-1),J("div",null,null,-1),J("div",null,null,-1)]),2))}},Wt=Qt(Oo,[["__scopeId","data-v-5338a1a7"]]),Ao={__name:"AqListControls",props:{tooltip:{type:[Boolean,String,null],required:!1,default:!1},displayNone:{type:Boolean,default:!1}},emits:["action","add","duplicate","delete"],setup(e,{emit:n}){const t=n,o=e,r=gt(()=>typeof o.tooltip=="string"?o.tooltip:"Add/Duplicate/Delete Item");function a(l){console.log("AqListControls add",l),t("action","add"+l),t("add",l)}function i(){t("action","duplicate"),t("duplicate")}function s(){t("action","delete"),t("delete")}return(l,u)=>(ee(),mt(In,Ut({icon:"more_horiz"},l.$attrs,{class:{"aq-disable":o.displayNone}}),{default:z(()=>[O(kn,{anchor:"center left",self:"center right","auto-close":""},{default:z(()=>[O(Xe,{clickable:"",onClick:u[0]||(u[0]=d=>a(0))},{default:z(()=>[O(Fe,null,{default:z(()=>[J("div",null,[O(Re,{name:"add",size:"150%",color:"positive"}),u[4]||(u[4]=Te(" Add Above"))])]),_:1})]),_:1}),O(rt),O(Xe,{clickable:"",onClick:u[1]||(u[1]=d=>a(1))},{default:z(()=>[O(Fe,null,{default:z(()=>[J("div",null,[O(Re,{name:"add",size:"150%",color:"positive"}),u[5]||(u[5]=Te(" Add Below"))])]),_:1})]),_:1}),O(rt),O(Xe,{clickable:"",onClick:u[2]||(u[2]=d=>i())},{default:z(()=>[O(Fe,null,{default:z(()=>[J("div",null,[O(Re,{name:"content_copy",size:"150%",color:"positive"}),u[6]||(u[6]=Te(" Duplicate"))])]),_:1})]),_:1}),O(rt),O(Xe,{clickable:"",onClick:u[3]||(u[3]=d=>s())},{default:z(()=>[O(Fe,null,{default:z(()=>[J("div",null,[O(Re,{name:"delete",size:"150%",color:"negative"}),u[7]||(u[7]=Te(" Delete"))])]),_:1})]),_:1})]),_:1}),e.tooltip?(ee(),mt(Pn,{key:0},{default:z(()=>[Te(An(r.value),1)]),_:1})):we("",!0),vt(l.$slots,"default",{},void 0,!0)]),_:3},16,["class"]))}},zt=Qt(Ao,[["__scopeId","data-v-2ac147b0"]]),Io={style:{"min-width":"900px"}},No={key:0,class:"row q-col-gutter-xs q-mb-md q-ml-none"},Mo={key:0,class:"col-auto"},ko={class:"col"},Po={class:"row q-col-gutter-xs"},Bo={key:1,class:"col-auto"},Yo={key:0,class:"col-auto my-drag-handle"},Ro={class:"col"},Xo={class:"row q-col-gutter-xs"},Fo={key:1,class:"col-auto"},Lo={__name:"AqArrayForm",props:{reactiveArray:{type:Object,required:!0},enable:{type:Boolean,default:!0}},setup(e){return(n,t)=>(ee(),mt(yn,Ut(n.$attrs,{style:{"overflow-x":"auto"}}),{default:z(()=>[J("div",Io,[n.$slots.header?(ee(),ce("div",No,[e.enable?(ee(),ce("div",Mo,[O(Wt,{"display-none":""})])):we("",!0),J("div",ko,[J("div",Po,[vt(n.$slots,"header")])]),e.enable?(ee(),ce("div",Bo,[O(zt,{flat:"",outlined:"",dense:"",disable:"",class:"full-height","display-none":""})])):we("",!0)])):we("",!0),O($(xo),{ref:"form-el","model-value":e.reactiveArray.value,onEnd:t[0]||(t[0]=o=>e.reactiveArray.dragEnd(o)),handle:".my-drag-handle"},{default:z(()=>[(ee(!0),ce(Nn,null,Mn(e.reactiveArray.value,(o,r)=>(ee(),ce("div",{class:"row q-col-gutter-xs q-pb-xs q-mb-sm q-ml-none row-highlight-hover rounded-borders",key:r},[e.enable?(ee(),ce("div",Yo,[O(Wt)])):we("",!0),J("div",Ro,[J("div",Xo,[vt(n.$slots,"default",{item:o,index:r})])]),e.enable?(ee(),ce("div",Fo,[O(zt,{flat:"",outlined:"",dense:"",class:"full-height",color:"primary",onAction:a=>e.reactiveArray.action(r,a),tooltip:""},null,8,["onAction"])])):we("",!0)]))),128))]),_:3},8,["model-value"]),O(bn,{class:"z-top",reset:!0,show:e.reactiveArray.changed.value},null,8,["show"])])]),_:3},16))}};export{Lo as _};
