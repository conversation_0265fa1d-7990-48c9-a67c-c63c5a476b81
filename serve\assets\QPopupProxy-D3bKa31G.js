import{a as M,aq as Ke,ar as Ot,c as et,g as tt,r as p,a6 as kt,w as te,n as ze,h,aa as oe,b as It,O as N,as as jt}from"./index-CzmOWWdj.js";import{a as Tt,e as Vt,c as $t,g as pt,f as Ft,k as At,l as Et}from"./use-key-composition-CoMUTTxZ.js";import{p as w}from"./focusout-C-pmmZED.js";import{b as Nt}from"./QDialog-Cuvxr_1w.js";import{Q as Zt}from"./QMenu-D3shCIOy.js";function Lt(){let e=Object.create(null);return{getCache:(r,s)=>e[r]===void 0?e[r]=typeof s=="function"?s():s:e[r],setCache(r,s){e[r]=s},hasCache(r){return Object.hasOwnProperty.call(e,r)},clearCache(r){r!==void 0?delete e[r]:e=Object.create(null)}}}const B=[-61,9,38,199,426,686,756,818,1111,1181,1210,1635,2060,2097,2192,2262,2324,2394,2456,3178];function Pt(e,r,s){return Object.prototype.toString.call(e)==="[object Date]"&&(s=e.getDate(),r=e.getMonth()+1,e=e.getFullYear()),zt(je(e,r,s))}function Je(e,r,s){return nt(Bt(e,r,s))}function Qt(e){return Rt(e)===0}function me(e,r){return r<=6?31:r<=11||Qt(e)?30:29}function Rt(e){const r=B.length;let s=B[0],i,c,o,D,l;if(e<s||e>=B[r-1])throw new Error("Invalid Jalaali year "+e);for(l=1;l<r&&(i=B[l],c=i-s,!(e<i));l+=1)s=i;return D=e-s,c-D<6&&(D=D-c+x(c+4,33)*33),o=V(V(D+1,33)-1,4),o===-1&&(o=4),o}function at(e,r){const s=B.length,i=e+621;let c=-14,o=B[0],D,l,m,C,v;if(e<o||e>=B[s-1])throw new Error("Invalid Jalaali year "+e);for(v=1;v<s&&(D=B[v],l=D-o,!(e<D));v+=1)c=c+x(l,33)*8+x(V(l,33),4),o=D;C=e-o,c=c+x(C,33)*8+x(V(C,33)+3,4),V(l,33)===4&&l-C===4&&(c+=1);const b=x(i,4)-x((x(i,100)+1)*3,4)-150,F=20+c-b;return r||(l-C<6&&(C=C-l+x(l+4,33)*33),m=V(V(C+1,33)-1,4),m===-1&&(m=4)),{leap:m,gy:i,march:F}}function Bt(e,r,s){const i=at(e,!0);return je(i.gy,3,i.march)+(r-1)*31-x(r,7)*(r-7)+s-1}function zt(e){const r=nt(e).gy;let s=r-621,i,c,o;const D=at(s,!1),l=je(r,3,D.march);if(o=e-l,o>=0){if(o<=185)return c=1+x(o,31),i=V(o,31)+1,{jy:s,jm:c,jd:i};o-=186}else s-=1,o+=179,D.leap===1&&(o+=1);return c=7+x(o,30),i=V(o,30)+1,{jy:s,jm:c,jd:i}}function je(e,r,s){let i=x((e+x(r-8,6)+100100)*1461,4)+x(153*V(r+9,12)+2,5)+s-34840408;return i=i-x(x(e+100100+x(r-8,6),100)*3,4)+752,i}function nt(e){let r=4*e+139361631;r=r+x(x(4*e+183187720,146097)*3,4)*4-3908;const s=x(V(r,1461),4)*5+308,i=x(V(s,153),5)+1,c=V(x(s,153),12)+1;return{gy:x(r,1461)-100100+x(8-c,6),gm:c,gd:i}}function x(e,r){return~~(e/r)}function V(e,r){return e-~~(e/r)*r}const Jt=["gregorian","persian"],Xe={mask:{type:String},locale:Object,calendar:{type:String,validator:e=>Jt.includes(e),default:"gregorian"},landscape:Boolean,color:String,textColor:String,square:Boolean,flat:Boolean,bordered:Boolean,readonly:Boolean,disable:Boolean},Xt=["update:modelValue"];function Q(e){return e.year+"/"+w(e.month)+"/"+w(e.day)}function Wt(e,r){const s=M(()=>e.disable!==!0&&e.readonly!==!0),i=M(()=>s.value===!0?0:-1),c=M(()=>{const l=[];return e.color!==void 0&&l.push(`bg-${e.color}`),e.textColor!==void 0&&l.push(`text-${e.textColor}`),l.join(" ")});function o(){return e.locale!==void 0?{...r.lang.date,...e.locale}:r.lang.date}function D(l){const m=new Date,C=l===!0?null:0;if(e.calendar==="persian"){const v=Pt(m);return{year:v.jy,month:v.jm,day:v.jd}}return{year:m.getFullYear(),month:m.getMonth()+1,day:m.getDate(),hour:C,minute:C,second:C,millisecond:C}}return{editable:s,tabindex:i,headerClass:c,getLocale:o,getCurrentDate:D}}const rt=864e5,Ut=36e5,Ie=6e4,ot="YYYY-MM-DDTHH:mm:ss.SSSZ",Gt=/\[((?:[^\]\\]|\\]|\\)*)\]|do|d{1,4}|Mo|M{1,4}|m{1,2}|wo|w{1,2}|Qo|Do|DDDo|D{1,4}|YY(?:YY)?|H{1,2}|h{1,2}|s{1,2}|S{1,3}|Z{1,2}|a{1,2}|[AQExX]/g,Kt=/(\[[^\]]*\])|do|d{1,4}|Mo|M{1,4}|m{1,2}|wo|w{1,2}|Qo|Do|DDDo|D{1,4}|YY(?:YY)?|H{1,2}|h{1,2}|s{1,2}|S{1,3}|Z{1,2}|a{1,2}|[AQExX]|([.*+:?^,\s${}()|\\]+)/g,Ce={};function ea(e,r){const s="("+r.days.join("|")+")",i=e+s;if(Ce[i]!==void 0)return Ce[i];const c="("+r.daysShort.join("|")+")",o="("+r.months.join("|")+")",D="("+r.monthsShort.join("|")+")",l={};let m=0;const C=e.replace(Kt,b=>{switch(m++,b){case"YY":return l.YY=m,"(-?\\d{1,2})";case"YYYY":return l.YYYY=m,"(-?\\d{1,4})";case"M":return l.M=m,"(\\d{1,2})";case"Mo":return l.M=m++,"(\\d{1,2}(st|nd|rd|th))";case"MM":return l.M=m,"(\\d{2})";case"MMM":return l.MMM=m,D;case"MMMM":return l.MMMM=m,o;case"D":return l.D=m,"(\\d{1,2})";case"Do":return l.D=m++,"(\\d{1,2}(st|nd|rd|th))";case"DD":return l.D=m,"(\\d{2})";case"H":return l.H=m,"(\\d{1,2})";case"HH":return l.H=m,"(\\d{2})";case"h":return l.h=m,"(\\d{1,2})";case"hh":return l.h=m,"(\\d{2})";case"m":return l.m=m,"(\\d{1,2})";case"mm":return l.m=m,"(\\d{2})";case"s":return l.s=m,"(\\d{1,2})";case"ss":return l.s=m,"(\\d{2})";case"S":return l.S=m,"(\\d{1})";case"SS":return l.S=m,"(\\d{2})";case"SSS":return l.S=m,"(\\d{3})";case"A":return l.A=m,"(AM|PM)";case"a":return l.a=m,"(am|pm)";case"aa":return l.aa=m,"(a\\.m\\.|p\\.m\\.)";case"ddd":return c;case"dddd":return s;case"Q":case"d":case"E":return"(\\d{1})";case"do":return m++,"(\\d{1}(st|nd|rd|th))";case"Qo":return"(1st|2nd|3rd|4th)";case"DDD":case"DDDD":return"(\\d{1,3})";case"DDDo":return m++,"(\\d{1,3}(st|nd|rd|th))";case"w":return"(\\d{1,2})";case"wo":return m++,"(\\d{1,2}(st|nd|rd|th))";case"ww":return"(\\d{2})";case"Z":return l.Z=m,"(Z|[+-]\\d{2}:\\d{2})";case"ZZ":return l.ZZ=m,"(Z|[+-]\\d{2}\\d{2})";case"X":return l.X=m,"(-?\\d+)";case"x":return l.x=m,"(-?\\d{4,})";default:return m--,b[0]==="["&&(b=b.substring(1,b.length-1)),b.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}}),v={map:l,regex:new RegExp("^"+C)};return Ce[i]=v,v}function lt(e,r){return e!==void 0?e:r!==void 0?r.date:Ot.date}function We(e,r=""){const s=e>0?"-":"+",i=Math.abs(e),c=Math.floor(i/60),o=i%60;return s+w(c)+r+w(o)}function ta(e,r,s,i,c){const o={year:null,month:null,day:null,hour:null,minute:null,second:null,millisecond:null,timezoneOffset:null,dateHash:null,timeHash:null};if(c!==void 0&&Object.assign(o,c),e==null||e===""||typeof e!="string")return o;r===void 0&&(r=ot);const D=lt(s,Ke.props),l=D.months,m=D.monthsShort,{regex:C,map:v}=ea(r,D),b=e.match(C);if(b===null)return o;let F="";if(v.X!==void 0||v.x!==void 0){const I=parseInt(b[v.X!==void 0?v.X:v.x],10);if(isNaN(I)===!0||I<0)return o;const j=new Date(I*(v.X!==void 0?1e3:1));o.year=j.getFullYear(),o.month=j.getMonth()+1,o.day=j.getDate(),o.hour=j.getHours(),o.minute=j.getMinutes(),o.second=j.getSeconds(),o.millisecond=j.getMilliseconds()}else{if(v.YYYY!==void 0)o.year=parseInt(b[v.YYYY],10);else if(v.YY!==void 0){const I=parseInt(b[v.YY],10);o.year=I<0?I:2e3+I}if(v.M!==void 0){if(o.month=parseInt(b[v.M],10),o.month<1||o.month>12)return o}else v.MMM!==void 0?o.month=m.indexOf(b[v.MMM])+1:v.MMMM!==void 0&&(o.month=l.indexOf(b[v.MMMM])+1);if(v.D!==void 0){if(o.day=parseInt(b[v.D],10),o.year===null||o.month===null||o.day<1)return o;const I=i!=="persian"?new Date(o.year,o.month,0).getDate():me(o.year,o.month);if(o.day>I)return o}v.H!==void 0?o.hour=parseInt(b[v.H],10)%24:v.h!==void 0&&(o.hour=parseInt(b[v.h],10)%12,(v.A&&b[v.A]==="PM"||v.a&&b[v.a]==="pm"||v.aa&&b[v.aa]==="p.m.")&&(o.hour+=12),o.hour=o.hour%24),v.m!==void 0&&(o.minute=parseInt(b[v.m],10)%60),v.s!==void 0&&(o.second=parseInt(b[v.s],10)%60),v.S!==void 0&&(o.millisecond=parseInt(b[v.S],10)*10**(3-b[v.S].length)),(v.Z!==void 0||v.ZZ!==void 0)&&(F=v.Z!==void 0?b[v.Z].replace(":",""):b[v.ZZ],o.timezoneOffset=(F[0]==="+"?-1:1)*(60*F.slice(1,3)+1*F.slice(3,5)))}return o.dateHash=w(o.year,6)+"/"+w(o.month)+"/"+w(o.day),o.timeHash=w(o.hour)+":"+w(o.minute)+":"+w(o.second)+F,o}function qe(e){const r=new Date(e.getFullYear(),e.getMonth(),e.getDate());r.setDate(r.getDate()-(r.getDay()+6)%7+3);const s=new Date(r.getFullYear(),0,4);s.setDate(s.getDate()-(s.getDay()+6)%7+3);const i=r.getTimezoneOffset()-s.getTimezoneOffset();r.setHours(r.getHours()-i);const c=(r-s)/(rt*7);return 1+Math.floor(c)}function L(e,r,s){const i=new Date(e),c=`set${s===!0?"UTC":""}`;switch(r){case"year":case"years":i[`${c}Month`](0);case"month":case"months":i[`${c}Date`](1);case"day":case"days":case"date":i[`${c}Hours`](0);case"hour":case"hours":i[`${c}Minutes`](0);case"minute":case"minutes":i[`${c}Seconds`](0);case"second":case"seconds":i[`${c}Milliseconds`](0)}return i}function fe(e,r,s){return(e.getTime()-e.getTimezoneOffset()*Ie-(r.getTime()-r.getTimezoneOffset()*Ie))/s}function ut(e,r,s="days"){const i=new Date(e),c=new Date(r);switch(s){case"years":case"year":return i.getFullYear()-c.getFullYear();case"months":case"month":return(i.getFullYear()-c.getFullYear())*12+i.getMonth()-c.getMonth();case"days":case"day":case"date":return fe(L(i,"day"),L(c,"day"),rt);case"hours":case"hour":return fe(L(i,"hour"),L(c,"hour"),Ut);case"minutes":case"minute":return fe(L(i,"minute"),L(c,"minute"),Ie);case"seconds":case"second":return fe(L(i,"second"),L(c,"second"),1e3)}}function Oe(e){return ut(e,L(e,"year"),"days")+1}function K(e){if(e>=11&&e<=13)return`${e}th`;switch(e%10){case 1:return`${e}st`;case 2:return`${e}nd`;case 3:return`${e}rd`}return`${e}th`}const Ue={YY(e,r,s){const i=this.YYYY(e,r,s)%100;return i>=0?w(i):"-"+w(Math.abs(i))},YYYY(e,r,s){return s??e.getFullYear()},M(e){return e.getMonth()+1},Mo(e){return K(e.getMonth()+1)},MM(e){return w(e.getMonth()+1)},MMM(e,r){return r.monthsShort[e.getMonth()]},MMMM(e,r){return r.months[e.getMonth()]},Q(e){return Math.ceil((e.getMonth()+1)/3)},Qo(e){return K(this.Q(e))},D(e){return e.getDate()},Do(e){return K(e.getDate())},DD(e){return w(e.getDate())},DDD(e){return Oe(e)},DDDo(e){return K(Oe(e))},DDDD(e){return w(Oe(e),3)},d(e){return e.getDay()},do(e){return K(e.getDay())},dd(e,r){return r.days[e.getDay()].slice(0,2)},ddd(e,r){return r.daysShort[e.getDay()]},dddd(e,r){return r.days[e.getDay()]},E(e){return e.getDay()||7},w(e){return qe(e)},wo(e){return K(qe(e))},ww(e){return w(qe(e))},H(e){return e.getHours()},HH(e){return w(e.getHours())},h(e){const r=e.getHours();return r===0?12:r>12?r%12:r},hh(e){return w(this.h(e))},m(e){return e.getMinutes()},mm(e){return w(e.getMinutes())},s(e){return e.getSeconds()},ss(e){return w(e.getSeconds())},S(e){return Math.floor(e.getMilliseconds()/100)},SS(e){return w(Math.floor(e.getMilliseconds()/10))},SSS(e){return w(e.getMilliseconds(),3)},A(e){return e.getHours()<12?"AM":"PM"},a(e){return e.getHours()<12?"am":"pm"},aa(e){return e.getHours()<12?"a.m.":"p.m."},Z(e,r,s,i){const c=i??e.getTimezoneOffset();return We(c,":")},ZZ(e,r,s,i){const c=i??e.getTimezoneOffset();return We(c)},X(e){return Math.floor(e.getTime()/1e3)},x(e){return e.getTime()}};function aa(e,r,s,i,c){if(e!==0&&!e||e===1/0||e===-1/0)return;const o=new Date(e);if(isNaN(o))return;r===void 0&&(r=ot);const D=lt(s,Ke.props);return r.replace(Gt,(l,m)=>l in Ue?Ue[l](o,D,i,c):m===void 0?l:m.split("\\]").join("]"))}const Z=20,na=["Calendar","Years","Months"],Ge=e=>na.includes(e),ke=e=>/^-?[\d]+\/[0-1]\d$/.test(e),ee=" — ";function R(e){return e.year+"/"+w(e.month)}const ia=et({name:"QDate",props:{...Xe,...Vt,...Tt,modelValue:{required:!0,validator:e=>typeof e=="string"||Array.isArray(e)===!0||Object(e)===e||e===null},multiple:Boolean,range:Boolean,title:String,subtitle:String,mask:{...Xe.mask,default:"YYYY/MM/DD"},defaultYearMonth:{type:String,validator:ke},yearsInMonthView:Boolean,events:[Array,Function],eventColor:[String,Function],emitImmediately:Boolean,options:[Array,Function],navigationMinYearMonth:{type:String,validator:ke},navigationMaxYearMonth:{type:String,validator:ke},noUnset:Boolean,firstDayOfWeek:[String,Number],todayBtn:Boolean,minimal:Boolean,defaultView:{type:String,default:"Calendar",validator:Ge}},emits:[...Xt,"rangeStart","rangeEnd","navigation"],setup(e,{slots:r,emit:s}){const{proxy:i}=tt(),{$q:c}=i,o=$t(e,c),{getCache:D}=Lt(),{tabindex:l,headerClass:m,getLocale:C,getCurrentDate:v}=Wt(e,c);let b;const F=pt(e),I=Ft(F),j=p(null),S=p(Ne()),H=p(C()),st=M(()=>Ne()),it=M(()=>C()),E=M(()=>v()),g=p(Ze(S.value,H.value)),T=p(e.defaultView),Te=M(()=>c.lang.rtl===!0?"right":"left"),le=p(Te.value),he=p(Te.value),ge=g.value.year,ue=p(ge-ge%Z-(ge<0?Z:0)),k=p(null),ct=M(()=>{const t=e.landscape===!0?"landscape":"portrait";return`q-date q-date--${t} q-date--${t}-${e.minimal===!0?"minimal":"standard"}`+(o.value===!0?" q-date--dark q-dark":"")+(e.bordered===!0?" q-date--bordered":"")+(e.square===!0?" q-date--square no-border-radius":"")+(e.flat===!0?" q-date--flat no-shadow":"")+(e.disable===!0?" disabled":e.readonly===!0?" q-date--readonly":"")}),z=M(()=>e.color||"primary"),X=M(()=>e.textColor||"white"),se=M(()=>e.emitImmediately===!0&&e.multiple!==!0&&e.range!==!0),ye=M(()=>Array.isArray(e.modelValue)===!0?e.modelValue:e.modelValue!==null&&e.modelValue!==void 0?[e.modelValue]:[]),A=M(()=>ye.value.filter(t=>typeof t=="string").map(t=>we(t,S.value,H.value)).filter(t=>t.dateHash!==null&&t.day!==null&&t.month!==null&&t.year!==null)),W=M(()=>{const t=a=>we(a,S.value,H.value);return ye.value.filter(a=>kt(a)===!0&&a.from!==void 0&&a.to!==void 0).map(a=>({from:t(a.from),to:t(a.to)})).filter(a=>a.from.dateHash!==null&&a.to.dateHash!==null&&a.from.dateHash<a.to.dateHash)}),ie=M(()=>e.calendar!=="persian"?t=>new Date(t.year,t.month-1,t.day):t=>{const a=Je(t.year,t.month,t.day);return new Date(a.gy,a.gm-1,a.gd)}),Me=M(()=>e.calendar==="persian"?Q:(t,a,n)=>aa(new Date(t.year,t.month-1,t.day,t.hour,t.minute,t.second,t.millisecond),a===void 0?S.value:a,n===void 0?H.value:n,t.year,t.timezoneOffset)),ae=M(()=>A.value.length+W.value.reduce((t,a)=>t+1+ut(ie.value(a.to),ie.value(a.from)),0)),Ve=M(()=>{if(e.title!==void 0&&e.title!==null&&e.title.length!==0)return e.title;if(k.value!==null){const n=k.value.init,u=ie.value(n);return H.value.daysShort[u.getDay()]+", "+H.value.monthsShort[n.month-1]+" "+n.day+ee+"?"}if(ae.value===0)return ee;if(ae.value>1)return`${ae.value} ${H.value.pluralDay}`;const t=A.value[0],a=ie.value(t);return isNaN(a.valueOf())===!0?ee:H.value.headerTitle!==void 0?H.value.headerTitle(a,t):H.value.daysShort[a.getDay()]+", "+H.value.monthsShort[t.month-1]+" "+t.day}),dt=M(()=>A.value.concat(W.value.map(a=>a.from)).sort((a,n)=>a.year-n.year||a.month-n.month)[0]),vt=M(()=>A.value.concat(W.value.map(a=>a.to)).sort((a,n)=>n.year-a.year||n.month-a.month)[0]),$e=M(()=>{if(e.subtitle!==void 0&&e.subtitle!==null&&e.subtitle.length!==0)return e.subtitle;if(ae.value===0)return ee;if(ae.value>1){const t=dt.value,a=vt.value,n=H.value.monthsShort;return n[t.month-1]+(t.year!==a.year?" "+t.year+ee+n[a.month-1]+" ":t.month!==a.month?ee+n[a.month-1]:"")+" "+a.year}return A.value[0].year}),ce=M(()=>{const t=[c.iconSet.datetime.arrowLeft,c.iconSet.datetime.arrowRight];return c.lang.rtl===!0?t.reverse():t}),pe=M(()=>e.firstDayOfWeek!==void 0?Number(e.firstDayOfWeek):H.value.firstDayOfWeek),ft=M(()=>{const t=H.value.daysShort,a=pe.value;return a>0?t.slice(a,7).concat(t.slice(0,a)):t}),P=M(()=>{const t=g.value;return e.calendar!=="persian"?new Date(t.year,t.month,0).getDate():me(t.year,t.month)}),mt=M(()=>typeof e.eventColor=="function"?e.eventColor:()=>e.eventColor),q=M(()=>{if(e.navigationMinYearMonth===void 0)return null;const t=e.navigationMinYearMonth.split("/");return{year:parseInt(t[0],10),month:parseInt(t[1],10)}}),O=M(()=>{if(e.navigationMaxYearMonth===void 0)return null;const t=e.navigationMaxYearMonth.split("/");return{year:parseInt(t[0],10),month:parseInt(t[1],10)}}),De=M(()=>{const t={month:{prev:!0,next:!0},year:{prev:!0,next:!0}};return q.value!==null&&q.value.year>=g.value.year&&(t.year.prev=!1,q.value.year===g.value.year&&q.value.month>=g.value.month&&(t.month.prev=!1)),O.value!==null&&O.value.year<=g.value.year&&(t.year.next=!1,O.value.year===g.value.year&&O.value.month<=g.value.month&&(t.month.next=!1)),t}),de=M(()=>{const t={};return A.value.forEach(a=>{const n=R(a);t[n]===void 0&&(t[n]=[]),t[n].push(a.day)}),t}),Fe=M(()=>{const t={};return W.value.forEach(a=>{const n=R(a.from),u=R(a.to);if(t[n]===void 0&&(t[n]=[]),t[n].push({from:a.from.day,to:n===u?a.to.day:void 0,range:a}),n<u){let d;const{year:Y,month:f}=a.from,y=f<12?{year:Y,month:f+1}:{year:Y+1,month:1};for(;(d=R(y))<=u;)t[d]===void 0&&(t[d]=[]),t[d].push({from:void 0,to:d===u?a.to.day:void 0,range:a}),y.month++,y.month>12&&(y.year++,y.month=1)}}),t}),ne=M(()=>{if(k.value===null)return;const{init:t,initHash:a,final:n,finalHash:u}=k.value,[d,Y]=a<=u?[t,n]:[n,t],f=R(d),y=R(Y);if(f!==$.value&&y!==$.value)return;const _={};return f===$.value?(_.from=d.day,_.includeFrom=!0):_.from=1,y===$.value?(_.to=Y.day,_.includeTo=!0):_.to=P.value,_}),$=M(()=>R(g.value)),ht=M(()=>{const t={};if(e.options===void 0){for(let n=1;n<=P.value;n++)t[n]=!0;return t}const a=typeof e.options=="function"?e.options:n=>e.options.includes(n);for(let n=1;n<=P.value;n++){const u=$.value+"/"+w(n);t[n]=a(u)}return t}),gt=M(()=>{const t={};if(e.events===void 0)for(let a=1;a<=P.value;a++)t[a]=!1;else{const a=typeof e.events=="function"?e.events:n=>e.events.includes(n);for(let n=1;n<=P.value;n++){const u=$.value+"/"+w(n);t[n]=a(u)===!0&&mt.value(u)}}return t}),yt=M(()=>{let t,a;const{year:n,month:u}=g.value;if(e.calendar!=="persian")t=new Date(n,u-1,1),a=new Date(n,u-1,0).getDate();else{const d=Je(n,u,1);t=new Date(d.gy,d.gm-1,d.gd);let Y=u-1,f=n;Y===0&&(Y=12,f--),a=me(f,Y)}return{days:t.getDay()-pe.value-1,endDay:a}}),Ae=M(()=>{const t=[],{days:a,endDay:n}=yt.value,u=a<0?a+7:a;if(u<6)for(let f=n-u;f<=n;f++)t.push({i:f,fill:!0});const d=t.length;for(let f=1;f<=P.value;f++){const y={i:f,event:gt.value[f],classes:[]};ht.value[f]===!0&&(y.in=!0,y.flat=!0),t.push(y)}if(de.value[$.value]!==void 0&&de.value[$.value].forEach(f=>{const y=d+f-1;Object.assign(t[y],{selected:!0,unelevated:!0,flat:!1,color:z.value,textColor:X.value})}),Fe.value[$.value]!==void 0&&Fe.value[$.value].forEach(f=>{if(f.from!==void 0){const y=d+f.from-1,_=d+(f.to||P.value)-1;for(let re=y;re<=_;re++)Object.assign(t[re],{range:f.range,unelevated:!0,color:z.value,textColor:X.value});Object.assign(t[y],{rangeFrom:!0,flat:!1}),f.to!==void 0&&Object.assign(t[_],{rangeTo:!0,flat:!1})}else if(f.to!==void 0){const y=d+f.to-1;for(let _=d;_<=y;_++)Object.assign(t[_],{range:f.range,unelevated:!0,color:z.value,textColor:X.value});Object.assign(t[y],{flat:!1,rangeTo:!0})}else{const y=d+P.value-1;for(let _=d;_<=y;_++)Object.assign(t[_],{range:f.range,unelevated:!0,color:z.value,textColor:X.value})}}),ne.value!==void 0){const f=d+ne.value.from-1,y=d+ne.value.to-1;for(let _=f;_<=y;_++)t[_].color=z.value,t[_].editRange=!0;ne.value.includeFrom===!0&&(t[f].editRangeFrom=!0),ne.value.includeTo===!0&&(t[y].editRangeTo=!0)}g.value.year===E.value.year&&g.value.month===E.value.month&&(t[d+E.value.day-1].today=!0);const Y=t.length%7;if(Y>0){const f=7-Y;for(let y=1;y<=f;y++)t.push({i:y,fill:!0})}return t.forEach(f=>{let y="q-date__calendar-item ";f.fill===!0?y+="q-date__calendar-item--fill":(y+=`q-date__calendar-item--${f.in===!0?"in":"out"}`,f.range!==void 0&&(y+=` q-date__range${f.rangeTo===!0?"-to":f.rangeFrom===!0?"-from":""}`),f.editRange===!0&&(y+=` q-date__edit-range${f.editRangeFrom===!0?"-from":""}${f.editRangeTo===!0?"-to":""}`),(f.range!==void 0||f.editRange===!0)&&(y+=` text-${f.color}`)),f.classes=y}),t}),Mt=M(()=>e.disable===!0?{"aria-disabled":"true"}:{});te(()=>e.modelValue,t=>{if(b===JSON.stringify(t))b=0;else{const a=Ze(S.value,H.value);U(a.year,a.month,a)}}),te(T,()=>{j.value!==null&&i.$el.contains(document.activeElement)===!0&&j.value.focus()}),te(()=>g.value.year+"|"+g.value.month,()=>{s("navigation",{year:g.value.year,month:g.value.month})}),te(st,t=>{Be(t,H.value,"mask"),S.value=t}),te(it,t=>{Be(S.value,t,"locale"),H.value=t});function be(t){b=JSON.stringify(t)}function Ee(){const{year:t,month:a,day:n}=E.value,u={...g.value,year:t,month:a,day:n},d=de.value[R(u)];(d===void 0||d.includes(u.day)===!1)&&xe(u),Ye(u.year,u.month)}function Dt(t){Ge(t)===!0&&(T.value=t)}function bt(t,a){["month","year"].includes(t)&&(t==="month"?Pe:_e)(a===!0?-1:1)}function Ye(t,a){T.value="Calendar",U(t,a)}function Yt(t,a){if(e.range===!1||!t){k.value=null;return}const n=Object.assign({...g.value},t),u=a!==void 0?Object.assign({...g.value},a):n;k.value={init:n,initHash:Q(n),final:u,finalHash:Q(u)},Ye(n.year,n.month)}function Ne(){return e.calendar==="persian"?"YYYY/MM/DD":e.mask}function we(t,a,n){return ta(t,a,n,e.calendar,{hour:0,minute:0,second:0,millisecond:0})}function Ze(t,a){const n=Array.isArray(e.modelValue)===!0?e.modelValue:e.modelValue?[e.modelValue]:[];if(n.length===0)return Le();const u=n[n.length-1],d=we(u.from!==void 0?u.from:u,t,a);return d.dateHash===null?Le():d}function Le(){let t,a;if(e.defaultYearMonth!==void 0){const n=e.defaultYearMonth.split("/");t=parseInt(n[0],10),a=parseInt(n[1],10)}else{const n=E.value!==void 0?E.value:v();t=n.year,a=n.month}return{year:t,month:a,day:1,hour:0,minute:0,second:0,millisecond:0,dateHash:t+"/"+w(a)+"/01"}}function Pe(t){let a=g.value.year,n=Number(g.value.month)+t;n===13?(n=1,a++):n===0&&(n=12,a--),U(a,n),se.value===!0&&ve("month")}function _e(t){const a=Number(g.value.year)+t;U(a,g.value.month),se.value===!0&&ve("year")}function wt(t){U(t,g.value.month),T.value=e.defaultView==="Years"?"Months":"Calendar",se.value===!0&&ve("year")}function _t(t){U(g.value.year,t),T.value="Calendar",se.value===!0&&ve("month")}function xt(t,a){(de.value[a]?.includes(t.day)===!0?Se:xe)(t)}function J(t){return{year:t.year,month:t.month,day:t.day}}function U(t,a,n){if(q.value!==null&&t<=q.value.year&&((a<q.value.month||t<q.value.year)&&(a=q.value.month),t=q.value.year),O.value!==null&&t>=O.value.year&&((a>O.value.month||t>O.value.year)&&(a=O.value.month),t=O.value.year),n!==void 0){const{hour:d,minute:Y,second:f,millisecond:y,timezoneOffset:_,timeHash:re}=n;Object.assign(g.value,{hour:d,minute:Y,second:f,millisecond:y,timezoneOffset:_,timeHash:re})}const u=t+"/"+w(a)+"/01";u!==g.value.dateHash&&(le.value=g.value.dateHash<u==(c.lang.rtl!==!0)?"left":"right",t!==g.value.year&&(he.value=le.value),ze(()=>{ue.value=t-t%Z-(t<0?Z:0),Object.assign(g.value,{year:t,month:a,day:1,dateHash:u})}))}function Qe(t,a,n){const u=t!==null&&t.length===1&&e.multiple===!1?t[0]:t,{reason:d,details:Y}=Re(a,n);be(u),s("update:modelValue",u,d,Y)}function ve(t){const a=A.value[0]!==void 0&&A.value[0].dateHash!==null?{...A.value[0]}:{...g.value};ze(()=>{a.year=g.value.year,a.month=g.value.month;const n=e.calendar!=="persian"?new Date(a.year,a.month,0).getDate():me(a.year,a.month);a.day=Math.min(Math.max(1,a.day),n);const u=G(a),{details:d}=Re("",a);be(u),s("update:modelValue",u,t,d)})}function Re(t,a){return a.from!==void 0?{reason:`${t}-range`,details:{...J(a.target),from:J(a.from),to:J(a.to)}}:{reason:`${t}-day`,details:J(a)}}function G(t,a,n){return t.from!==void 0?{from:Me.value(t.from,a,n),to:Me.value(t.to,a,n)}:Me.value(t,a,n)}function xe(t){let a;if(e.multiple===!0)if(t.from!==void 0){const n=Q(t.from),u=Q(t.to),d=A.value.filter(f=>f.dateHash<n||f.dateHash>u),Y=W.value.filter(({from:f,to:y})=>y.dateHash<n||f.dateHash>u);a=d.concat(Y).concat(t).map(f=>G(f))}else{const n=ye.value.slice();n.push(G(t)),a=n}else a=G(t);Qe(a,"add",t)}function Se(t){if(e.noUnset===!0)return;let a=null;if(e.multiple===!0&&Array.isArray(e.modelValue)===!0){const n=G(t);t.from!==void 0?a=e.modelValue.filter(u=>u.from!==void 0?u.from!==n.from&&u.to!==n.to:!0):a=e.modelValue.filter(u=>u!==n),a.length===0&&(a=null)}Qe(a,"remove",t)}function Be(t,a,n){const u=A.value.concat(W.value).map(Y=>G(Y,t,a)).filter(Y=>Y.from!==void 0?Y.from.dateHash!==null&&Y.to.dateHash!==null:Y.dateHash!==null),d=(e.multiple===!0?u:u[0])||null;be(d),s("update:modelValue",d,n)}function St(){if(e.minimal!==!0)return h("div",{class:"q-date__header "+m.value},[h("div",{class:"relative-position"},[h(oe,{name:"q-transition--fade"},()=>h("div",{key:"h-yr-"+$e.value,class:"q-date__header-subtitle q-date__header-link "+(T.value==="Years"?"q-date__header-link--active":"cursor-pointer"),tabindex:l.value,...D("vY",{onClick(){T.value="Years"},onKeyup(t){t.keyCode===13&&(T.value="Years")}})},[$e.value]))]),h("div",{class:"q-date__header-title relative-position flex no-wrap"},[h("div",{class:"relative-position col"},[h(oe,{name:"q-transition--fade"},()=>h("div",{key:"h-sub"+Ve.value,class:"q-date__header-title-label q-date__header-link "+(T.value==="Calendar"?"q-date__header-link--active":"cursor-pointer"),tabindex:l.value,...D("vC",{onClick(){T.value="Calendar"},onKeyup(t){t.keyCode===13&&(T.value="Calendar")}})},[Ve.value]))]),e.todayBtn===!0?h(N,{class:"q-date__header-today self-start",icon:c.iconSet.datetime.today,ariaLabel:c.lang.date.today,flat:!0,size:"sm",round:!0,tabindex:l.value,onClick:Ee}):null])])}function He({label:t,type:a,key:n,dir:u,goTo:d,boundaries:Y,cls:f}){return[h("div",{class:"row items-center q-date__arrow"},[h(N,{round:!0,dense:!0,size:"sm",flat:!0,icon:ce.value[0],ariaLabel:a==="Years"?c.lang.date.prevYear:c.lang.date.prevMonth,tabindex:l.value,disable:Y.prev===!1,...D("go-#"+a,{onClick(){d(-1)}})})]),h("div",{class:"relative-position overflow-hidden flex flex-center"+f},[h(oe,{name:"q-transition--jump-"+u},()=>h("div",{key:n},[h(N,{flat:!0,dense:!0,noCaps:!0,label:t,tabindex:l.value,...D("view#"+a,{onClick:()=>{T.value=a}})})]))]),h("div",{class:"row items-center q-date__arrow"},[h(N,{round:!0,dense:!0,size:"sm",flat:!0,icon:ce.value[1],ariaLabel:a==="Years"?c.lang.date.nextYear:c.lang.date.nextMonth,tabindex:l.value,disable:Y.next===!1,...D("go+#"+a,{onClick(){d(1)}})})])]}const Ht={Calendar:()=>[h("div",{key:"calendar-view",class:"q-date__view q-date__calendar"},[h("div",{class:"q-date__navigation row items-center no-wrap"},He({label:H.value.months[g.value.month-1],type:"Months",key:g.value.month,dir:le.value,goTo:Pe,boundaries:De.value.month,cls:" col"}).concat(He({label:g.value.year,type:"Years",key:g.value.year,dir:he.value,goTo:_e,boundaries:De.value.year,cls:""}))),h("div",{class:"q-date__calendar-weekdays row items-center no-wrap"},ft.value.map(t=>h("div",{class:"q-date__calendar-item"},[h("div",t)]))),h("div",{class:"q-date__calendar-days-container relative-position overflow-hidden"},[h(oe,{name:"q-transition--slide-"+le.value},()=>h("div",{key:$.value,class:"q-date__calendar-days fit"},Ae.value.map(t=>h("div",{class:t.classes},[t.in===!0?h(N,{class:t.today===!0?"q-date__today":"",dense:!0,flat:t.flat,unelevated:t.unelevated,color:t.color,textColor:t.textColor,label:t.i,tabindex:l.value,...D("day#"+t.i,{onClick:()=>{Ct(t.i)},onMouseover:()=>{qt(t.i)}})},t.event!==!1?()=>h("div",{class:"q-date__event bg-"+t.event}):null):h("div",""+t.i)]))))])])],Months(){const t=g.value.year===E.value.year,a=u=>q.value!==null&&g.value.year===q.value.year&&q.value.month>u||O.value!==null&&g.value.year===O.value.year&&O.value.month<u,n=H.value.monthsShort.map((u,d)=>{const Y=g.value.month===d+1;return h("div",{class:"q-date__months-item flex flex-center"},[h(N,{class:t===!0&&E.value.month===d+1?"q-date__today":null,flat:Y!==!0,label:u,unelevated:Y,color:Y===!0?z.value:null,textColor:Y===!0?X.value:null,tabindex:l.value,disable:a(d+1),...D("month#"+d,{onClick:()=>{_t(d+1)}})})])});return e.yearsInMonthView===!0&&n.unshift(h("div",{class:"row no-wrap full-width"},[He({label:g.value.year,type:"Years",key:g.value.year,dir:he.value,goTo:_e,boundaries:De.value.year,cls:" col"})])),h("div",{key:"months-view",class:"q-date__view q-date__months flex flex-center"},n)},Years(){const t=ue.value,a=t+Z,n=[],u=d=>q.value!==null&&q.value.year>d||O.value!==null&&O.value.year<d;for(let d=t;d<=a;d++){const Y=g.value.year===d;n.push(h("div",{class:"q-date__years-item flex flex-center"},[h(N,{key:"yr"+d,class:E.value.year===d?"q-date__today":null,flat:!Y,label:d,dense:!0,unelevated:Y,color:Y===!0?z.value:null,textColor:Y===!0?X.value:null,tabindex:l.value,disable:u(d),...D("yr#"+d,{onClick:()=>{wt(d)}})})]))}return h("div",{class:"q-date__view q-date__years flex flex-center"},[h("div",{class:"col-auto"},[h(N,{round:!0,dense:!0,flat:!0,icon:ce.value[0],ariaLabel:c.lang.date.prevRangeYears(Z),tabindex:l.value,disable:u(t),...D("y-",{onClick:()=>{ue.value-=Z}})})]),h("div",{class:"q-date__years-content col self-stretch row items-center"},n),h("div",{class:"col-auto"},[h(N,{round:!0,dense:!0,flat:!0,icon:ce.value[1],ariaLabel:c.lang.date.nextRangeYears(Z),tabindex:l.value,disable:u(a),...D("y+",{onClick:()=>{ue.value+=Z}})})])])}};function Ct(t){const a={...g.value,day:t};if(e.range===!1){xt(a,$.value);return}if(k.value===null){const n=Ae.value.find(d=>d.fill!==!0&&d.i===t);if(e.noUnset!==!0&&n.range!==void 0){Se({target:a,from:n.range.from,to:n.range.to});return}if(n.selected===!0){Se(a);return}const u=Q(a);k.value={init:a,initHash:u,final:a,finalHash:u},s("rangeStart",J(a))}else{const n=k.value.initHash,u=Q(a),d=n<=u?{from:k.value.init,to:a}:{from:a,to:k.value.init};k.value=null,xe(n===u?a:{target:a,...d}),s("rangeEnd",{from:J(d.from),to:J(d.to)})}}function qt(t){if(k.value!==null){const a={...g.value,day:t};Object.assign(k.value,{final:a,finalHash:Q(a)})}}return Object.assign(i,{setToday:Ee,setView:Dt,offsetCalendar:bt,setCalendarTo:Ye,setEditingRange:Yt}),()=>{const t=[h("div",{class:"q-date__content col relative-position"},[h(oe,{name:"q-transition--fade"},Ht[T.value])])],a=It(r.default);return a!==void 0&&t.push(h("div",{class:"q-date__actions"},a)),e.name!==void 0&&e.disable!==!0&&I(t,"push"),h("div",{class:ct.value,...Mt.value},[St(),h("div",{ref:j,class:"q-date__main col column",tabindex:-1},t)])}}}),ca=et({name:"QPopupProxy",props:{...At,breakpoint:{type:[String,Number],default:450}},emits:["show","hide"],setup(e,{slots:r,emit:s,attrs:i}){const{proxy:c}=tt(),{$q:o}=c,D=p(!1),l=p(null),m=M(()=>parseInt(e.breakpoint,10)),{canShow:C}=Et({showing:D});function v(){return o.screen.width<m.value||o.screen.height<m.value?"dialog":"menu"}const b=p(v()),F=M(()=>b.value==="menu"?{maxHeight:"99vh"}:{});te(()=>v(),S=>{D.value!==!0&&(b.value=S)});function I(S){D.value=!0,s("show",S)}function j(S){D.value=!1,b.value=v(),s("hide",S)}return Object.assign(c,{show(S){C(S)===!0&&l.value.show(S)},hide(S){l.value.hide(S)},toggle(S){l.value.toggle(S)}}),jt(c,"currentComponent",()=>({type:b.value,ref:l.value})),()=>{const S={ref:l,...F.value,...i,onShow:I,onHide:j};let H;return b.value==="dialog"?H=Nt:(H=Zt,Object.assign(S,{target:e.target,contextMenu:e.contextMenu,noParentEvent:!0,separateClosePopup:!0})),h(H,S,r.default)}}});export{ca as Q,ta as _,ia as a,Xt as b,Wt as c,aa as f,Q as g,Xe as u};
