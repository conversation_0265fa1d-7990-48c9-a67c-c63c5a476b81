
//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// routes/alarms.js
//
// <PERSON> routes
//=============================================================================================
console.log('========== routes/alarms.js ========')
// const fs = require('fs')

/**
 * @module routes/alarms
 */

const { API_ROOT } = require('../System.js')
const { alarms, clearAlarms, sendAlertEmail, composeAlertEmail, applySettings } = require('../Alarms.js')
const { objectArrayInclude, stringToBoolean } = require('../Util.js')
const { AUTH_OPERATOR } = require('../AppLoginAuth.js')
const { postFileHandler } = require('./handlers.js')
const { ProjectDefinitions } = require('../Project.js')


//----------------------------------------------------------------------------------------------------
/**
 * Routes for the alarms page.
 * - Call using `fastify.register()`
 * @param {object} fastify - The Fastify instance
 * @param {object} options
 * @param {Function} done - The callback function to call when the plugin is registered
 */
//----------------------------------------------------------------------------------------------------
function alarmsRoutes(fastify, options, done)
{
	const changeFields = ['i', 'alarm', 'active', 'value', 'start', 'error', 'timestamp']

	// GET /api/status/alarms
	fastify.get(API_ROOT + '/status/alarms', async (request, reply) => {
		const r = stringToBoolean(request.query.changes) ?
			objectArrayInclude(alarms, changeFields) : // Only return items that can change dynamically
			alarms
		return reply.send(r)
	})

	// DELETE /api/status/lockouts
	fastify.delete(API_ROOT + '/status/lockouts', AUTH_OPERATOR, async (request, reply) => {
		const n = clearAlarms('lockouts')
		return reply.send({ message: n + 'Lockouts cleared' })
	})

	// DELETE /api/status/alarms
	fastify.delete(API_ROOT + '/status/alarms', async (request, reply) => {
		const n = clearAlarms()
		return reply.send({ message: n + ' Alarms cleared' })
	})

	// GET /api/setup/alarms
	fastify.get(API_ROOT + '/setup/alarms', async (request, reply) => {
		return reply.send(alarms)
	})

	// Reload the alarms from the file
	// GET /api/setup/alarms/reload
	fastify.get(API_ROOT + '/setup/alarms/reload', async (request, reply) => {
		applySettings(ProjectDefinitions.alarmSettingsFile)
		return reply.send(alarms)
	})

	// Save the alarms to the file
	// POST /api/setup/alarms
	fastify.post(API_ROOT + '/setup/alarms', async (request, reply) => {
		const data = request.body.data
		// fs.writeFileSync(fileName, JSON.stringify(data, null, '\t'))
		postFileHandler(request, reply, ProjectDefinitions.alarmSettingsFile, () => applySettings(ProjectDefinitions.alarmSettingsFile), true)
		return reply.send(alarms)
	})

	// Send a test alert email
	// GET /api/alert/email/test
	fastify.get(API_ROOT + '/alert/email/test', async (request, reply) => {
		const r = await sendAlertEmail(true) // true to test
		return reply.send(r)
	})

	// Send a test alert email
	// GET /api/alert/email
	fastify.get(API_ROOT + '/alert/email', async (request, reply) => {
		const r = await composeAlertEmail(true) // true to test
		return reply.type('text/html; charset=utf-8').send(r)
	})

	done()
}


module.exports = alarmsRoutes
