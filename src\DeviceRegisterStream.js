const path = require('path');
const { createReadStream, readdirSync } = require('fs');
const { parse } = require('csv-parse');
const { Transform } = require('stream');
const { trends } = require('./Global');

/**
 * An object representing the resolution levels for plotting or data representation.
 * The values correspond to different granularities of time intervals.
 * 
 * @type {Object}
 * @property {number} fine - A finer resolution level, typically used for more detailed data with more points
 * @property {number} coarse - A coarser resolution level, used when fewer data points are needed for a broader view
 */
const resolutions = {
    fine: 10000,
    coarse: 2000
};

/**
 * Get file paths from a directory whose numeric day values fall within a slightly expanded date range.
 *
 * @param {string} directoryPath - Directory containing files named with numeric day values.
 * @param {{ start: number|string, end: number|string }} range - Date range for filtering files, in seconds.
 * @returns {string[]} Sorted list of file paths within the range.
 */
function getRecordsByRange(directoryPath, range) {
    const toMillis = sec => new Date(sec * 1000);
    const minTime = new Date(toMillis(range.start)).setDate(toMillis(range.start).getDate() - 2);
    const maxTime = new Date(toMillis(range.end)).setDate(toMillis(range.end).getDate() + 2);
    return readdirSync(directoryPath)
        .map(file => {
            const dayNum = parseInt(file.replace(/\D/g, ''), 10);
            const time = new Date(1970, 0, dayNum).getTime();
            return { path: path.join(directoryPath, file), time };
        })
        .filter(({ time }) => time >= minTime && time <= maxTime)
        .sort((a, b) => a.time - b.time)
        .map(({ path }) => path);
}

/**
 * Determines whether a timestamp falls exactly on an aligned interval boundary.
 *
 * @param {number} timestamp - The timestamp in seconds (or ms if your interval matches).
 * @param {number} interval - The fixed time interval in seconds.
 * @returns {boolean} True if aligned, false otherwise.
 */
function isOnTimeBoundary(timestamp, interval) {
    return Math.abs(timestamp % interval) === 0;
}

/**
 * Determines the ideal time interval for plotting or data representation based on a specified time range 
 * and the desired number of total data points. The function finds the smallest interval from a predefined 
 * set of intervals that will allow the total number of points to fit within the given time range.
 *
 * @param {Object} timeRange - The time range object that contains `start` and `end` Date objects.
 * @param {Date} timeRange.start - The start time of the time range.
 * @param {Date} timeRange.end - The end time of the time range.
 * @param {number} [idealTotalPoints=300] - The target number of data points to fit within the time range.
 *                                           Defaults to 300 if not provided.
 * 
 * @returns {number} - The selected time interval (in minutes)
 */
const getIdealInterval = (timeRange, idealTotalPoints = 300) => {
    const intervals = [
        1,       // 1 minute
        5,       // 5 minutes
        15,      // 15 minutes
        30,      // 30 minutes
        60,      // 1 hour
        120,     // 2 hours
        180,     // 3 hours
        360,     // 6 hours
        1440,    // 1 day (24 hours * 60 minutes)
    ];
    const duration = (timeRange.end - timeRange.start) / 60; // seconds to minutes
    const required = duration / idealTotalPoints;
    return intervals.find(i => i >= required) ?? intervals.at(-1);
};

class DeviceRegisterProcessor {
    /**
     * Initializes a new instance of the class with metadata for the device and its registers.
     *
     * @constructor
     * @param {Object} metadata - The metadata object containing device and register information.
     * @param {string} metadata.macid - The unique MAC ID of the device.
     * @param {string} metadata.mac - The MAC address of the device.
     * @param {string} metadata.model - The model identifier of the device.
     * @param {string} metadata.label - A short label or name for the device.
     * @param {string} metadata.description - A description of the device.
     * @param {Array<Object>} metadata.registers - An array of register metadata objects, each describing a register.
     *
     * @property {Object} info - Contains the basic device information (`macid`, `mac`, `model`, `label`, `description`).
     * @property {Array<string>} headers - The list of register metadata used for parsing and header generation.
     */
    constructor(metadata) {
        this.info = {
            macid: metadata.macid,
            mac: metadata.mac,
            model: metadata.model,
            label: metadata.label,
            description: metadata.description
        };

        this.headers = metadata.registers;
    }
}

class DeviceRegisterStreamProcessor {
    #processor;
    #headersSent = false;

    /**
     * Constructs a new DeviceRegisterStreamProcessor instance.
     * @param {Object} metadata - The metadata associated with the device registration.
     */
    constructor(metadata) {
        this.#processor = new DeviceRegisterProcessor(metadata);
    }

    /**
     * Parses an array of register values into appropriate data types based on their register definition.
     * @private
     * @param {Array} arrayStringValues - Array of string values to be parsed.
     * @returns {Array} - Array of parsed values, with types converted to their respective data types.
     */
    #parseRegisterValues(arrayStringValues) {
        return arrayStringValues.map((value, index) => {
            const register = this.#processor.headers[index];
            if (!register || value === 'null' || value === '') return null;
            return Number(value)
        });
    }

    /**
     * Creates a stream transformer to process and format the stream data.
     * @returns {Transform} - A Node.js Transform stream to process incoming data.
     */
    createStreamTransformer() {
        const self = this;
        return new Transform({
            objectMode: true,
            construct(callback) {
                if (!self.#headersSent) {
                    self.#headersSent = true;
                    this.push(self.#processor.headers);
                }
                callback();
            },
            transform(chunk, _, callback) {
                this.push(self.#parseRegisterValues(chunk));
                callback();
            }
        });
    }

    /**
     * Streams and processes multiple CSV files, applying filters and transformations on the data.
     * Writes the results to the response object.
     * @param {Array} directoryPath - Paths to the CSV files to be processed.
     * @param {Object} res - The response object to write the output data to.
     * @param {Object} timeRange - An object containing start and end Date objects to filter the data by timestamp.
     * @param {Date} timeRange.start - The start timestamp for the time range.
     * @param {Date} timeRange.end - The end timestamp for the time range.
     */
    streamChainFiles(directoryPath, query, buffer) {
        const self = this;
        const range = {
            start: Number(query.t0),
            end: Number(query.t1)
        };

        const csvFilePaths = getRecordsByRange(directoryPath, range);

        // Default to "Fine" resolution if not provided, and lowercase for consistency
        const resolution = (query.resolution ?? "Fine").toLowerCase();

        // Calculate the ideal interval in seconds
        const intervalModulo = (getIdealInterval(range, resolutions[resolution]) * 60) // In minutes, to seconds.

        // Adjust range.start to one interval before the original start
        range.start -= intervalModulo; // Move back by one interval

        const checkRange = ([timestamp]) => timestamp >= range.start && timestamp <= range.end;

        const trend = trends[query.type];
        const registerFilter = trend.values;
        const trendRegisterIndexes = registerFilter.map(filterItem =>
            self.#processor.headers.findIndex(
                header => header.device === filterItem.device && header.register === filterItem.register
            )
        );

        // Extract and merge trend filters with matching headers
        const filterHeaderRow = () => [
            ...trendRegisterIndexes.map((i, idx) =>
                i > -1 ? {
                    ...self.#processor.headers[i],
                    ...registerFilter[idx]
                } : null
            ).filter(Boolean)
        ];

        function filterDataRow(data) {
            return [data[0], ...trendRegisterIndexes.map(i => data[i + 1])];
        }

        function processNextFile(index) {
            if (index >= csvFilePaths.length) {
                buffer.push(null)
                return;
            }

            const csvFilePath = csvFilePaths[index];
            createReadStream(csvFilePath)
                .pipe(parse({ skip_records_with_error: true }))
                .pipe(self.createStreamTransformer())
                .on('data', (data) => {
                    // Check if the data is an object (header row) or an array (data row)
                    if (typeof data[0] === 'object') buffer.push(JSON.stringify(filterHeaderRow()) + '\n');
                    else {
                        if (!checkRange(data)) return;
                        if (isOnTimeBoundary(data[0], intervalModulo)) buffer.push(JSON.stringify(filterDataRow(data)) + '\n');
                    }
                })
                .on('end', () => processNextFile(index + 1))
                .on('error', (err) => buffer.destroy());
        }

        // Start processing the first file in the array
        processNextFile(0);
    }
}

module.exports = { DeviceRegisterStreamProcessor } 