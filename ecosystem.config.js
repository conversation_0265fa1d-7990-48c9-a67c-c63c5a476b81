// ecosystem.config.js
module.exports = {
    apps: [
        {
            name: "my-node-app",
            script: "public/src/main.js", // Correct path to your main entry file
            exec_mode: "fork",
            instances: 1, // Start with 1 for embedded systems
            // PM2's stdout/stderr are redirected to null because <PERSON> manages logging
            output: "/dev/null",
            error: "/dev/null",
            env: {
                NODE_ENV: "production",
                LOG_DIR: "/var/log/my-app", // Critical: Directory where <PERSON> writes error logs
            },
            log_rotate: false, // Let <PERSON> handle error log rotation
            max_memory_restart: "150M", // Example: Adjust based on your system's actual RAM and app's needs
            watch: false, // Disable in production for performance
            ignore_watch: ["node_modules", "logs", "*.log"], // Files/dirs to ignore if watch is enabled (though it's false)
        }
    ]
};