<!-- <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloud Status Monitor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .connection-status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .connecting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Cloud Status Monitor</h1>
        
        <div id="connectionStatus" class="connection-status connecting">
            Connecting...
        </div>
        
        <div>
            <button id="connectBtn" onclick="connect()">Connect</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <h3>Server URL:</h3>
        <input type="text" id="serverUrl" value="https://pvapi2.netmeter.cloud" style="width: 100%; padding: 8px; margin-bottom: 20px;">
        
        <h3>Latest Cloud Status:</h3>
        <div id="latestStatus" class="status">No data received yet...</div>
        
        <h3>Connection Log:</h3>
        <div id="logContainer" style="max-height: 400px; overflow-y: auto;">
        </div>
    </div>

    <script>
        let eventSource = null;
        
        function updateConnectionStatus(status, message) {
            const statusEl = document.getElementById('connectionStatus');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            statusEl.className = `connection-status ${status}`;
            statusEl.textContent = message;
            
            if (status === 'connected') {
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }
        
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'status';
            logEntry.style.fontSize = '12px';
            logEntry.style.marginBottom = '5px';
            
            if (type === 'error') {
                logEntry.style.backgroundColor = '#f8d7da';
                logEntry.style.borderColor = '#f5c6cb';
            } else if (type === 'success') {
                logEntry.style.backgroundColor = '#d4edda';
                logEntry.style.borderColor = '#c3e6cb';
            }
            
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function connect() {
            const serverUrl = document.getElementById('serverUrl').value.trim();
            if (!serverUrl) {
                alert('Please enter a server URL');
                return;
            }
            
            if (eventSource) {
                eventSource.close();
            }
            
            updateConnectionStatus('connecting', 'Connecting...');
            addLog('Attempting to connect to: ' + serverUrl + '/api/status/cloud');
            
            try {
                eventSource = new EventSource(serverUrl + '/api/status/cloud');
                
                eventSource.onopen = function(event) {
                    updateConnectionStatus('connected', 'Connected to cloud status stream');
                    addLog('Successfully connected to SSE stream', 'success');
                };
                
                eventSource.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        document.getElementById('latestStatus').textContent = JSON.stringify(data, null, 2);
                        addLog('Received cloud status update');
                    } catch (e) {
                        addLog('Error parsing received data: ' + e.message, 'error');
                        document.getElementById('latestStatus').textContent = 'Error: ' + event.data;
                    }
                };
                
                eventSource.onerror = function(event) {
                    updateConnectionStatus('disconnected', 'Connection error or disconnected');
                    addLog('SSE connection error occurred', 'error');
                };
                
            } catch (error) {
                updateConnectionStatus('disconnected', 'Failed to connect');
                addLog('Connection failed: ' + error.message, 'error');
            }
        }
        
        function disconnect() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                updateConnectionStatus('disconnected', 'Disconnected');
                addLog('Manually disconnected from SSE stream');
            }
        }
        
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
        }
        
        // Auto-connect on page load (optional)
        // window.onload = function() {
        //     connect();
        // };
        
        // Clean up on page unload
        window.onbeforeunload = function() {
            if (eventSource) {
                eventSource.close();
            }
        };
    </script>
</body>
</html> -->

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloud Status Monitor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
        }
        .status-box {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .connection {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
        .connecting { background: #fff3cd; color: #856404; }
        .error { background: #f8d7da; }
        .success { background: #d4edda; }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .log { max-height: 300px; overflow-y: auto; }
        .log-entry { font-size: 12px; margin: 2px 0; }
    </style>
</head>
<body>
    <h1>cloud communication status</h1>
    
    <div id="status" class="connection connecting">Connecting...</div>
    
    <button id="connectBtn" onclick="connect()">Connect</button>
    <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
    <button onclick="clearLog()">Clear Log</button>
    
    <h3>server URL:</h3>
    <input type="text" id="serverUrl" value="https://pvapi2.netmeter.cloud">
    
    <h3>SSE updated cloud status:</h3>
    <div id="latestStatus" class="status-box">No data received yet...</div>
    
    <h3>SSE logs:</h3>
    <div id="logContainer" class="status-box log"></div>

    <script>
        let eventSource = null;
        
        function updateStatus(type, message) {
            const el = document.getElementById('status');
            el.className = `connection ${type}`;
            el.textContent = message;
            
            document.getElementById('connectBtn').disabled = (type === 'connected');
            document.getElementById('disconnectBtn').disabled = (type !== 'connected');
        }
        
        function addLog(message, type = 'info') {
            const container = document.getElementById('logContainer');
            const time = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${time}] ${message}`;
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }
        
        function connect() {
            const url = document.getElementById('serverUrl').value.trim();
            if (!url) {
                alert('Please enter a server URL');
                return;
            }
            
            if (eventSource) eventSource.close();
            
            updateStatus('connecting', 'Connecting...');
            addLog('Connecting to: ' + url + '/api/status/cloud');
            
            try {
                eventSource = new EventSource(url + '/api/status/cloud');
                
                eventSource.onopen = () => {
                    updateStatus('connected', 'Connected');
                    addLog('Connected successfully', 'success');
                };
                
                eventSource.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        document.getElementById('latestStatus').textContent = JSON.stringify(data, null, 2);
                        addLog('Status update received');
                    } catch (e) {
                        addLog('Parse error: ' + e.message, 'error');
                        document.getElementById('latestStatus').textContent = 'Error: ' + event.data;
                    }
                };
                
                eventSource.onerror = () => {
                    updateStatus('disconnected', 'Connection Error');
                    addLog('Connection error', 'error');
                };
                
            } catch (error) {
                updateStatus('disconnected', 'Failed to Connect');
                addLog('Failed: ' + error.message, 'error');
            }
        }
        
        function disconnect() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                updateStatus('disconnected', 'Disconnected');
                addLog('Disconnected');
            }
        }
        
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
        }
        
        window.onbeforeunload = () => {
            if (eventSource) eventSource.close();
        };
    </script>
</body>
</html>