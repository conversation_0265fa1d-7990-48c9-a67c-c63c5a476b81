import{Q}from"./QSelect-BNWcW_ch.js";import{i as T,r as c,f as N,o as $,a as q,D as k,F as S,G as v,X as d,H as i,E,ag as V,S as b,O as j}from"./index-CzmOWWdj.js";import{Q as A}from"./QTable-CsCiCbH_.js";import{Q as G}from"./QPage-Disdm55v.js";import{_ as L,m as O,e as F}from"./export-file-CL4zo4TZ.js";import{S as H}from"./SseSpinner-HLkgS90o.js";import{_ as x}from"./AqPersistToggleButton-BcqzKwP0.js";import{u as R}from"./PersistObject-BVaCPhKL.js";import{u as I}from"./use-quasar-Li8tSQ3f.js";import"./use-key-composition-CoMUTTxZ.js";import"./use-timeout-DeCFbuIx.js";import"./QDialog-Cuvxr_1w.js";import"./focusout-C-pmmZED.js";import"./QMenu-D3shCIOy.js";import"./QSeparator-D-fNGoQY.js";import"./QList-yWBbetAh.js";import"./QCheckbox-BLQMX8bg.js";import"./use-checkbox-DDSE4paG.js";import"./Login-BVtZ5S7B.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const K={class:"a-container-lg q-mb-xl"},X={class:"col-sm col-12"},z={class:"row q-col-gutter-sm"},J={class:"col"},W={class:"col-auto"},he={__name:"RegistersPage",setup(Y){const C=I(),w=T("$api"),h=[{name:"i",label:"#",align:"center",field:"i",sortable:!0},{name:"address",label:"MB",align:"right",field:"address",sortable:!0},{name:"device",label:"Device",align:"right",field:"device",sortable:!0},{name:"name",label:"Register",align:"left",field:"name",sortable:!0},{name:"value",label:"Value",align:"right",field:"asText",sortable:!0,classes:e=>e.error?"table-cell-error-text":""},{name:"units",label:"Units",align:"left",field:"units",sortable:!0},{name:"raw",label:"Raw Value",align:"right",field:"raw",sortable:!0,classes:e=>e.error?"table-cell-error-text":""},{name:"display",label:"Display",align:"center",field:"display",sortable:!0},{name:"updated",label:"Updated",align:"center",field:"updated",sortable:!0,format:e=>e?O(e):"-"},{name:"error",label:"Error",align:"center",field:"error",sortable:!0,format:()=>"",classes:e=>e.error?"table-cell-is-error":"table-cell-is-ok"},{name:"comment",label:"Comment",align:"left",field:"comment",sortable:!0,classes:"ellipsis table-col-comment"}],m=c(!0),p=c(!0),l=R("registerStatusTablePagination",{sortBy:"i",descending:!1,page:1,rowsPerPage:0}),o=R("regStatusDeviceSelect","AnalogIn"),g=c([]),P=c(null),n=c([]),D=e=>{console.log("RegistersPage onDeviceSelect: ",e),f()},U=e=>{P.value=e,p.value&&f()};N(()=>{w.get("/info").then(e=>{console.log("RegistersPage onMounted: ",e),g.value=e.data.devices}).then(()=>{f()}),window.addEventListener("keydown",_)}),$(()=>{window.removeEventListener("keydown",_)});function _(e){switch(e.code){case"NumpadAdd":l.value.page=Math.min(B.value,l.value.page+1);break;case"NumpadSubtract":l.value.page=Math.max(1,l.value.page-1);break}}const B=q(()=>Math.ceil(n.value.length/l.value.rowsPerPage)),f=()=>{o.value===null||!g.value.includes(o.value)||w.get("/status/registers/"+o.value).then(e=>{n.value=e.data})};function y(e,t,a){let s=t!==void 0?t(e,a):e;return s=s==null?"":String(s),s=s.split('"').join('""'),`"${s}"`}const M=()=>{const e=h,t=n.value,a=[e.map(u=>y(u.label))].concat(t.map(u=>e.map(r=>y(typeof r.field=="function"?r.field(u):u[r.field===void 0?r.name:r.field],r.format,u)).join(","))).join(`\r
`);F("registers-"+o.value+".csv",a,"text/csv")!==!0&&C.notify({message:"Browser denied file download...",color:"negative",icon:"warning"})};return(e,t)=>(S(),k(G,null,{default:v(()=>[d("div",K,[i(L,{title:"Register Status",icon:"svguse:icons.svg#io"},{default:v(()=>[d("div",X,[d("div",z,[d("div",J,[i(Q,{outlined:"",modelValue:b(o),"onUpdate:modelValue":[t[0]||(t[0]=a=>V(o)?o.value=a:null),D],options:g.value,label:"Select Register Group"},null,8,["modelValue","options"])]),d("div",W,[i(x,{round:"",flat:"",modelValue:p.value,"onUpdate:modelValue":t[1]||(t[1]=a=>p.value=a),"local-store-key":"regStatusAutoUpdate","default-value":!0,"color-true":"primary","color-false":"grey-5","icon-true":"play_circle","icon-false":"pause_circle","tooltip-true":"Click to disable auto updates","tooltip-false":"Click to enable auto updates"},null,8,["modelValue"]),i(H,{onMessage:U})])])])]),_:1}),n.value.length>0?(S(),k(A,{key:0,"rows-per-page-label":"Registers per page","rows-per-page-options":[10,20,25,30,35,50,100,0],class:"table-row-highlight q-mt-md",dense:m.value,flat:"",bordered:"",title:b(o),rows:n.value,columns:h,"row-key":"name",pagination:b(l),"onUpdate:pagination":t[3]||(t[3]=a=>V(l)?l.value=a:null)},{"top-right":v(()=>[i(x,{class:"q-mr-md",round:"",flat:"",modelValue:m.value,"onUpdate:modelValue":t[2]||(t[2]=a=>m.value=a),"local-store-key":"regStatusTableDense","default-value":!0,"color-true":"grey-5","color-false":"grey-5","icon-true":"compress","icon-false":"expand","tooltip-true":"Click to expand the table spacing","tooltip-false":"Click to make the table compact"},null,8,["modelValue"]),i(j,{color:"primary","icon-right":"archive",label:"Export to csv","no-caps":"",onClick:M})]),_:1},8,["dense","title","rows","pagination"])):E("",!0)])]),_:1}))}};export{he as default};
