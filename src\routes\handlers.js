//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//*
//* routes/data.js
//*
//* Server Sent Events
//*============================================================================================
const fs = require('fs')

/**
 * Handles POST requests for file operations, writes data to a file, and optionally triggers a callback function.
 *
 * @param {object} request - The Fastify request object containing the incoming request data.
 * @param {object} reply - The Fastify reply object for sending responses.
 * @param {string} fileName - The path to the file where the data should be written.
 * @param {Function} [initFunction] - Optional callback function to execute after writing the file.
 * @param {boolean} [noReply=false] - If true, suppresses the reply with the file contents.
 * @returns {object|void} Returns a Fastify reply with the file contents or error response, or nothing if noReply is true.
 *
 * @throws Will throw and propagate any filesystem errors from writeFileSync or readFileSync.
 *
 */
function postFileHandler(request, reply, fileName, initFunction, noReply = false)
{
		const data = request.body.data
		if(!Array.isArray(data) && typeof data !== 'object')
		{
			return reply.code(400).send({ error: 'Invalid data, a JSON array or object is required' })
		}
		fs.writeFileSync(fileName, JSON.stringify(data, null, '\t'))

		if (typeof initFunction === 'function') {
			initFunction() // Apply the updated settings
		}

		if(!noReply)
		{
			const r = fs.readFileSync(fileName, 'utf8')
			return reply.type('application/json').send(r)
		}
}

module.exports = {
	postFileHandler
}