//*********************************************************************************************
//* COPYRIGHT © 2025-, Michael <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of Michael <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//* HttpServerAuth.js
//******************************************************************************************
//*
//* This is the non-application specific authentication module for the HttpServer.
//*
//******************************************************************************************
/**
 * @module HttpServerAuth
 * @description
 * Implements the authentication methods for Basic Authentication and JWT Authentication
 *
 *  This is meant to be used with Fastify but is agnostic about:
 *  - How username and password are verified for Basic Authentication
 *    - `user`/`pass` is verified by a function that is passed to authBasic
 *  - How the JWT payload is generated (this is application dependent)
 * -----------------------------------------------------------------------------------------
 * The strategy is to use Basic Authentication for the initial login to provide a JWT.
 * Then JWT Authentication is used for the protected REST API.
 *
 * The typical steps are:
 * 1. Client sends a GET request to the login endpoint with the username and password using
 *    Basic Authentication
 * 2. Fastify router receives the request and `authBasic` is called via the `preHandler` :
 *    - `authBasic` decodes the Authorization header to get the username and password
 *    - `authBasic` calls the  and password verifier function that was passed
 * 2. The application calls `jwtSign` to generate a JWT token with the information from the app
 * 3. The application stores the token for subsequent requests to the protected routes of the API
 * 4. The client sends a GET request to the protected endpoint with the JWT in the Authorization header (`Authorization: Bearer <token>`)
 * 5. `authJwt` is called:
 *    - `authJwt` calls the `jwtVerify` function to verify the JWT
 */
//******************************************************************************************

// import jwt from 'jsonwebtoken' // https://github.com/auth0/node-jsonwebtoken
const jwt = require('jsonwebtoken') // https://github.com/auth0/node-jsonwebtoken

//const secret = process.env.JWT_SECRET || 'My-Secret'

//------------------------------------------------------------------------------------------
/**
 * authBasic - Basic Authentication for Fastify
 * @param {object} req - Request object from Fastify
 * @param {object} reply - Reply object from Fastify
 * @param {function} done - Fastify callback
 * @param {function} verify - application dependent function to verify the user/password
 * - the verify function should return the user information (which it typically stored in the token),
 * or false if the user/password is invalid
 * @returns {void}
 * - When successful, `req.user` is set to the user information returned by the verify function
 */
//------------------------------------------------------------------------------------------
function authBasic(req, reply, done, verify)
{
	console.log('1) authBasic:', req.headers.authorization, typeof done)
	const authHeader = req.headers.authorization
	if (!authHeader)
	{
		reply.code(401).send({ message: 'Authorization header required' })
		return
	}

	const authParts = authHeader.split(/\s+/)
	const auth = new Buffer.from(authParts[1], 'base64').toString().split(':');
	const user = auth[0];
	const pass = auth[1];
	console.log('2) authBasic:', user, pass)

	// Verify the user/password
	// if (user != 'admin' || pass != 'praevista')
	const data = verify(user, pass)
	if(!data)
	{
		reply.code(401).send({ message: 'The supplied username/password combination is invalid' })
		return
	}
	req.user = data

	// If we get here, the user/password is valid
	done()
}


//------------------------------------------------------------------------------------------
/**
 * Sign (create) a JWT token
 * @param {object} data - data to be stored in the token
 * @param {string} secret  - secret used to sign the token
 * @param {string} expiresIn  - How long the token is valid for
 * @returns {string} - JWT token
 */
//------------------------------------------------------------------------------------------
function jwtSign(data, secret, expiresIn)
{
	return jwt.sign(data, secret, { expiresIn: expiresIn || '1w' })
}

//------------------------------------------------------------------------------------------
/**
 * Verify (decode) a JWT token
 * @param {string} token - JWT token
 * @param {string} secret  - secret used to sign the token
 * @returns {object} - decoded token
 */
//------------------------------------------------------------------------------------------
function jwtVerify(token, secret)
{
	try {
		return jwt.verify(token, secret)
	}
	catch (e) {
	}
	return null
}

// // Test JWT
// const testTokenSign = jwtSign({ user: 'admin', role: 'admin' }, 'My-Secret')
// const testTokenVerify = jwtVerify(testTokenSign, 'My-Secret')
// console.log('-------------Test JWT -------------')
// console.log('      testToken:', testTokenSign)
// console.log('testTokenVerify:', testTokenVerify)

//------------------------------------------------------------------------------------------
/**
 * authJWT - JWT Authentication for Fastify
 * @param {object} req - Request object from Fastify
 * @param {object} reply - Reply object from Fastify
 * @param {function} done - Fastify callback
 * @returns {void}
 */
//------------------------------------------------------------------------------------------
function authJWT(req, reply, done)
{
	console.log('1) authJWT:', req.headers.authorization, typeof done)
	const authHeader = req.headers.authorization
	if (!authHeader)
	{
		reply.code(401).send({ message: 'No authorization header' })
		return
	}

	const authParts = authHeader.split(/\s+/)
	const auth = new Buffer.from(authParts[1], 'base64').toString().split(':');
	const user = auth[0];
	const pass = auth[1];
	console.log('2) authBasic:', user, pass)

	if (user != 'admin' || pass != 'praevista') {
		reply.code(401).send({ message: 'The supplied username/password combination is invalid' })
		return
	}
	done()
}

console.log('------------- HttpServerAuth Loaded -------------')

// export {
// 	authBasic,
// 	jwtSign,
// 	jwtVerify,
// 	authJWT,
// }
// export default {
// 	authBasic,
// 	jwtSign,
// 	jwtVerify,
// 	authJWT,
// }

module.exports = {
	authBasic,
	jwtSign,
	jwtVerify,
	authJWT,
}