import{Q as K}from"./QInput-CCOJFSf5.js";import{Q as Xe}from"./QSeparator-D-fNGoQY.js";import{c as Ge,g as Je,r as S,a as c,w as N,n as Ie,a7 as Re,b as Ze,h as d,a9 as el,aa as ll,j as re,O as de,a0 as tl,K as al,D as ce,F as le,G as k,H as f,M as H,f as ul,X as D,T as nl,E as be,S as F,Q as Ve,U as ol,I as il,J as sl}from"./index-CzmOWWdj.js";import{u as ke,b as rl,c as dl,_ as ee,g as cl,f as vl,Q as we,a as ml}from"./QPopupProxy-D3bKa31G.js";import{T as fl}from"./TouchPan-C63yVDWw.js";import{e as hl,a as pl,c as _l,g as gl,f as yl}from"./use-key-composition-CoMUTTxZ.js";import{p as A}from"./focusout-C-pmmZED.js";import{Q as bl}from"./QPage-Disdm55v.js";import{C as qe}from"./ClosePopup-DhH_6eqd.js";import{_ as Vl}from"./PageHeading-CorBRYVa.js";import{_ as kl}from"./AqArrayForm-C93bdg5l.js";import{_ as xe}from"./AqPersistToggleButton-BcqzKwP0.js";import{i as wl}from"./Login-BVtZ5S7B.js";import{r as ql}from"./SaveButton-DQ7yxOUN.js";import{Q as xl}from"./QSelect-BNWcW_ch.js";import{d as Ml}from"./Debug-gcimLaHD.js";import"./QDialog-Cuvxr_1w.js";import"./use-timeout-DeCFbuIx.js";import"./QMenu-D3shCIOy.js";import"./QSpace-i2TdLNVM.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";function Cl(t,g){return t.hour!==null&&t.minute===null?"minute":"hour"}function Dl(){const t=new Date;return{hour:t.getHours(),minute:t.getMinutes(),second:t.getSeconds(),millisecond:t.getMilliseconds()}}const Sl=Ge({name:"QTime",props:{...pl,...hl,...ke,modelValue:{required:!0,validator:t=>typeof t=="string"||t===null},mask:{...ke.mask,default:null},format24h:{type:Boolean,default:null},defaultDate:{type:String,validator:t=>/^-?[\d]+\/[0-1]\d\/[0-3]\d$/.test(t)},options:Function,hourOptions:Array,minuteOptions:Array,secondOptions:Array,withSeconds:Boolean,nowBtn:Boolean},emits:rl,setup(t,{slots:g,emit:C}){const h=Je(),{$q:m}=h.proxy,U=_l(t,m),{tabindex:w,headerClass:q,getLocale:r,getCurrentDate:o}=dl(t,m),p=gl(t),b=yl(p);let P,j;const ve=S(null),Y=c(()=>Pe()),E=c(()=>r()),me=c(()=>Ae()),L=ee(t.modelValue,Y.value,E.value,t.calendar,me.value),s=S(Cl(L)),a=S(L),_=S(L.hour===null||L.hour<12),De=c(()=>`q-time q-time--${t.landscape===!0?"landscape":"portrait"}`+(U.value===!0?" q-time--dark q-dark":"")+(t.disable===!0?" disabled":t.readonly===!0?" q-time--readonly":"")+(t.bordered===!0?" q-time--bordered":"")+(t.square===!0?" q-time--square no-border-radius":"")+(t.flat===!0?" q-time--flat no-shadow":"")),te=c(()=>{const e=a.value;return{hour:e.hour===null?"--":V.value===!0?A(e.hour):String(_.value===!0?e.hour===0?12:e.hour:e.hour>12?e.hour-12:e.hour),minute:e.minute===null?"--":A(e.minute),second:e.second===null?"--":A(e.second)}}),V=c(()=>t.format24h!==null?t.format24h:m.lang.date.format24h),Se=c(()=>{const e=s.value==="hour",l=e===!0?12:60,u=a.value[s.value];let v=`rotate(${Math.round(u*(360/l))-180}deg) translateX(-50%)`;return e===!0&&V.value===!0&&a.value.hour>=12&&(v+=" scale(.7)"),{transform:v}}),fe=c(()=>a.value.hour!==null),He=c(()=>fe.value===!0&&a.value.minute!==null),B=c(()=>t.hourOptions!==void 0?e=>t.hourOptions.includes(e):t.options!==void 0?e=>t.options(e,null,null):null),W=c(()=>t.minuteOptions!==void 0?e=>t.minuteOptions.includes(e):t.options!==void 0?e=>t.options(a.value.hour,e,null):null),z=c(()=>t.secondOptions!==void 0?e=>t.secondOptions.includes(e):t.options!==void 0?e=>t.options(a.value.hour,a.value.minute,e):null),M=c(()=>{if(B.value===null)return null;const e=G(0,11,B.value),l=G(12,11,B.value);return{am:e,pm:l,values:e.values.concat(l.values)}}),Q=c(()=>W.value!==null?G(0,59,W.value):null),$=c(()=>z.value!==null?G(0,59,z.value):null),X=c(()=>{switch(s.value){case"hour":return M.value;case"minute":return Q.value;case"second":return $.value}}),Ue=c(()=>{let e,l,u=0,i=1;const v=X.value!==null?X.value.values:void 0;s.value==="hour"?V.value===!0?(e=0,l=23):(e=0,l=11,_.value===!1&&(u=12)):(e=0,l=55,i=5);const x=[];for(let n=e,y=e;n<=l;n+=i,y++){const O=n+u,We=v?.includes(O)===!1,ze=s.value==="hour"&&n===0?V.value===!0?"00":"12":n;x.push({val:O,index:y,disable:We,label:ze})}return x}),Oe=c(()=>[[fl,Be,void 0,{stop:!0,prevent:!0,mouse:!0}]]);N(()=>t.modelValue,e=>{const l=ee(e,Y.value,E.value,t.calendar,me.value);(l.dateHash!==a.value.dateHash||l.timeHash!==a.value.timeHash)&&(a.value=l,l.hour===null?s.value="hour":_.value=l.hour<12)}),N([Y,E],()=>{Ie(()=>{se()})});function he(){const e={...o(),...Dl()};se(e),Object.assign(a.value,e),s.value="hour"}function G(e,l,u){const i=Array.apply(null,{length:l+1}).map((v,x)=>{const n=x+e;return{index:n,val:u(n)===!0}}).filter(v=>v.val===!0).map(v=>v.index);return{min:i[0],max:i[i.length-1],values:i,threshold:l+1}}function pe(e,l,u){const i=Math.abs(e-l);return Math.min(i,u-i)}function ae(e,{min:l,max:u,values:i,threshold:v}){if(e===l)return l;if(e<l||e>u)return pe(e,l,v)<=pe(e,u,v)?l:u;const x=i.findIndex(O=>e<=O),n=i[x-1],y=i[x];return e-n<=y-e?n:y}function Pe(){return t.calendar!=="persian"&&t.mask!==null?t.mask:`HH:mm${t.withSeconds===!0?":ss":""}`}function Ae(){if(typeof t.defaultDate!="string"){const e=o(!0);return e.dateHash=cl(e),e}return ee(t.defaultDate,"YYYY/MM/DD",void 0,t.calendar)}function ue(){return el(h)===!0||X.value!==null&&(X.value.values.length===0||s.value==="hour"&&V.value!==!0&&M.value[_.value===!0?"am":"pm"].values.length===0)}function ne(){const e=ve.value,{top:l,left:u,width:i}=e.getBoundingClientRect(),v=i/2;return{top:l+v,left:u+v,dist:v*.7}}function Be(e){if(ue()!==!0){if(e.isFirst===!0){P=ne(),j=J(e.evt,P);return}j=J(e.evt,P,j),e.isFinal===!0&&(P=!1,j=null,_e())}}function _e(){s.value==="hour"?s.value="minute":t.withSeconds&&s.value==="minute"&&(s.value="second")}function J(e,l,u){const i=Re(e),v=Math.abs(i.top-l.top),x=Math.sqrt(Math.pow(Math.abs(i.top-l.top),2)+Math.pow(Math.abs(i.left-l.left),2));let n,y=Math.asin(v/x)*(180/Math.PI);if(i.top<l.top?y=l.left<i.left?90-y:270+y:y=l.left<i.left?y+90:270-y,s.value==="hour"){if(n=y/30,M.value!==null){const O=V.value!==!0?_.value===!0:M.value.am.values.length!==0&&M.value.pm.values.length!==0?x>=l.dist:M.value.am.values.length!==0;n=ae(n+(O===!0?0:12),M.value[O===!0?"am":"pm"])}else n=Math.round(n),V.value===!0?x<l.dist?n<12&&(n+=12):n===12&&(n=0):_.value===!0&&n===12?n=0:_.value===!1&&n!==12&&(n+=12);V.value===!0&&(_.value=n<12)}else n=Math.round(y/6)%60,s.value==="minute"&&Q.value!==null?n=ae(n,Q.value):s.value==="second"&&$.value!==null&&(n=ae(n,$.value));return u!==n&&Ye[s.value](n),n}const oe={hour(){s.value="hour"},minute(){s.value="minute"},second(){s.value="second"}};function Qe(e){e.keyCode===13&&ge()}function $e(e){e.keyCode===13&&ye()}function Te(e){ue()!==!0&&(m.platform.is.desktop!==!0&&J(e,ne()),_e())}function Fe(e){ue()!==!0&&J(e,ne())}function Ke(e){if(e.keyCode===13)s.value="hour";else if([37,39].includes(e.keyCode)){const l=e.keyCode===37?-1:1;if(M.value!==null){const u=V.value===!0?M.value.values:M.value[_.value===!0?"am":"pm"].values;if(u.length===0)return;if(a.value.hour===null)I(u[0]);else{const i=(u.length+u.indexOf(a.value.hour)+l)%u.length;I(u[i])}}else{const u=V.value===!0?24:12,i=V.value!==!0&&_.value===!1?12:0,v=a.value.hour===null?-l:a.value.hour;I(i+(24+v+l)%u)}}}function Ne(e){if(e.keyCode===13)s.value="minute";else if([37,39].includes(e.keyCode)){const l=e.keyCode===37?-1:1;if(Q.value!==null){const u=Q.value.values;if(u.length===0)return;if(a.value.minute===null)R(u[0]);else{const i=(u.length+u.indexOf(a.value.minute)+l)%u.length;R(u[i])}}else{const u=a.value.minute===null?-l:a.value.minute;R((60+u+l)%60)}}}function je(e){if(e.keyCode===13)s.value="second";else if([37,39].includes(e.keyCode)){const l=e.keyCode===37?-1:1;if($.value!==null){const u=$.value.values;if(u.length===0)return;if(a.value.seconds===null)Z(u[0]);else{const i=(u.length+u.indexOf(a.value.second)+l)%u.length;Z(u[i])}}else{const u=a.value.second===null?-l:a.value.second;Z((60+u+l)%60)}}}function I(e){a.value.hour!==e&&(a.value.hour=e,T())}function R(e){a.value.minute!==e&&(a.value.minute=e,T())}function Z(e){a.value.second!==e&&(a.value.second=e,T())}const Ye={hour:I,minute:R,second:Z};function ge(){_.value===!1&&(_.value=!0,a.value.hour!==null&&(a.value.hour-=12,T()))}function ye(){_.value===!0&&(_.value=!1,a.value.hour!==null&&(a.value.hour+=12,T()))}function ie(e){const l=t.modelValue;s.value!==e&&l!==void 0&&l!==null&&l!==""&&typeof l!="string"&&(s.value=e)}function T(){if(B.value!==null&&B.value(a.value.hour)!==!0){a.value=ee(),ie("hour");return}if(W.value!==null&&W.value(a.value.minute)!==!0){a.value.minute=null,a.value.second=null,ie("minute");return}if(t.withSeconds===!0&&z.value!==null&&z.value(a.value.second)!==!0){a.value.second=null,ie("second");return}a.value.hour===null||a.value.minute===null||t.withSeconds===!0&&a.value.second===null||se()}function se(e){const l=Object.assign({...a.value},e),u=t.calendar==="persian"?A(l.hour)+":"+A(l.minute)+(t.withSeconds===!0?":"+A(l.second):""):vl(new Date(l.year,l.month===null?null:l.month-1,l.day,l.hour,l.minute,l.second,l.millisecond),Y.value,E.value,l.year,l.timezoneOffset);l.changed=u!==t.modelValue,C("update:modelValue",u,l)}function Ee(){const e=[d("div",{class:"q-time__link "+(s.value==="hour"?"q-time__link--active":"cursor-pointer"),tabindex:w.value,onClick:oe.hour,onKeyup:Ke},te.value.hour),d("div",":"),d("div",fe.value===!0?{class:"q-time__link "+(s.value==="minute"?"q-time__link--active":"cursor-pointer"),tabindex:w.value,onKeyup:Ne,onClick:oe.minute}:{class:"q-time__link"},te.value.minute)];t.withSeconds===!0&&e.push(d("div",":"),d("div",He.value===!0?{class:"q-time__link "+(s.value==="second"?"q-time__link--active":"cursor-pointer"),tabindex:w.value,onKeyup:je,onClick:oe.second}:{class:"q-time__link"},te.value.second));const l=[d("div",{class:"q-time__header-label row items-center no-wrap",dir:"ltr"},e)];return V.value===!1&&l.push(d("div",{class:"q-time__header-ampm column items-between no-wrap"},[d("div",{class:"q-time__link "+(_.value===!0?"q-time__link--active":"cursor-pointer"),tabindex:w.value,onClick:ge,onKeyup:Qe},"AM"),d("div",{class:"q-time__link "+(_.value!==!0?"q-time__link--active":"cursor-pointer"),tabindex:w.value,onClick:ye,onKeyup:$e},"PM")])),d("div",{class:"q-time__header flex flex-center no-wrap "+q.value},l)}function Le(){const e=a.value[s.value];return d("div",{class:"q-time__content col relative-position"},[d(ll,{name:"q-transition--scale"},()=>d("div",{key:"clock"+s.value,class:"q-time__container-parent absolute-full"},[d("div",{ref:ve,class:"q-time__container-child fit overflow-hidden"},[re(d("div",{class:"q-time__clock cursor-pointer non-selectable",onClick:Te,onMousedown:Fe},[d("div",{class:"q-time__clock-circle fit"},[d("div",{class:"q-time__clock-pointer"+(a.value[s.value]===null?" hidden":t.color!==void 0?` text-${t.color}`:""),style:Se.value}),Ue.value.map(l=>d("div",{class:`q-time__clock-position row flex-center q-time__clock-pos-${l.index}`+(l.val===e?" q-time__clock-position--active "+q.value:l.disable===!0?" q-time__clock-position--disable":"")},[d("span",l.label)]))])]),Oe.value)])])),t.nowBtn===!0?d(de,{class:"q-time__now-button absolute",icon:m.iconSet.datetime.now,unelevated:!0,size:"sm",round:!0,color:t.color,textColor:t.textColor,tabindex:w.value,onClick:he}):null])}return h.proxy.setNow=he,()=>{const e=[Le()],l=Ze(g.default);return l!==void 0&&e.push(d("div",{class:"q-time__actions"},l)),t.name!==void 0&&t.disable!==!0&&b(e,"push"),d("div",{class:De.value,tabindex:-1},[Ee(),d("div",{class:"q-time__main col overflow-auto"},e)])}}}),Me={__name:"DurationWidget",props:tl({options:{type:Array,default:()=>[{value:"",label:"Event"},{value:"H",label:"Hours"},{value:"m",label:"Minutes"},{value:"s",label:"Seconds"},{value:"D",label:"Days"}]},label:String,VBind:{type:Object,default:()=>({})},Class:String},{modelValue:{type:String,default:"1d"},modelModifiers:{}}),emits:["update:modelValue"],setup(t){const g=al(t,"modelValue"),C=t,h=S(0),m=S("H"),U=S(!1),w=c(()=>C.options.map(o=>o.value)),q=c(()=>C.options[0]?.value||null),r={parse:o=>{const p=parseInt(o)||0,b=o.slice(-1);return[p,w.value.includes(b)?b:q.value]},format:(o,p)=>`${o}${p||""}`};return N(g,o=>{const[p,b]=r.parse(o);if(U.value=b===null,U.value){h.value=0;return}p!==h.value&&(h.value=p),b!==m.value&&(m.value=b)},{immediate:!0}),N([h,m],([o,p])=>{g.value=r.format(o,p)}),N(()=>C.options,o=>{const p=m.value;o.map(P=>P.value).includes(p)||(m.value=o[0]?.value||"H")},{deep:!0}),(o,p)=>(le(),ce(K,H({class:["duration-input",t.Class],label:t.label,type:"number",min:"0",step:"1",modelValue:h.value,"onUpdate:modelValue":p[1]||(p[1]=b=>h.value=b),modelModifiers:{number:!0}},t.VBind,{readonly:t.VBind.readonly?!0:U.value}),{append:k(()=>[f(xl,H({class:"duration-select",modelValue:m.value,"onUpdate:modelValue":p[0]||(p[0]=b=>m.value=b),options:t.options,"emit-value":"","map-options":""},t.VBind),null,16,["modelValue","options"])]),_:1},16,["label","modelValue","class","readonly"]))}},Hl={class:"a-container-lg q-mb-xl"},Ul={class:"q-gutter-sm"},Ol={class:"row full-width q-gutter-sm"},Pl={class:"col rounded-borders items-center"},Al={class:"row full-width q-gutter-sm"},Bl={class:"row items-center justify-end"},Ql={class:"row items-center justify-end"},$l={class:"col-xs-12 col-sm-4 col-md"},Tl={class:"col-xs-12 col-sm-4 col-md"},Ce="/setup/schedules",ot={__name:"SchedulesPage",setup(t){const g=c(()=>({dense:C.value,outlined:h.value,readonly:!h.value,standout:!h.value})),C=S(!0),h=S(!1),m=new ql({name:"",description:"",date:"2025-05-22",time:"00:00",repeat:"0h",duration:"0d"});ul(()=>{console.log("SchedulesPage mounted"),m.get(Ce)});function U(){m.post(Ce).then(w=>{console.log("postForm",w)})}return(w,q)=>(le(),ce(bl,null,{default:k(()=>[D("div",Hl,[f(Vl,{title:"Schedules",icon:"schedule"},{default:k(()=>[D("div",Ul,[F(wl)?(le(),ce(xe,{key:0,round:"",flat:"",modelValue:h.value,"onUpdate:modelValue":q[0]||(q[0]=r=>h.value=r),"local-store-key":"scheduleEdit","default-value":!1,"color-true":"primary","color-false":"grey-5","icon-true":"edit","icon-false":"edit_off","tooltip-true":"Click to disable editing","tooltip-false":"Click to enable editing"},null,8,["modelValue"])):be("",!0),f(xe,{round:"",flat:"",modelValue:C.value,"onUpdate:modelValue":q[1]||(q[1]=r=>C.value=r),"local-store-key":"scheduleShowDense","default-value":!0,"color-true":"grey-5","color-false":"grey-5","icon-true":"compress","icon-false":"expand","tooltip-true":"Click to expand the form spacing","tooltip-false":"Click to make the form compact"},null,8,["modelValue"])])]),_:1}),f(kl,{"reactive-array":F(m),onReset:q[2]||(q[2]=r=>F(m).reset()),onSubmit:U,enable:h.value,class:"q-pt-sm"},{default:k(({item:r})=>[D("div",Ol,[D("div",Pl,[f(K,H({"bg-color":"primary","input-class":"text-white text-h4",modelValue:r.name,"onUpdate:modelValue":o=>r.name=o},g.value),null,16,["modelValue","onUpdate:modelValue"]),f(Xe,{class:"q-my-xs"}),f(K,H({modelValue:r.description,"onUpdate:modelValue":o=>r.description=o,label:"Description"},g.value),null,16,["modelValue","onUpdate:modelValue"])])]),D("div",Al,[f(K,H({class:"col-xs-12 col-sm-4 col-md",modelValue:r.date,"onUpdate:modelValue":o=>r.date=o,label:"Date"},g.value),{append:k(()=>[f(Ve,{name:"event",class:"cursor-pointer"},{default:k(()=>[f(we,{cover:"","transition-show":"scale","transition-hide":"scale"},{default:k(()=>[f(ml,H({mask:"YYYY-MM-DD",modelValue:r.date,"onUpdate:modelValue":o=>r.date=o,"today-btn":""},g.value),{default:k(()=>[D("div",Bl,[re(f(de,{label:"Close",color:"primary",flat:""},null,512),[[qe]])])]),_:2},1040,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:2},1040,["modelValue","onUpdate:modelValue"]),f(K,H({class:"col-xs-12 col-sm-4 col-md",modelValue:r.time,"onUpdate:modelValue":o=>r.time=o,label:"Time"},g.value),{append:k(()=>[f(Ve,{name:"access_time",class:"cursor-pointer"},{default:k(()=>[f(we,{cover:"","transition-show":"scale","transition-hide":"scale"},{default:k(()=>[f(Sl,H({modelValue:r.time,"onUpdate:modelValue":o=>r.time=o,mask:"HH:mm",format24h:"","now-btn":""},g.value),{default:k(()=>[D("div",Ql,[re(f(de,{label:"Close",color:"primary",flat:""},null,512),[[qe]])])]),_:2},1040,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:2},1040,["modelValue","onUpdate:modelValue"]),D("div",$l,[f(Me,{label:"Repeat",modelValue:r.repeat,"onUpdate:modelValue":o=>r.repeat=o,options:[{value:null,label:"No Repeat"},{value:"D",label:"Days"},{value:"w",label:"Weeks"}],"-v-bind":g.value,"-class":"col-xs-12 col-sm-4 col-md"},null,8,["modelValue","onUpdate:modelValue","-v-bind"])]),D("div",Tl,[f(Me,{label:"Duration",modelValue:r.duration,"onUpdate:modelValue":o=>r.duration=o,options:[{value:null,label:"Event"},{value:"s",label:"Seconds"},{value:"m",label:"Minutes"},{value:"H",label:"Hours"},{value:"D",label:"Days"}],"-v-bind":g.value,"-class":"col-xs-12 col-sm-4 col-md"},null,8,["modelValue","onUpdate:modelValue","-v-bind"])])])]),_:1},8,["reactive-array","enable"]),F(Ml)?(le(),nl(ol,{key:0},[il(sl(F(m).value),1)],64)):be("",!0)])]),_:1}))}};export{ot as default};
