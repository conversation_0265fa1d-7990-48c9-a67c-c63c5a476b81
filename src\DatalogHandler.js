const fs = require("fs")
const path = require("path");
const readline = require('readline');
const { Readable, Transform } = require("stream");

class DatalogLineReader {
    #filePaths;
    #currentFileIndex = 0;
    #currentStream = null;
    #lineReader = null;
    #lineIterator = null;

    constructor(paths) {
        this.#filePaths = paths;
    }

    #openNextFile() {
        if (this.#currentStream) {
            this.#currentStream.close();
        }
        if (this.#currentFileIndex >= this.#filePaths.length) {
            return false;
        }
        const path = this.#filePaths[this.#currentFileIndex++];
        this.#currentStream = fs.createReadStream(path);
        this.#lineReader = readline.createInterface({
            input: this.#currentStream,
            crlfDelay: Infinity,
        });
        this.#lineIterator = this.#lineReader[Symbol.asyncIterator]();
        return true;
    }

    async nextLine() {
        while (true) {
            if (!this.#lineIterator) {
                if (!this.#openNextFile()) return null;
            }
            const { value, done } = await this.#lineIterator.next();
            if (done) {
                this.#lineIterator = null;
                continue;
            }
            return value.trim();
        }
    }
}

// This probably should be Global
const resolutions = {
    fine: 10000,
    coarse: 2000
};

/**
 * @typedef {Object} MetadataPaths
 * @property {string} csv - Path to the CSV metadata file
 * @property {string} json - Path to the JSON metadata file
 */

/**
 * Handles loading, parsing, and streaming datalog files.
 */
class DatalogHandler {
    /** @type {string} */
    #datalogPath

    /** @type {{csv: string|null, json: object|null}} */
    #metadata = {
        csv: null,
        json: null
    }

    /**
     * @param {string} datalogPath - Base directory of datalog files
     * @param {MetadataPaths} metadataPaths - File paths for metadata
     */
    constructor(datalogPath, metadataPaths) {
        this.#datalogPath = datalogPath;
        this.#metadata = this.#loadMetadata(metadataPaths);
    }

    /**
     * Loads metadata from the given paths or returns cached data if already loaded.
     * @param {MetadataPaths} metadataPaths
     * @returns {{csv: string, json: object, loaded: boolean}}
     */
    #loadMetadata(metadataPaths) {
        // If the metadata files have already been loaded,
        // return the cached CSV and JSON data instead of re-reading from paths.
        if (metadataPaths.loaded) {
            return {
                csv: metadataPaths.csv,
                json: metadataPaths.json,
            }
        }

        try {
            return {
                csv: fs.readFileSync(metadataPaths.csv, "utf-8"),
                json: JSON.parse(fs.readFileSync(metadataPaths.json, "utf-8"))
            };
        }
        catch (err) {
            throw new Error(`Failed to load metadata files: ${err.message}`);
        }
    }

    /**
     * Returns metadata in the requested format.
     * @param {"csv"|"json"} format
     * @returns {string|object}
     */
    getMetadata = (format) => (this.#metadata[format] ?? this.#metadata["csv"])

    /**
     * Extracts day number from file name.
     * @param {string} file
     * @returns {number}
     */
    #dayUnixFromFile = (file) => Date.UTC(1970, 0, parseInt(file.replace(/\D/g, ''), 10)) / 1000;

    /**
     * Gets paths to data files in a given range and resolution.
     * @param {string} resolution
     * @param {number} [t0] - Start time in Unix seconds (optional)
     * @param {number} [t1] - End time in Unix seconds (optional)
     * @returns {string[]} List of file paths
     */
    getDataFiles(resolution, t0 = undefined, t1 = undefined) {
        const dir = path.join(this.#datalogPath, resolution);
        const files = fs.readdirSync(dir);
        if (!files.length) throw new Error(`No data files in: ${dir}`);
        // 172800 <- Reperesents 2 days offset
        const offset = 172800;
        const min = t0 !== undefined ? t0 - offset : 0;
        const max = t1 !== undefined ? t1 + offset : Infinity;
        return files
            .map(f => ({
                p: path.join(dir, f),
                t: this.#dayUnixFromFile(f)
            }))
            .filter(({ t }) => t >= min && t <= max)
            .sort((a, b) => a.t - b.t)
            .map(({ p }) => p);
    }

    /**
     * Gets the available time range from files.
     * @param {string} resolution
     * @returns {{t0: number, t1: number}} Time range in Unix seconds
     */
    getAvailableDataRange(resolution) {
        const files = this.getDataFiles(resolution);
        if (!files.length) throw new Error(`No data files found in resolution: ${resolution}`);
        const timestamps = files.map(f => this.#dayUnixFromFile(f));
        return { t0: Math.min(...timestamps), t1: Math.max(...timestamps) };
    }

    /**
     * Chooses the optimal interval in minutes based on time range and resolution.
     * @param {number} t0 - Start time (Unix seconds)
     * @param {number} t1 - End time (Unix seconds)
     * @param {string} resolution
     * @returns {number} Interval in minutes
     */
    #getIdealInterval(t0, t1, resolution) {
        if (typeof t0 !== 'number' || typeof t1 !== 'number' || t1 <= t0) throw new Error("Invalid time range: t0 and t1 must be numbers and t1 > t0");
        const idealTotalPoints = resolutions[resolution] * 60
        const intervals = [
            1,       // 1 minute
            5,       // 5 minutes
            15,      // 15 minutes
            30,      // 30 minutes
            60,      // 1 hour
            120,     // 2 hours
            180,     // 3 hours
            360,     // 6 hours
            1440,    // 1 day (24 hours * 60 minutes)
        ];
        const duration = (t1 - t0) / 60;
        const required = duration / idealTotalPoints;
        return intervals.find(i => i >= required) ?? intervals.at(-1);
    };

    /**
     * Creates a line by line stream returning raw csv values
     * @param {number} t0 - Start time (Unix seconds)
     * @param {number} t1 - End time (Unix seconds)
     * @param {string} resolution
     * @returns {DatalogLineReader}
     */
    createLineStreamFiles(t0, t1, resolution) {
        const files = this.getDataFiles(resolution, t0, t1);
        const reader = new DatalogLineReader(files);
        return reader;
    }

    /**
     * Creates a readable stream of raw data lines from files.
     * @param {number} t0 - Start time (Unix seconds)
     * @param {number} t1 - End time (Unix seconds)
     * @param {string} resolution
     * @returns {Readable}
     */
    createStreamFiles(t0, t1, resolution) {
        const files = this.getDataFiles(resolution, t0, t1);
        let currentFileIndex = 0;
        let rl = null;
        return new Readable({
            read() {
                if (rl) return;
                if (currentFileIndex >= files.length) {
                    return this.push(null);
                }
                const fileStream = fs.createReadStream(files[currentFileIndex++], { encoding: 'utf-8' });
                rl = readline.createInterface({ input: fileStream });
                rl.on('line', (line) => {
                    if (!line.trim()) return;
                    if (!this.push(line + '\n')) {
                        rl.pause();
                    }
                });
                rl.on('close', () => {
                    rl = null;
                    this._read();
                });
                fileStream.on('error', (err) => this.destroy(err));
            }
        });
    }

    /**
     * Checks whether a timestamp is divisible by a given interval.
     * @param {string|number} timestamp
     * @param {number} modulo
     * @returns {boolean}
     */
    #checkModulo = (timestamp, modulo) => Number(timestamp) % modulo === 0;

    /**
     * Checks whether a timestamp is within the range.
     * @param {number} t0
     * @param {number} t1
     * @param {string|number} timestamp
     * @returns {boolean}
     */
    #isInRange = (t0, t1, timestamp) => timestamp >= t0 && timestamp <= t1;

    /**
     * Parses a CSV line based on format.
     * @param {string} line
     * @param {"array"|"csv"} format
     * @returns {string|Array<number|null>}
     */
    formatCsvLine(line, format) {
        line = line.trim();
        switch (format) {
            case "array":
                return line.split(',').map(value => {
                    const trimmed = value.trim();
                    if (trimmed === "" || trimmed.toLowerCase() === "null") return null;
                    const num = Number(trimmed);
                    return Number.isNaN(num) ? null : num;
                });
            case "csv":
            default:
                return line;
        }
    }

    /**
     * Creates a transform stream with formatted and filtered CSV data.
     * @param {number} t0 - Start time (Unix seconds)
     * @param {number} t1 - End time (Unix seconds)
     * @param {"csv"|"array"} format
     * @param {string} resolution
     * @param {boolean} [modulo] - Optional modulo for filtering timestamps
     * @returns {Transform}
     */
    createStreamFilesFormat(t0, t1, format, resolution, modulo) {
        const self = this;
        const mod = this.#getIdealInterval(t0, t1, resolution);
        return this.createStreamFiles(t0, t1, resolution).pipe(new Transform({
            objectMode: true,
            transform(chunk, _, cb) {
                const timestamp = parseInt(chunk.toString())
                if (modulo && !self.#checkModulo(timestamp, mod)) return cb();
                if (!self.#isInRange(t0, t1, timestamp)) return cb();
                cb(null, self.formatCsvLine(chunk.toString(), format));
            }
        }));
    }
}

module.exports = { DatalogHandler }