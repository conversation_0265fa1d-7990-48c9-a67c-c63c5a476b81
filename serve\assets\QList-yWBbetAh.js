import{c as n,g as d,a as t,h as i,b as u}from"./index-CzmOWWdj.js";import{a as c,c as g}from"./use-key-composition-CoMUTTxZ.js";const m=["ul","ol"],v=n({name:"QList",props:{...c,bordered:<PERSON><PERSON>an,dense:<PERSON><PERSON><PERSON>,separator:<PERSON><PERSON><PERSON>,padding:<PERSON><PERSON><PERSON>,tag:{type:String,default:"div"}},setup(e,{slots:a}){const s=d(),r=g(e,s.proxy.$q),o=t(()=>m.includes(e.tag)?null:"list"),l=t(()=>"q-list"+(e.bordered===!0?" q-list--bordered":"")+(e.dense===!0?" q-list--dense":"")+(e.separator===!0?" q-list--separator":"")+(r.value===!0?" q-list--dark":"")+(e.padding===!0?" q-list--padding":""));return()=>i(e.tag,{class:l.value,role:o.value},u(a.default))}});export{v as Q};
