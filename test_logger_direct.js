const logger = require('./src/Logger');

console.log('Testing direct logger calls...');

// Test direct error logging
logger.error('Direct error test 1');
logger.error('Direct error test 2', { extra: 'metadata' });

// Test error with Error object
const testError = new Error('Test error object');
logger.error('Error with object:', testError);

console.log('Direct tests completed. Check logs/application_errors.log');
