
//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//*
//* routes/data.js
//*
//* Server Sent Events
//*============================================================================================
/**
 * @module routes/setup
 */

const { API_ROOT } = require('../System.js')
const { DeviceRegisterStreamProcessor } = require('../DeviceRegisterStream.js')

const fs = require('fs')
const { Stream } = require('stream')
const path = require('path')

const METADATA = JSON.parse(fs.readFileSync(path.join(process.cwd(), "project", "Roxborough", "datalog", "metadata.json")))
const FINE_PATH = path.join(process.cwd(), "project", "Roxborough", "datalog", "fine")

//----------------------------------------------------------------------------------------------------
/**
 * Routes for the trend page.
 * - Call using `fastify.register()`
 * @param {object} fastify - The Fastify instance
 * @param {object} options
 * @param {Function} done - The callback function to call when the plugin is registered
 */
//----------------------------------------------------------------------------------------------------
function trendRoutes(fastify, options, done) {
    // Main trend route
    fastify.get(API_ROOT + '/trends', async (request, reply) => {
        // Create a buffer to hold the response chunks
        const buffer = new Stream.Readable();
            buffer._read = () => { };
        reply.type('text/html').send(buffer)

        /** t0 (start time), t1 (end time), type (trendType), resolution */
        const query = { ...request.query }
        console.log(query)

        const streamProcessor = new DeviceRegisterStreamProcessor(METADATA)
        streamProcessor.streamChainFiles(FINE_PATH, query, buffer)

        // Handle the events of the request
        request.raw.on("close", () => {
            buffer.destroy();
        })
    })
    done()
}


module.exports = trendRoutes