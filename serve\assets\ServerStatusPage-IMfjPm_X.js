import{i as V,r as a,f as E,D as _,F as s,G as f,X as o,H as l,O as P,E as y,ag as D,S as U,T as v,U as x,Y as B,J as T}from"./index-CzmOWWdj.js";import{Q as N}from"./QTable-CsCiCbH_.js";import{Q}from"./QPage-Disdm55v.js";import{_ as $}from"./PageHeading-CorBRYVa.js";import{S as q}from"./SseSpinner-HLkgS90o.js";import{_ as g}from"./AqPersistToggleButton-BcqzKwP0.js";import{u as R}from"./PersistObject-BVaCPhKL.js";import{d as z}from"./dayjs-mToOJNbe.js";import"./QSeparator-D-fNGoQY.js";import"./use-key-composition-CoMUTTxZ.js";import"./use-timeout-DeCFbuIx.js";import"./QList-yWBbetAh.js";import"./QSelect-BNWcW_ch.js";import"./QDialog-Cuvxr_1w.js";import"./focusout-C-pmmZED.js";import"./QMenu-D3shCIOy.js";import"./QCheckbox-BLQMX8bg.js";import"./use-checkbox-DDSE4paG.js";import"./QSpace-i2TdLNVM.js";import"./Login-BVtZ5S7B.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./_commonjsHelpers-Bx2EM-6T.js";const L={class:"a-container-lg"},M={class:"q-gutter-sm"},O={class:"q-mt-lg"},j={class:"a-container-lg"},A={key:0},F={class:"row q-col-gutter-sm",style:{"font-size":"80%"}},I={class:"server-status-pre rounded-borders"},ve={__name:"ServerStatusPage",setup(J){const b=V("$api"),k=[{name:"i",label:"#",align:"center",field:"i",sortable:!0},{name:"name",label:"Server Name",align:"left",field:"name",sortable:!0},{name:"host",label:"IP Address",align:"left",field:"host",sortable:!0},{name:"count",align:"center",label:"Transfer Count",field:"count",sortable:!0},{name:"errors",align:"center",label:"Error Count",field:"errors",sortable:!0},{name:"error",align:"center",label:"Error State",field:"error",format:()=>"",classes:t=>t.error?"table-cell-is-error":"table-cell-is-ok"},{name:"lastError",align:"center",label:"Last Error",field:"lastError",sortable:!0,format:t=>t?z().to(t):"-"},{name:"t",align:"center",label:"ΔT",field:"t",sortable:!1,format:t=>t+" ms"}],n=R("serverStatusTablePagination",{sortBy:"name",descending:!1,page:1,rowsPerPage:20}),i=a(!0),u=a(!1),c=a(!0),h=a(null),m=a(null),d=a(null),C=t=>{h.value=t,c.value&&S()},S=()=>b.get("/status/servers").then(t=>{m.value=t.data;const r=[];t.data.forEach((e,p)=>{r.push({i:p,name:e.name,host:e.host+":"+e.port,count:e.xferCount,errors:e.error.count,error:e.error.state,lastError:e.error.timestamp,t:e.requestLatency})}),d.value=r});function w(){return b.delete("/status/servers").then(t=>{console.log("ServerStatusPage resetStats",t)}).catch(t=>{console.log("ServerStatusPage resetStats error",t)})}return E(()=>{S()}),(t,r)=>(s(),_(Q,null,{default:f(()=>[o("div",L,[l($,{title:"IO Servers",icon:"svguse:icons.svg#server"},{default:f(()=>[o("div",M,[l(P,{size:"sm",onClick:r[0]||(r[0]=e=>w()),icon:"undo",label:"Reset Stats",color:"primary"}),l(g,{size:"sm",modelValue:u.value,"onUpdate:modelValue":r[1]||(r[1]=e=>u.value=e),"local-store-key":"serverStatusShowDetails","default-value":!1,"color-true":"primary","color-false":"grey-8",label:"Show Details","tooltip-true":"Click to disable auto updates","tooltip-false":"Click to enable auto updates"},null,8,["modelValue"]),l(g,{round:"",flat:"",modelValue:c.value,"onUpdate:modelValue":r[2]||(r[2]=e=>c.value=e),"local-store-key":"serverStatusAutoUpdate","default-value":!0,"color-true":"primary","color-false":"grey-5","icon-true":"play_circle","icon-false":"pause_circle","tooltip-true":"Click to disable auto updates","tooltip-false":"Click to enable auto updates"},null,8,["modelValue"]),l(q,{onMessage:C})])]),_:1}),o("div",O,[d.value?(s(),_(N,{key:0,class:"table-row-highlight",dense:i.value,flat:"",bordered:"",title:"Server Status",rows:d.value,columns:k,"row-key":"name",pagination:U(n),"onUpdate:pagination":r[4]||(r[4]=e=>D(n)?n.value=e:null)},{"top-right":f(()=>[l(g,{round:"",flat:"",modelValue:i.value,"onUpdate:modelValue":r[3]||(r[3]=e=>i.value=e),"local-store-key":"serverStatusTableDense","default-value":!0,"color-true":"grey-5","color-false":"grey-5","icon-true":"compress","icon-false":"expand","tooltip-true":"Click to expand the table spacing","tooltip-false":"Click to make the table compact"},null,8,["modelValue"])]),_:1},8,["dense","rows","pagination"])):y("",!0)])]),o("div",j,[u.value&&m.value?(s(),v("div",A,[o("div",F,[(s(!0),v(x,null,B(m.value,(e,p)=>(s(),v("div",{class:"col-3",key:p},[o("pre",I,T(JSON.stringify(e,null,2)),1)]))),128))])])):y("",!0)])]),_:1}))}};export{ve as default};
