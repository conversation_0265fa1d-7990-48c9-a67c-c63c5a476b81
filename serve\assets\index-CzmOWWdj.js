const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/MainLayout-C0Qc3H1M.js","assets/QImg-DdyGoaK-.js","assets/use-timeout-DeCFbuIx.js","assets/QCard-Dm-gpeRW.js","assets/use-key-composition-CoMUTTxZ.js","assets/QSeparator-D-fNGoQY.js","assets/QResizeObserver-Bk4whrbK.js","assets/focusout-C-pmmZED.js","assets/QSpace-i2TdLNVM.js","assets/QList-yWBbetAh.js","assets/QDialog-Cuvxr_1w.js","assets/TouchPan-C63yVDWw.js","assets/_plugin-vue_export-helper-DlAUqK2U.js","assets/use-quasar-Li8tSQ3f.js","assets/QInput-CCOJFSf5.js","assets/QCheckbox-BLQMX8bg.js","assets/use-checkbox-DDSE4paG.js","assets/ClosePopup-DhH_6eqd.js","assets/Login-BVtZ5S7B.js","assets/Debug-gcimLaHD.js","assets/info-store-rjwJi5x9.js","assets/MainLayout-CWHNZuRU.css","assets/TrendsPage-BJjzRfl9.js","assets/QPage-Disdm55v.js","assets/QSelect-BNWcW_ch.js","assets/QMenu-D3shCIOy.js","assets/QPopupProxy-D3bKa31G.js","assets/dayjs-mToOJNbe.js","assets/_commonjsHelpers-Bx2EM-6T.js","assets/dygraph-CO2DREVQ.js","assets/QToggle-D9UX9nQt.js","assets/TrendsPage-BClVZdsB.css","assets/CurvesPage-DH6BRJjh.js","assets/PageHeading-CorBRYVa.js","assets/SaveButton-DQ7yxOUN.js","assets/SaveButton-Dt79qmeT.css","assets/AqArrayForm-C93bdg5l.js","assets/AqArrayForm-BDAGrbCZ.css","assets/AqPersistToggleButton-BcqzKwP0.js","assets/CurvesPage-Dz2doP5y.css","assets/SchedulesPage-Ditq5I8c.js","assets/SchedulesPage-UjA_k4EW.css","assets/SettingsPage-CbZE7FuX.js","assets/SettingsPage-CY6Asul_.css","assets/DashboardPage-DGEqqYZR.js","assets/SseSpinner-HLkgS90o.js","assets/SseSpinner-BUyMtNnG.css","assets/DashboardPage-CNM_NV4C.css","assets/DataPage-CWaPc1cJ.js","assets/DataPage-XYjQBGNU.css","assets/AlarmsPage-B9f8q1vm.js","assets/QTable-CsCiCbH_.js","assets/export-file-CL4zo4TZ.js","assets/PersistObject-BVaCPhKL.js","assets/AlarmsPage-B95xqYd9.css","assets/RegistersPage-B_NHAgbr.js","assets/RegistersPage-DUq304CA.css","assets/DebugPage-Cn9cP68R.js","assets/DebugPage-CRAtQgOk.css","assets/PlaceholderPage-vDkLOcz1.js","assets/DocumentsPage-CztO03un.js","assets/DocumentsPage-CVWGk8k9.css","assets/ServerStatusPage-IMfjPm_X.js","assets/ServerStatusPage-CsJlzyqK.css"])))=>i.map(i=>d[i]);
const Ka=function(){const t=typeof document<"u"&&document.createElement("link").relList;return t&&t.supports&&t.supports("modulepreload")?"modulepreload":"preload"}(),Wa=function(e){return"/"+e},ho={},Pe=function(t,n,r){let s=Promise.resolve();if(n&&n.length>0){let i=function(u){return Promise.all(u.map(c=>Promise.resolve(c).then(f=>({status:"fulfilled",value:f}),f=>({status:"rejected",reason:f}))))};document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),a=l?.nonce||l?.getAttribute("nonce");s=i(n.map(u=>{if(u=Wa(u),u in ho)return;ho[u]=!0;const c=u.endsWith(".css"),f=c?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${u}"]${f}`))return;const d=document.createElement("link");if(d.rel=c?"stylesheet":Ka,c||(d.as="script"),d.crossOrigin="",d.href=u,a&&d.setAttribute("nonce",a),document.head.appendChild(d),c)return new Promise((g,v)=>{d.addEventListener("load",g),d.addEventListener("error",()=>v(new Error(`Unable to preload CSS for ${u}`)))})}))}function o(i){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i}return s.then(i=>{for(const l of i||[])l.status==="rejected"&&o(l.reason);return t().catch(o)})};/**
* @vue/shared v3.5.14
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Is(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const se={},nn=[],rt=()=>{},Ga=()=>!1,xr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Ns=e=>e.startsWith("onUpdate:"),ge=Object.assign,js=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Qa=Object.prototype.hasOwnProperty,le=(e,t)=>Qa.call(e,t),K=Array.isArray,rn=e=>Sr(e)==="[object Map]",ji=e=>Sr(e)==="[object Set]",J=e=>typeof e=="function",he=e=>typeof e=="string",dt=e=>typeof e=="symbol",de=e=>e!==null&&typeof e=="object",Di=e=>(de(e)||J(e))&&J(e.then)&&J(e.catch),qi=Object.prototype.toString,Sr=e=>qi.call(e),Ya=e=>Sr(e).slice(8,-1),Fi=e=>Sr(e)==="[object Object]",Ds=e=>he(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,xn=Is(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Er=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Ja=/-(\w)/g,De=Er(e=>e.replace(Ja,(t,n)=>n?n.toUpperCase():"")),Xa=/\B([A-Z])/g,Rt=Er(e=>e.replace(Xa,"-$1").toLowerCase()),Cr=Er(e=>e.charAt(0).toUpperCase()+e.slice(1)),Fr=Er(e=>e?`on${Cr(e)}`:""),Me=(e,t)=>!Object.is(e,t),Hr=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Hi=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},Za=e=>{const t=parseFloat(e);return isNaN(t)?e:t},ec=e=>{const t=he(e)?Number(e):NaN;return isNaN(t)?e:t};let po;const Pr=()=>po||(po=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function qs(e){if(K(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],s=he(r)?sc(r):qs(r);if(s)for(const o in s)t[o]=s[o]}return t}else if(he(e)||de(e))return e}const tc=/;(?![^(]*\))/g,nc=/:([^]+)/,rc=/\/\*[^]*?\*\//g;function sc(e){const t={};return e.replace(rc,"").split(tc).forEach(n=>{if(n){const r=n.split(nc);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function Fs(e){let t="";if(he(e))t=e;else if(K(e))for(let n=0;n<e.length;n++){const r=Fs(e[n]);r&&(t+=r+" ")}else if(de(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const oc="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",ic=Is(oc);function Bi(e){return!!e||e===""}const Vi=e=>!!(e&&e.__v_isRef===!0),lc=e=>he(e)?e:e==null?"":K(e)||de(e)&&(e.toString===qi||!J(e.toString))?Vi(e)?lc(e.value):JSON.stringify(e,zi,2):String(e),zi=(e,t)=>Vi(t)?zi(e,t.value):rn(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,s],o)=>(n[Br(r,o)+" =>"]=s,n),{})}:ji(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Br(n))}:dt(t)?Br(t):de(t)&&!K(t)&&!Fi(t)?String(t):t,Br=(e,t="")=>{var n;return dt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.14
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let we;class Ui{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=we,!t&&we&&(this.index=(we.scopes||(we.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=we;try{return we=this,t()}finally{we=n}}}on(){++this._on===1&&(this.prevScope=we,we=this)}off(){this._on>0&&--this._on===0&&(we=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0}}}function Ki(e){return new Ui(e)}function Wi(){return we}function ac(e,t=!1){we&&we.cleanups.push(e)}let fe;const Vr=new WeakSet;class Gi{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,we&&we.active&&we.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Vr.has(this)&&(Vr.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Yi(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,go(this),Ji(this);const t=fe,n=Ke;fe=this,Ke=!0;try{return this.fn()}finally{Xi(this),fe=t,Ke=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Vs(t);this.deps=this.depsTail=void 0,go(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Vr.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){hs(this)&&this.run()}get dirty(){return hs(this)}}let Qi=0,Sn,En;function Yi(e,t=!1){if(e.flags|=8,t){e.next=En,En=e;return}e.next=Sn,Sn=e}function Hs(){Qi++}function Bs(){if(--Qi>0)return;if(En){let t=En;for(En=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Sn;){let t=Sn;for(Sn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function Ji(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Xi(e){let t,n=e.depsTail,r=n;for(;r;){const s=r.prevDep;r.version===-1?(r===n&&(n=s),Vs(r),cc(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=s}e.deps=t,e.depsTail=n}function hs(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Zi(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Zi(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Ln)||(e.globalVersion=Ln,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!hs(e))))return;e.flags|=2;const t=e.dep,n=fe,r=Ke;fe=e,Ke=!0;try{Ji(e);const s=e.fn(e._value);(t.version===0||Me(s,e._value))&&(e.flags|=128,e._value=s,t.version++)}catch(s){throw t.version++,s}finally{fe=n,Ke=r,Xi(e),e.flags&=-3}}function Vs(e,t=!1){const{dep:n,prevSub:r,nextSub:s}=e;if(r&&(r.nextSub=s,e.prevSub=void 0),s&&(s.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)Vs(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function cc(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Ke=!0;const el=[];function ut(){el.push(Ke),Ke=!1}function ft(){const e=el.pop();Ke=e===void 0?!0:e}function go(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=fe;fe=void 0;try{t()}finally{fe=n}}}let Ln=0;class uc{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Rr{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!fe||!Ke||fe===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==fe)n=this.activeLink=new uc(fe,this),fe.deps?(n.prevDep=fe.depsTail,fe.depsTail.nextDep=n,fe.depsTail=n):fe.deps=fe.depsTail=n,tl(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=fe.depsTail,n.nextDep=void 0,fe.depsTail.nextDep=n,fe.depsTail=n,fe.deps===n&&(fe.deps=r)}return n}trigger(t){this.version++,Ln++,this.notify(t)}notify(t){Hs();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Bs()}}}function tl(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)tl(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const ir=new WeakMap,jt=Symbol(""),ps=Symbol(""),Mn=Symbol("");function xe(e,t,n){if(Ke&&fe){let r=ir.get(e);r||ir.set(e,r=new Map);let s=r.get(n);s||(r.set(n,s=new Rr),s.map=r,s.key=n),s.track()}}function at(e,t,n,r,s,o){const i=ir.get(e);if(!i){Ln++;return}const l=a=>{a&&a.trigger()};if(Hs(),t==="clear")i.forEach(l);else{const a=K(e),u=a&&Ds(n);if(a&&n==="length"){const c=Number(r);i.forEach((f,d)=>{(d==="length"||d===Mn||!dt(d)&&d>=c)&&l(f)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),u&&l(i.get(Mn)),t){case"add":a?u&&l(i.get("length")):(l(i.get(jt)),rn(e)&&l(i.get(ps)));break;case"delete":a||(l(i.get(jt)),rn(e)&&l(i.get(ps)));break;case"set":rn(e)&&l(i.get(jt));break}}Bs()}function fc(e,t){const n=ir.get(e);return n&&n.get(t)}function Gt(e){const t=ne(e);return t===e?t:(xe(t,"iterate",Mn),Be(e)?t:t.map(ye))}function Tr(e){return xe(e=ne(e),"iterate",Mn),e}const dc={__proto__:null,[Symbol.iterator](){return zr(this,Symbol.iterator,ye)},concat(...e){return Gt(this).concat(...e.map(t=>K(t)?Gt(t):t))},entries(){return zr(this,"entries",e=>(e[1]=ye(e[1]),e))},every(e,t){return ot(this,"every",e,t,void 0,arguments)},filter(e,t){return ot(this,"filter",e,t,n=>n.map(ye),arguments)},find(e,t){return ot(this,"find",e,t,ye,arguments)},findIndex(e,t){return ot(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return ot(this,"findLast",e,t,ye,arguments)},findLastIndex(e,t){return ot(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return ot(this,"forEach",e,t,void 0,arguments)},includes(...e){return Ur(this,"includes",e)},indexOf(...e){return Ur(this,"indexOf",e)},join(e){return Gt(this).join(e)},lastIndexOf(...e){return Ur(this,"lastIndexOf",e)},map(e,t){return ot(this,"map",e,t,void 0,arguments)},pop(){return dn(this,"pop")},push(...e){return dn(this,"push",e)},reduce(e,...t){return mo(this,"reduce",e,t)},reduceRight(e,...t){return mo(this,"reduceRight",e,t)},shift(){return dn(this,"shift")},some(e,t){return ot(this,"some",e,t,void 0,arguments)},splice(...e){return dn(this,"splice",e)},toReversed(){return Gt(this).toReversed()},toSorted(e){return Gt(this).toSorted(e)},toSpliced(...e){return Gt(this).toSpliced(...e)},unshift(...e){return dn(this,"unshift",e)},values(){return zr(this,"values",ye)}};function zr(e,t,n){const r=Tr(e),s=r[t]();return r!==e&&!Be(e)&&(s._next=s.next,s.next=()=>{const o=s._next();return o.value&&(o.value=n(o.value)),o}),s}const hc=Array.prototype;function ot(e,t,n,r,s,o){const i=Tr(e),l=i!==e&&!Be(e),a=i[t];if(a!==hc[t]){const f=a.apply(e,o);return l?ye(f):f}let u=n;i!==e&&(l?u=function(f,d){return n.call(this,ye(f),d,e)}:n.length>2&&(u=function(f,d){return n.call(this,f,d,e)}));const c=a.call(i,u,r);return l&&s?s(c):c}function mo(e,t,n,r){const s=Tr(e);let o=n;return s!==e&&(Be(e)?n.length>3&&(o=function(i,l,a){return n.call(this,i,l,a,e)}):o=function(i,l,a){return n.call(this,i,ye(l),a,e)}),s[t](o,...r)}function Ur(e,t,n){const r=ne(e);xe(r,"iterate",Mn);const s=r[t](...n);return(s===-1||s===!1)&&Ks(n[0])?(n[0]=ne(n[0]),r[t](...n)):s}function dn(e,t,n=[]){ut(),Hs();const r=ne(e)[t].apply(e,n);return Bs(),ft(),r}const pc=Is("__proto__,__v_isRef,__isVue"),nl=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(dt));function gc(e){dt(e)||(e=String(e));const t=ne(this);return xe(t,"has",e),t.hasOwnProperty(e)}class rl{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const s=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return o;if(n==="__v_raw")return r===(s?o?Cc:ll:o?il:ol).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const i=K(t);if(!s){let a;if(i&&(a=dc[n]))return a;if(n==="hasOwnProperty")return gc}const l=Reflect.get(t,n,pe(t)?t:r);return(dt(n)?nl.has(n):pc(n))||(s||xe(t,"get",n),o)?l:pe(l)?i&&Ds(n)?l:l.value:de(l)?s?cl(l):zt(l):l}}class sl extends rl{constructor(t=!1){super(!1,t)}set(t,n,r,s){let o=t[n];if(!this._isShallow){const a=Ct(o);if(!Be(r)&&!Ct(r)&&(o=ne(o),r=ne(r)),!K(t)&&pe(o)&&!pe(r))return a?!1:(o.value=r,!0)}const i=K(t)&&Ds(n)?Number(n)<t.length:le(t,n),l=Reflect.set(t,n,r,pe(t)?t:s);return t===ne(s)&&(i?Me(r,o)&&at(t,"set",n,r):at(t,"add",n,r)),l}deleteProperty(t,n){const r=le(t,n);t[n];const s=Reflect.deleteProperty(t,n);return s&&r&&at(t,"delete",n,void 0),s}has(t,n){const r=Reflect.has(t,n);return(!dt(n)||!nl.has(n))&&xe(t,"has",n),r}ownKeys(t){return xe(t,"iterate",K(t)?"length":jt),Reflect.ownKeys(t)}}class mc extends rl{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const vc=new sl,_c=new mc,yc=new sl(!0);const gs=e=>e,Wn=e=>Reflect.getPrototypeOf(e);function bc(e,t,n){return function(...r){const s=this.__v_raw,o=ne(s),i=rn(o),l=e==="entries"||e===Symbol.iterator&&i,a=e==="keys"&&i,u=s[e](...r),c=n?gs:t?lr:ye;return!t&&xe(o,"iterate",a?ps:jt),{next(){const{value:f,done:d}=u.next();return d?{value:f,done:d}:{value:l?[c(f[0]),c(f[1])]:c(f),done:d}},[Symbol.iterator](){return this}}}}function Gn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function wc(e,t){const n={get(s){const o=this.__v_raw,i=ne(o),l=ne(s);e||(Me(s,l)&&xe(i,"get",s),xe(i,"get",l));const{has:a}=Wn(i),u=t?gs:e?lr:ye;if(a.call(i,s))return u(o.get(s));if(a.call(i,l))return u(o.get(l));o!==i&&o.get(s)},get size(){const s=this.__v_raw;return!e&&xe(ne(s),"iterate",jt),Reflect.get(s,"size",s)},has(s){const o=this.__v_raw,i=ne(o),l=ne(s);return e||(Me(s,l)&&xe(i,"has",s),xe(i,"has",l)),s===l?o.has(s):o.has(s)||o.has(l)},forEach(s,o){const i=this,l=i.__v_raw,a=ne(l),u=t?gs:e?lr:ye;return!e&&xe(a,"iterate",jt),l.forEach((c,f)=>s.call(o,u(c),u(f),i))}};return ge(n,e?{add:Gn("add"),set:Gn("set"),delete:Gn("delete"),clear:Gn("clear")}:{add(s){!t&&!Be(s)&&!Ct(s)&&(s=ne(s));const o=ne(this);return Wn(o).has.call(o,s)||(o.add(s),at(o,"add",s,s)),this},set(s,o){!t&&!Be(o)&&!Ct(o)&&(o=ne(o));const i=ne(this),{has:l,get:a}=Wn(i);let u=l.call(i,s);u||(s=ne(s),u=l.call(i,s));const c=a.call(i,s);return i.set(s,o),u?Me(o,c)&&at(i,"set",s,o):at(i,"add",s,o),this},delete(s){const o=ne(this),{has:i,get:l}=Wn(o);let a=i.call(o,s);a||(s=ne(s),a=i.call(o,s)),l&&l.call(o,s);const u=o.delete(s);return a&&at(o,"delete",s,void 0),u},clear(){const s=ne(this),o=s.size!==0,i=s.clear();return o&&at(s,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(s=>{n[s]=bc(s,e,t)}),n}function zs(e,t){const n=wc(e,t);return(r,s,o)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?r:Reflect.get(le(n,s)&&s in r?n:r,s,o)}const xc={get:zs(!1,!1)},Sc={get:zs(!1,!0)},Ec={get:zs(!0,!1)};const ol=new WeakMap,il=new WeakMap,ll=new WeakMap,Cc=new WeakMap;function Pc(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Rc(e){return e.__v_skip||!Object.isExtensible(e)?0:Pc(Ya(e))}function zt(e){return Ct(e)?e:Us(e,!1,vc,xc,ol)}function al(e){return Us(e,!1,yc,Sc,il)}function cl(e){return Us(e,!0,_c,Ec,ll)}function Us(e,t,n,r,s){if(!de(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=Rc(e);if(o===0)return e;const i=s.get(e);if(i)return i;const l=new Proxy(e,o===2?r:n);return s.set(e,l),l}function Et(e){return Ct(e)?Et(e.__v_raw):!!(e&&e.__v_isReactive)}function Ct(e){return!!(e&&e.__v_isReadonly)}function Be(e){return!!(e&&e.__v_isShallow)}function Ks(e){return e?!!e.__v_raw:!1}function ne(e){const t=e&&e.__v_raw;return t?ne(t):e}function Ut(e){return!le(e,"__v_skip")&&Object.isExtensible(e)&&Hi(e,"__v_skip",!0),e}const ye=e=>de(e)?zt(e):e,lr=e=>de(e)?cl(e):e;function pe(e){return e?e.__v_isRef===!0:!1}function Ft(e){return ul(e,!1)}function Tc(e){return ul(e,!0)}function ul(e,t){return pe(e)?e:new Ac(e,t)}class Ac{constructor(t,n){this.dep=new Rr,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:ne(t),this._value=n?t:ye(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||Be(t)||Ct(t);t=r?t:ne(t),Me(t,n)&&(this._rawValue=t,this._value=r?t:ye(t),this.dep.trigger())}}function Dt(e){return pe(e)?e.value:e}const kc={get:(e,t,n)=>t==="__v_raw"?e:Dt(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const s=e[t];return pe(s)&&!pe(n)?(s.value=n,!0):Reflect.set(e,t,n,r)}};function fl(e){return Et(e)?e:new Proxy(e,kc)}class Oc{constructor(t){this.__v_isRef=!0,this._value=void 0;const n=this.dep=new Rr,{get:r,set:s}=t(n.track.bind(n),n.trigger.bind(n));this._get=r,this._set=s}get value(){return this._value=this._get()}set value(t){this._set(t)}}function Lc(e){return new Oc(e)}function Mc(e){const t=K(e)?new Array(e.length):{};for(const n in e)t[n]=Ic(e,n);return t}class $c{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return fc(ne(this._object),this._key)}}function Ic(e,t,n){const r=e[t];return pe(r)?r:new $c(e,t,n)}class Nc{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Rr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Ln-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&fe!==this)return Yi(this,!0),!0}get value(){const t=this.dep.track();return Zi(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function jc(e,t,n=!1){let r,s;return J(e)?r=e:(r=e.get,s=e.set),new Nc(r,s,n)}const Qn={},ar=new WeakMap;let Mt;function Dc(e,t=!1,n=Mt){if(n){let r=ar.get(n);r||ar.set(n,r=[]),r.push(e)}}function qc(e,t,n=se){const{immediate:r,deep:s,once:o,scheduler:i,augmentJob:l,call:a}=n,u=A=>s?A:Be(A)||s===!1||s===0?ct(A,1):ct(A);let c,f,d,g,v=!1,y=!1;if(pe(e)?(f=()=>e.value,v=Be(e)):Et(e)?(f=()=>u(e),v=!0):K(e)?(y=!0,v=e.some(A=>Et(A)||Be(A)),f=()=>e.map(A=>{if(pe(A))return A.value;if(Et(A))return u(A);if(J(A))return a?a(A,2):A()})):J(e)?t?f=a?()=>a(e,2):e:f=()=>{if(d){ut();try{d()}finally{ft()}}const A=Mt;Mt=c;try{return a?a(e,3,[g]):e(g)}finally{Mt=A}}:f=rt,t&&s){const A=f,O=s===!0?1/0:s;f=()=>ct(A(),O)}const P=Wi(),T=()=>{c.stop(),P&&P.active&&js(P.effects,c)};if(o&&t){const A=t;t=(...O)=>{A(...O),T()}}let I=y?new Array(e.length).fill(Qn):Qn;const j=A=>{if(!(!(c.flags&1)||!c.dirty&&!A))if(t){const O=c.run();if(s||v||(y?O.some((W,U)=>Me(W,I[U])):Me(O,I))){d&&d();const W=Mt;Mt=c;try{const U=[O,I===Qn?void 0:y&&I[0]===Qn?[]:I,g];a?a(t,3,U):t(...U),I=O}finally{Mt=W}}}else c.run()};return l&&l(j),c=new Gi(f),c.scheduler=i?()=>i(j,!1):j,g=A=>Dc(A,!1,c),d=c.onStop=()=>{const A=ar.get(c);if(A){if(a)a(A,4);else for(const O of A)O();ar.delete(c)}},t?r?j(!0):I=c.run():i?i(j.bind(null,!0),!0):c.run(),T.pause=c.pause.bind(c),T.resume=c.resume.bind(c),T.stop=T,T}function ct(e,t=1/0,n){if(t<=0||!de(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,pe(e))ct(e.value,t,n);else if(K(e))for(let r=0;r<e.length;r++)ct(e[r],t,n);else if(ji(e)||rn(e))e.forEach(r=>{ct(r,t,n)});else if(Fi(e)){for(const r in e)ct(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&ct(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.14
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Bn(e,t,n,r){try{return r?e(...r):e()}catch(s){Ar(s,t,n)}}function We(e,t,n,r){if(J(e)){const s=Bn(e,t,n,r);return s&&Di(s)&&s.catch(o=>{Ar(o,t,n)}),s}if(K(e)){const s=[];for(let o=0;o<e.length;o++)s.push(We(e[o],t,n,r));return s}}function Ar(e,t,n,r=!0){const s=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||se;if(t){let l=t.parent;const a=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const c=l.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,a,u)===!1)return}l=l.parent}if(o){ut(),Bn(o,null,10,[e,a,u]),ft();return}}Fc(e,n,s,r,i)}function Fc(e,t,n,r=!0,s=!1){if(s)throw e;console.error(e)}const Te=[];let tt=-1;const sn=[];let yt=null,en=0;const dl=Promise.resolve();let cr=null;function Ws(e){const t=cr||dl;return e?t.then(this?e.bind(this):e):t}function Hc(e){let t=tt+1,n=Te.length;for(;t<n;){const r=t+n>>>1,s=Te[r],o=$n(s);o<e||o===e&&s.flags&2?t=r+1:n=r}return t}function Gs(e){if(!(e.flags&1)){const t=$n(e),n=Te[Te.length-1];!n||!(e.flags&2)&&t>=$n(n)?Te.push(e):Te.splice(Hc(t),0,e),e.flags|=1,hl()}}function hl(){cr||(cr=dl.then(gl))}function Bc(e){K(e)?sn.push(...e):yt&&e.id===-1?yt.splice(en+1,0,e):e.flags&1||(sn.push(e),e.flags|=1),hl()}function vo(e,t,n=tt+1){for(;n<Te.length;n++){const r=Te[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;Te.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function pl(e){if(sn.length){const t=[...new Set(sn)].sort((n,r)=>$n(n)-$n(r));if(sn.length=0,yt){yt.push(...t);return}for(yt=t,en=0;en<yt.length;en++){const n=yt[en];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}yt=null,en=0}}const $n=e=>e.id==null?e.flags&2?-1:1/0:e.id;function gl(e){try{for(tt=0;tt<Te.length;tt++){const t=Te[tt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Bn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;tt<Te.length;tt++){const t=Te[tt];t&&(t.flags&=-2)}tt=-1,Te.length=0,pl(),cr=null,(Te.length||sn.length)&&gl()}}let me=null,ml=null;function ur(e){const t=me;return me=e,ml=e&&e.type.__scopeId||null,t}function Vc(e,t=me,n){if(!t||e._n)return e;const r=(...s)=>{r._d&&ko(-1);const o=ur(t);let i;try{i=e(...s)}finally{ur(o),r._d&&ko(1)}return i};return r._n=!0,r._c=!0,r._d=!0,r}function vl(e,t){if(me===null)return e;const n=Ir(me),r=e.dirs||(e.dirs=[]);for(let s=0;s<t.length;s++){let[o,i,l,a=se]=t[s];o&&(J(o)&&(o={mounted:o,updated:o}),o.deep&&ct(i),r.push({dir:o,instance:n,value:i,oldValue:void 0,arg:l,modifiers:a}))}return e}function Tt(e,t,n,r){const s=e.dirs,o=t&&t.dirs;for(let i=0;i<s.length;i++){const l=s[i];o&&(l.oldValue=o[i].value);let a=l.dir[r];a&&(ut(),We(a,n,8,[e.el,l,e,t]),ft())}}const _l=Symbol("_vte"),yl=e=>e.__isTeleport,Cn=e=>e&&(e.disabled||e.disabled===""),_o=e=>e&&(e.defer||e.defer===""),yo=e=>typeof SVGElement<"u"&&e instanceof SVGElement,bo=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,ms=(e,t)=>{const n=e&&e.to;return he(n)?t?t(n):null:n},bl={name:"Teleport",__isTeleport:!0,process(e,t,n,r,s,o,i,l,a,u){const{mc:c,pc:f,pbc:d,o:{insert:g,querySelector:v,createText:y,createComment:P}}=u,T=Cn(t.props);let{shapeFlag:I,children:j,dynamicChildren:A}=t;if(e==null){const O=t.el=y(""),W=t.anchor=y("");g(O,n,r),g(W,n,r);const U=(E,H)=>{I&16&&(s&&s.isCE&&(s.ce._teleportTarget=E),c(j,E,H,s,o,i,l,a))},q=()=>{const E=t.target=ms(t.props,v),H=wl(E,t,y,g);E&&(i!=="svg"&&yo(E)?i="svg":i!=="mathml"&&bo(E)&&(i="mathml"),T||(U(E,H),er(t,!1)))};T&&(U(n,W),er(t,!0)),_o(t.props)?Re(()=>{q(),t.el.__isMounted=!0},o):q()}else{if(_o(t.props)&&!e.el.__isMounted){Re(()=>{bl.process(e,t,n,r,s,o,i,l,a,u),delete e.el.__isMounted},o);return}t.el=e.el,t.targetStart=e.targetStart;const O=t.anchor=e.anchor,W=t.target=e.target,U=t.targetAnchor=e.targetAnchor,q=Cn(e.props),E=q?n:W,H=q?O:U;if(i==="svg"||yo(W)?i="svg":(i==="mathml"||bo(W))&&(i="mathml"),A?(d(e.dynamicChildren,A,E,s,o,i,l),to(e,t,!0)):a||f(e,t,E,H,s,o,i,l,!1),T)q?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Yn(t,n,O,u,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const G=t.target=ms(t.props,v);G&&Yn(t,G,null,u,0)}else q&&Yn(t,W,U,u,1);er(t,T)}},remove(e,t,n,{um:r,o:{remove:s}},o){const{shapeFlag:i,children:l,anchor:a,targetStart:u,targetAnchor:c,target:f,props:d}=e;if(f&&(s(u),s(c)),o&&s(a),i&16){const g=o||!Cn(d);for(let v=0;v<l.length;v++){const y=l[v];r(y,t,n,g,!!y.dynamicChildren)}}},move:Yn,hydrate:zc};function Yn(e,t,n,{o:{insert:r},m:s},o=2){o===0&&r(e.targetAnchor,t,n);const{el:i,anchor:l,shapeFlag:a,children:u,props:c}=e,f=o===2;if(f&&r(i,t,n),(!f||Cn(c))&&a&16)for(let d=0;d<u.length;d++)s(u[d],t,n,2);f&&r(l,t,n)}function zc(e,t,n,r,s,o,{o:{nextSibling:i,parentNode:l,querySelector:a,insert:u,createText:c}},f){const d=t.target=ms(t.props,a);if(d){const g=Cn(t.props),v=d._lpa||d.firstChild;if(t.shapeFlag&16)if(g)t.anchor=f(i(e),t,l(e),n,r,s,o),t.targetStart=v,t.targetAnchor=v&&i(v);else{t.anchor=i(e);let y=v;for(;y;){if(y&&y.nodeType===8){if(y.data==="teleport start anchor")t.targetStart=y;else if(y.data==="teleport anchor"){t.targetAnchor=y,d._lpa=t.targetAnchor&&i(t.targetAnchor);break}}y=i(y)}t.targetAnchor||wl(d,t,c,u),f(v&&i(v),t,d,n,r,s,o)}er(t,g)}return t.anchor&&i(t.anchor)}const hp=bl;function er(e,t){const n=e.ctx;if(n&&n.ut){let r,s;for(t?(r=e.el,s=e.anchor):(r=e.targetStart,s=e.targetAnchor);r&&r!==s;)r.nodeType===1&&r.setAttribute("data-v-owner",n.uid),r=r.nextSibling;n.ut()}}function wl(e,t,n,r){const s=t.targetStart=n(""),o=t.targetAnchor=n("");return s[_l]=o,e&&(r(s,e),r(o,e)),o}const bt=Symbol("_leaveCb"),Jn=Symbol("_enterCb");function xl(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Js(()=>{e.isMounted=!0}),Xs(()=>{e.isUnmounting=!0}),e}const Fe=[Function,Array],Sl={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Fe,onEnter:Fe,onAfterEnter:Fe,onEnterCancelled:Fe,onBeforeLeave:Fe,onLeave:Fe,onAfterLeave:Fe,onLeaveCancelled:Fe,onBeforeAppear:Fe,onAppear:Fe,onAfterAppear:Fe,onAppearCancelled:Fe},El=e=>{const t=e.subTree;return t.component?El(t.component):t},Uc={name:"BaseTransition",props:Sl,setup(e,{slots:t}){const n=un(),r=xl();return()=>{const s=t.default&&Qs(t.default(),!0);if(!s||!s.length)return;const o=Cl(s),i=ne(e),{mode:l}=i;if(r.isLeaving)return Kr(o);const a=wo(o);if(!a)return Kr(o);let u=In(a,i,r,n,f=>u=f);a.type!==Se&&Ht(a,u);let c=n.subTree&&wo(n.subTree);if(c&&c.type!==Se&&!It(a,c)&&El(n).type!==Se){let f=In(c,i,r,n);if(Ht(c,f),l==="out-in"&&a.type!==Se)return r.isLeaving=!0,f.afterLeave=()=>{r.isLeaving=!1,n.job.flags&8||n.update(),delete f.afterLeave,c=void 0},Kr(o);l==="in-out"&&a.type!==Se?f.delayLeave=(d,g,v)=>{const y=Pl(r,c);y[String(c.key)]=c,d[bt]=()=>{g(),d[bt]=void 0,delete u.delayedLeave,c=void 0},u.delayedLeave=()=>{v(),delete u.delayedLeave,c=void 0}}:c=void 0}else c&&(c=void 0);return o}}};function Cl(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==Se){t=n;break}}return t}const Kc=Uc;function Pl(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function In(e,t,n,r,s){const{appear:o,mode:i,persisted:l=!1,onBeforeEnter:a,onEnter:u,onAfterEnter:c,onEnterCancelled:f,onBeforeLeave:d,onLeave:g,onAfterLeave:v,onLeaveCancelled:y,onBeforeAppear:P,onAppear:T,onAfterAppear:I,onAppearCancelled:j}=t,A=String(e.key),O=Pl(n,e),W=(E,H)=>{E&&We(E,r,9,H)},U=(E,H)=>{const G=H[1];W(E,H),K(E)?E.every(k=>k.length<=1)&&G():E.length<=1&&G()},q={mode:i,persisted:l,beforeEnter(E){let H=a;if(!n.isMounted)if(o)H=P||a;else return;E[bt]&&E[bt](!0);const G=O[A];G&&It(e,G)&&G.el[bt]&&G.el[bt](),W(H,[E])},enter(E){let H=u,G=c,k=f;if(!n.isMounted)if(o)H=T||u,G=I||c,k=j||f;else return;let Z=!1;const N=E[Jn]=ee=>{Z||(Z=!0,ee?W(k,[E]):W(G,[E]),q.delayedLeave&&q.delayedLeave(),E[Jn]=void 0)};H?U(H,[E,N]):N()},leave(E,H){const G=String(e.key);if(E[Jn]&&E[Jn](!0),n.isUnmounting)return H();W(d,[E]);let k=!1;const Z=E[bt]=N=>{k||(k=!0,H(),N?W(y,[E]):W(v,[E]),E[bt]=void 0,O[G]===e&&delete O[G])};O[G]=e,g?U(g,[E,Z]):Z()},clone(E){const H=In(E,t,n,r,s);return s&&s(H),H}};return q}function Kr(e){if(kr(e))return e=Pt(e),e.children=null,e}function wo(e){if(!kr(e))return yl(e.type)&&e.children?Cl(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&J(n.default))return n.default()}}function Ht(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Ht(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Qs(e,t=!1,n){let r=[],s=0;for(let o=0;o<e.length;o++){let i=e[o];const l=n==null?i.key:String(n)+String(i.key!=null?i.key:o);i.type===$e?(i.patchFlag&128&&s++,r=r.concat(Qs(i.children,t,l))):(t||i.type!==Se)&&r.push(l!=null?Pt(i,{key:l}):i)}if(s>1)for(let o=0;o<r.length;o++)r[o].patchFlag=-2;return r}/*! #__NO_SIDE_EFFECTS__ */function Ys(e,t){return J(e)?ge({name:e.name},t,{setup:e}):e}function Rl(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function fr(e,t,n,r,s=!1){if(K(e)){e.forEach((v,y)=>fr(v,t&&(K(t)?t[y]:t),n,r,s));return}if(on(r)&&!s){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&fr(e,t,n,r.component.subTree);return}const o=r.shapeFlag&4?Ir(r.component):r.el,i=s?null:o,{i:l,r:a}=e,u=t&&t.r,c=l.refs===se?l.refs={}:l.refs,f=l.setupState,d=ne(f),g=f===se?()=>!1:v=>le(d,v);if(u!=null&&u!==a&&(he(u)?(c[u]=null,g(u)&&(f[u]=null)):pe(u)&&(u.value=null)),J(a))Bn(a,l,12,[i,c]);else{const v=he(a),y=pe(a);if(v||y){const P=()=>{if(e.f){const T=v?g(a)?f[a]:c[a]:a.value;s?K(T)&&js(T,o):K(T)?T.includes(o)||T.push(o):v?(c[a]=[o],g(a)&&(f[a]=c[a])):(a.value=[o],e.k&&(c[e.k]=a.value))}else v?(c[a]=i,g(a)&&(f[a]=i)):y&&(a.value=i,e.k&&(c[e.k]=i))};i?(P.id=-1,Re(P,n)):P()}}}Pr().requestIdleCallback;Pr().cancelIdleCallback;const on=e=>!!e.type.__asyncLoader,kr=e=>e.type.__isKeepAlive;function Wc(e,t){Tl(e,"a",t)}function Gc(e,t){Tl(e,"da",t)}function Tl(e,t,n=_e){const r=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(Or(t,r,n),n){let s=n.parent;for(;s&&s.parent;)kr(s.parent.vnode)&&Qc(r,t,n,s),s=s.parent}}function Qc(e,t,n,r){const s=Or(t,e,r,!0);kl(()=>{js(r[t],s)},n)}function Or(e,t,n=_e,r=!1){if(n){const s=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{ut();const l=Vn(n),a=We(t,n,e,i);return l(),ft(),a});return r?s.unshift(o):s.push(o),o}}const ht=e=>(t,n=_e)=>{(!Dn||e==="sp")&&Or(e,(...r)=>t(...r),n)},Yc=ht("bm"),Js=ht("m"),Jc=ht("bu"),Al=ht("u"),Xs=ht("bum"),kl=ht("um"),Xc=ht("sp"),Zc=ht("rtg"),eu=ht("rtc");function tu(e,t=_e){Or("ec",e,t)}const Ol="components";function nu(e,t){return Ml(Ol,e,!0,t)||e}const Ll=Symbol.for("v-ndc");function pp(e){return he(e)?Ml(Ol,e,!1)||e:e||Ll}function Ml(e,t,n=!0,r=!1){const s=me||_e;if(s){const o=s.type;{const l=zu(o,!1);if(l&&(l===t||l===De(t)||l===Cr(De(t))))return o}const i=xo(s[e]||o[e],t)||xo(s.appContext[e],t);return!i&&r?o:i}}function xo(e,t){return e&&(e[t]||e[De(t)]||e[Cr(De(t))])}function gp(e,t,n,r){let s;const o=n,i=K(e);if(i||he(e)){const l=i&&Et(e);let a=!1,u=!1;l&&(a=!Be(e),u=Ct(e),e=Tr(e)),s=new Array(e.length);for(let c=0,f=e.length;c<f;c++)s[c]=t(a?u?lr(ye(e[c])):ye(e[c]):e[c],c,void 0,o)}else if(typeof e=="number"){s=new Array(e);for(let l=0;l<e;l++)s[l]=t(l+1,l,void 0,o)}else if(de(e))if(e[Symbol.iterator])s=Array.from(e,(l,a)=>t(l,a,void 0,o));else{const l=Object.keys(e);s=new Array(l.length);for(let a=0,u=l.length;a<u;a++){const c=l[a];s[a]=t(e[c],c,a,o)}}else s=[];return s}function mp(e,t,n={},r,s){if(me.ce||me.parent&&on(me.parent)&&me.parent.ce)return t!=="default"&&(n.name=t),pr(),gr($e,null,[Ae("slot",n,r)],64);let o=e[t];o&&o._c&&(o._d=!1),pr();const i=o&&$l(o(n)),l=n.key||i&&i.key,a=gr($e,{key:(l&&!dt(l)?l:`_${t}`)+""},i||[],i&&e._===1?64:-2);return!s&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),o&&o._c&&(o._d=!0),a}function $l(e){return e.some(t=>jn(t)?!(t.type===Se||t.type===$e&&!$l(t.children)):!0)?e:null}const vs=e=>e?ea(e)?Ir(e):vs(e.parent):null,Pn=ge(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>vs(e.parent),$root:e=>vs(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Nl(e),$forceUpdate:e=>e.f||(e.f=()=>{Gs(e.update)}),$nextTick:e=>e.n||(e.n=Ws.bind(e.proxy)),$watch:e=>Cu.bind(e)}),Wr=(e,t)=>e!==se&&!e.__isScriptSetup&&le(e,t),ru={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:s,props:o,accessCache:i,type:l,appContext:a}=e;let u;if(t[0]!=="$"){const g=i[t];if(g!==void 0)switch(g){case 1:return r[t];case 2:return s[t];case 4:return n[t];case 3:return o[t]}else{if(Wr(r,t))return i[t]=1,r[t];if(s!==se&&le(s,t))return i[t]=2,s[t];if((u=e.propsOptions[0])&&le(u,t))return i[t]=3,o[t];if(n!==se&&le(n,t))return i[t]=4,n[t];_s&&(i[t]=0)}}const c=Pn[t];let f,d;if(c)return t==="$attrs"&&xe(e.attrs,"get",""),c(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(n!==se&&le(n,t))return i[t]=4,n[t];if(d=a.config.globalProperties,le(d,t))return d[t]},set({_:e},t,n){const{data:r,setupState:s,ctx:o}=e;return Wr(s,t)?(s[t]=n,!0):r!==se&&le(r,t)?(r[t]=n,!0):le(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:s,propsOptions:o}},i){let l;return!!n[i]||e!==se&&le(e,i)||Wr(t,i)||(l=o[0])&&le(l,i)||le(r,i)||le(Pn,i)||le(s.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:le(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function dr(e){return K(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}function vp(e,t){return!e||!t?e||t:K(e)&&K(t)?e.concat(t):ge({},dr(e),dr(t))}let _s=!0;function su(e){const t=Nl(e),n=e.proxy,r=e.ctx;_s=!1,t.beforeCreate&&So(t.beforeCreate,e,"bc");const{data:s,computed:o,methods:i,watch:l,provide:a,inject:u,created:c,beforeMount:f,mounted:d,beforeUpdate:g,updated:v,activated:y,deactivated:P,beforeDestroy:T,beforeUnmount:I,destroyed:j,unmounted:A,render:O,renderTracked:W,renderTriggered:U,errorCaptured:q,serverPrefetch:E,expose:H,inheritAttrs:G,components:k,directives:Z,filters:N}=t;if(u&&ou(u,r,null),i)for(const X in i){const oe=i[X];J(oe)&&(r[X]=oe.bind(n))}if(s){const X=s.call(n,n);de(X)&&(e.data=zt(X))}if(_s=!0,o)for(const X in o){const oe=o[X],st=J(oe)?oe.bind(n,n):J(oe.get)?oe.get.bind(n,n):rt,pt=!J(oe)&&J(oe.set)?oe.set.bind(n):rt,Qe=Q({get:st,set:pt});Object.defineProperty(r,X,{enumerable:!0,configurable:!0,get:()=>Qe.value,set:ke=>Qe.value=ke})}if(l)for(const X in l)Il(l[X],r,n,X);if(a){const X=J(a)?a.call(n):a;Reflect.ownKeys(X).forEach(oe=>{tr(oe,X[oe])})}c&&So(c,e,"c");function re(X,oe){K(oe)?oe.forEach(st=>X(st.bind(n))):oe&&X(oe.bind(n))}if(re(Yc,f),re(Js,d),re(Jc,g),re(Al,v),re(Wc,y),re(Gc,P),re(tu,q),re(eu,W),re(Zc,U),re(Xs,I),re(kl,A),re(Xc,E),K(H))if(H.length){const X=e.exposed||(e.exposed={});H.forEach(oe=>{Object.defineProperty(X,oe,{get:()=>n[oe],set:st=>n[oe]=st})})}else e.exposed||(e.exposed={});O&&e.render===rt&&(e.render=O),G!=null&&(e.inheritAttrs=G),k&&(e.components=k),Z&&(e.directives=Z),E&&Rl(e)}function ou(e,t,n=rt){K(e)&&(e=ys(e));for(const r in e){const s=e[r];let o;de(s)?"default"in s?o=Ve(s.from||r,s.default,!0):o=Ve(s.from||r):o=Ve(s),pe(o)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[r]=o}}function So(e,t,n){We(K(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function Il(e,t,n,r){let s=r.includes(".")?Wl(n,r):()=>n[r];if(he(e)){const o=t[e];J(o)&&Rn(s,o)}else if(J(e))Rn(s,e.bind(n));else if(de(e))if(K(e))e.forEach(o=>Il(o,t,n,r));else{const o=J(e.handler)?e.handler.bind(n):t[e.handler];J(o)&&Rn(s,o,e)}}function Nl(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:s,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let a;return l?a=l:!s.length&&!n&&!r?a=t:(a={},s.length&&s.forEach(u=>hr(a,u,i,!0)),hr(a,t,i)),de(t)&&o.set(t,a),a}function hr(e,t,n,r=!1){const{mixins:s,extends:o}=t;o&&hr(e,o,n,!0),s&&s.forEach(i=>hr(e,i,n,!0));for(const i in t)if(!(r&&i==="expose")){const l=iu[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const iu={data:Eo,props:Co,emits:Co,methods:_n,computed:_n,beforeCreate:Ce,created:Ce,beforeMount:Ce,mounted:Ce,beforeUpdate:Ce,updated:Ce,beforeDestroy:Ce,beforeUnmount:Ce,destroyed:Ce,unmounted:Ce,activated:Ce,deactivated:Ce,errorCaptured:Ce,serverPrefetch:Ce,components:_n,directives:_n,watch:au,provide:Eo,inject:lu};function Eo(e,t){return t?e?function(){return ge(J(e)?e.call(this,this):e,J(t)?t.call(this,this):t)}:t:e}function lu(e,t){return _n(ys(e),ys(t))}function ys(e){if(K(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Ce(e,t){return e?[...new Set([].concat(e,t))]:t}function _n(e,t){return e?ge(Object.create(null),e,t):t}function Co(e,t){return e?K(e)&&K(t)?[...new Set([...e,...t])]:ge(Object.create(null),dr(e),dr(t??{})):t}function au(e,t){if(!e)return t;if(!t)return e;const n=ge(Object.create(null),e);for(const r in t)n[r]=Ce(e[r],t[r]);return n}function jl(){return{app:null,config:{isNativeTag:Ga,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let cu=0;function uu(e,t){return function(r,s=null){J(r)||(r=ge({},r)),s!=null&&!de(s)&&(s=null);const o=jl(),i=new WeakSet,l=[];let a=!1;const u=o.app={_uid:cu++,_component:r,_props:s,_container:null,_context:o,_instance:null,version:Ku,get config(){return o.config},set config(c){},use(c,...f){return i.has(c)||(c&&J(c.install)?(i.add(c),c.install(u,...f)):J(c)&&(i.add(c),c(u,...f))),u},mixin(c){return o.mixins.includes(c)||o.mixins.push(c),u},component(c,f){return f?(o.components[c]=f,u):o.components[c]},directive(c,f){return f?(o.directives[c]=f,u):o.directives[c]},mount(c,f,d){if(!a){const g=u._ceVNode||Ae(r,s);return g.appContext=o,d===!0?d="svg":d===!1&&(d=void 0),e(g,c,d),a=!0,u._container=c,c.__vue_app__=u,Ir(g.component)}},onUnmount(c){l.push(c)},unmount(){a&&(We(l,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(c,f){return o.provides[c]=f,u},runWithContext(c){const f=qt;qt=u;try{return c()}finally{qt=f}}};return u}}let qt=null;function tr(e,t){if(_e){let n=_e.provides;const r=_e.parent&&_e.parent.provides;r===n&&(n=_e.provides=Object.create(r)),n[e]=t}}function Ve(e,t,n=!1){const r=_e||me;if(r||qt){const s=qt?qt._context.provides:r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(s&&e in s)return s[e];if(arguments.length>1)return n&&J(t)?t.call(r&&r.proxy):t}}function fu(){return!!(_e||me||qt)}const Dl={},ql=()=>Object.create(Dl),Fl=e=>Object.getPrototypeOf(e)===Dl;function du(e,t,n,r=!1){const s={},o=ql();e.propsDefaults=Object.create(null),Hl(e,t,s,o);for(const i in e.propsOptions[0])i in s||(s[i]=void 0);n?e.props=r?s:al(s):e.type.props?e.props=s:e.props=o,e.attrs=o}function hu(e,t,n,r){const{props:s,attrs:o,vnode:{patchFlag:i}}=e,l=ne(s),[a]=e.propsOptions;let u=!1;if((r||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let f=0;f<c.length;f++){let d=c[f];if(Mr(e.emitsOptions,d))continue;const g=t[d];if(a)if(le(o,d))g!==o[d]&&(o[d]=g,u=!0);else{const v=De(d);s[v]=bs(a,l,v,g,e,!1)}else g!==o[d]&&(o[d]=g,u=!0)}}}else{Hl(e,t,s,o)&&(u=!0);let c;for(const f in l)(!t||!le(t,f)&&((c=Rt(f))===f||!le(t,c)))&&(a?n&&(n[f]!==void 0||n[c]!==void 0)&&(s[f]=bs(a,l,f,void 0,e,!0)):delete s[f]);if(o!==l)for(const f in o)(!t||!le(t,f))&&(delete o[f],u=!0)}u&&at(e.attrs,"set","")}function Hl(e,t,n,r){const[s,o]=e.propsOptions;let i=!1,l;if(t)for(let a in t){if(xn(a))continue;const u=t[a];let c;s&&le(s,c=De(a))?!o||!o.includes(c)?n[c]=u:(l||(l={}))[c]=u:Mr(e.emitsOptions,a)||(!(a in r)||u!==r[a])&&(r[a]=u,i=!0)}if(o){const a=ne(n),u=l||se;for(let c=0;c<o.length;c++){const f=o[c];n[f]=bs(s,a,f,u[f],e,!le(u,f))}}return i}function bs(e,t,n,r,s,o){const i=e[n];if(i!=null){const l=le(i,"default");if(l&&r===void 0){const a=i.default;if(i.type!==Function&&!i.skipFactory&&J(a)){const{propsDefaults:u}=s;if(n in u)r=u[n];else{const c=Vn(s);r=u[n]=a.call(null,t),c()}}else r=a;s.ce&&s.ce._setProp(n,r)}i[0]&&(o&&!l?r=!1:i[1]&&(r===""||r===Rt(n))&&(r=!0))}return r}const pu=new WeakMap;function Bl(e,t,n=!1){const r=n?pu:t.propsCache,s=r.get(e);if(s)return s;const o=e.props,i={},l=[];let a=!1;if(!J(e)){const c=f=>{a=!0;const[d,g]=Bl(f,t,!0);ge(i,d),g&&l.push(...g)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!o&&!a)return de(e)&&r.set(e,nn),nn;if(K(o))for(let c=0;c<o.length;c++){const f=De(o[c]);Po(f)&&(i[f]=se)}else if(o)for(const c in o){const f=De(c);if(Po(f)){const d=o[c],g=i[f]=K(d)||J(d)?{type:d}:ge({},d),v=g.type;let y=!1,P=!0;if(K(v))for(let T=0;T<v.length;++T){const I=v[T],j=J(I)&&I.name;if(j==="Boolean"){y=!0;break}else j==="String"&&(P=!1)}else y=J(v)&&v.name==="Boolean";g[0]=y,g[1]=P,(y||le(g,"default"))&&l.push(f)}}const u=[i,l];return de(e)&&r.set(e,u),u}function Po(e){return e[0]!=="$"&&!xn(e)}const Zs=e=>e[0]==="_"||e==="$stable",eo=e=>K(e)?e.map(nt):[nt(e)],gu=(e,t,n)=>{if(t._n)return t;const r=Vc((...s)=>eo(t(...s)),n);return r._c=!1,r},Vl=(e,t,n)=>{const r=e._ctx;for(const s in e){if(Zs(s))continue;const o=e[s];if(J(o))t[s]=gu(s,o,r);else if(o!=null){const i=eo(o);t[s]=()=>i}}},zl=(e,t)=>{const n=eo(t);e.slots.default=()=>n},Ul=(e,t,n)=>{for(const r in t)(n||!Zs(r))&&(e[r]=t[r])},mu=(e,t,n)=>{const r=e.slots=ql();if(e.vnode.shapeFlag&32){const s=t._;s?(Ul(r,t,n),n&&Hi(r,"_",s,!0)):Vl(t,r)}else t&&zl(e,t)},vu=(e,t,n)=>{const{vnode:r,slots:s}=e;let o=!0,i=se;if(r.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:Ul(s,t,n):(o=!t.$stable,Vl(t,s)),i=t}else t&&(zl(e,t),i={default:1});if(o)for(const l in s)!Zs(l)&&i[l]==null&&delete s[l]},Re=Ou;function _u(e){return yu(e)}function yu(e,t){const n=Pr();n.__VUE__=!0;const{insert:r,remove:s,patchProp:o,createElement:i,createText:l,createComment:a,setText:u,setElementText:c,parentNode:f,nextSibling:d,setScopeId:g=rt,insertStaticContent:v}=e,y=(h,p,m,_=null,x=null,w=null,L=void 0,R=null,C=!!p.dynamicChildren)=>{if(h===p)return;h&&!It(h,p)&&(_=b(h),ke(h,x,w,!0),h=null),p.patchFlag===-2&&(C=!1,p.dynamicChildren=null);const{type:S,ref:V,shapeFlag:$}=p;switch(S){case $r:P(h,p,m,_);break;case Se:T(h,p,m,_);break;case Qr:h==null&&I(p,m,_,L);break;case $e:k(h,p,m,_,x,w,L,R,C);break;default:$&1?O(h,p,m,_,x,w,L,R,C):$&6?Z(h,p,m,_,x,w,L,R,C):($&64||$&128)&&S.process(h,p,m,_,x,w,L,R,C,F)}V!=null&&x&&fr(V,h&&h.ref,w,p||h,!p)},P=(h,p,m,_)=>{if(h==null)r(p.el=l(p.children),m,_);else{const x=p.el=h.el;p.children!==h.children&&u(x,p.children)}},T=(h,p,m,_)=>{h==null?r(p.el=a(p.children||""),m,_):p.el=h.el},I=(h,p,m,_)=>{[h.el,h.anchor]=v(h.children,p,m,_,h.el,h.anchor)},j=({el:h,anchor:p},m,_)=>{let x;for(;h&&h!==p;)x=d(h),r(h,m,_),h=x;r(p,m,_)},A=({el:h,anchor:p})=>{let m;for(;h&&h!==p;)m=d(h),s(h),h=m;s(p)},O=(h,p,m,_,x,w,L,R,C)=>{p.type==="svg"?L="svg":p.type==="math"&&(L="mathml"),h==null?W(p,m,_,x,w,L,R,C):E(h,p,x,w,L,R,C)},W=(h,p,m,_,x,w,L,R)=>{let C,S;const{props:V,shapeFlag:$,transition:B,dirs:Y}=h;if(C=h.el=i(h.type,w,V&&V.is,V),$&8?c(C,h.children):$&16&&q(h.children,C,null,_,x,Gr(h,w),L,R),Y&&Tt(h,null,_,"created"),U(C,h,h.scopeId,L,_),V){for(const ue in V)ue!=="value"&&!xn(ue)&&o(C,ue,null,V[ue],w,_);"value"in V&&o(C,"value",null,V.value,w),(S=V.onVnodeBeforeMount)&&Ze(S,_,h)}Y&&Tt(h,null,_,"beforeMount");const te=bu(x,B);te&&B.beforeEnter(C),r(C,p,m),((S=V&&V.onVnodeMounted)||te||Y)&&Re(()=>{S&&Ze(S,_,h),te&&B.enter(C),Y&&Tt(h,null,_,"mounted")},x)},U=(h,p,m,_,x)=>{if(m&&g(h,m),_)for(let w=0;w<_.length;w++)g(h,_[w]);if(x){let w=x.subTree;if(p===w||Yl(w.type)&&(w.ssContent===p||w.ssFallback===p)){const L=x.vnode;U(h,L,L.scopeId,L.slotScopeIds,x.parent)}}},q=(h,p,m,_,x,w,L,R,C=0)=>{for(let S=C;S<h.length;S++){const V=h[S]=R?wt(h[S]):nt(h[S]);y(null,V,p,m,_,x,w,L,R)}},E=(h,p,m,_,x,w,L)=>{const R=p.el=h.el;let{patchFlag:C,dynamicChildren:S,dirs:V}=p;C|=h.patchFlag&16;const $=h.props||se,B=p.props||se;let Y;if(m&&At(m,!1),(Y=B.onVnodeBeforeUpdate)&&Ze(Y,m,p,h),V&&Tt(p,h,m,"beforeUpdate"),m&&At(m,!0),($.innerHTML&&B.innerHTML==null||$.textContent&&B.textContent==null)&&c(R,""),S?H(h.dynamicChildren,S,R,m,_,Gr(p,x),w):L||oe(h,p,R,null,m,_,Gr(p,x),w,!1),C>0){if(C&16)G(R,$,B,m,x);else if(C&2&&$.class!==B.class&&o(R,"class",null,B.class,x),C&4&&o(R,"style",$.style,B.style,x),C&8){const te=p.dynamicProps;for(let ue=0;ue<te.length;ue++){const ae=te[ue],Ie=$[ae],Oe=B[ae];(Oe!==Ie||ae==="value")&&o(R,ae,Ie,Oe,x,m)}}C&1&&h.children!==p.children&&c(R,p.children)}else!L&&S==null&&G(R,$,B,m,x);((Y=B.onVnodeUpdated)||V)&&Re(()=>{Y&&Ze(Y,m,p,h),V&&Tt(p,h,m,"updated")},_)},H=(h,p,m,_,x,w,L)=>{for(let R=0;R<p.length;R++){const C=h[R],S=p[R],V=C.el&&(C.type===$e||!It(C,S)||C.shapeFlag&70)?f(C.el):m;y(C,S,V,null,_,x,w,L,!0)}},G=(h,p,m,_,x)=>{if(p!==m){if(p!==se)for(const w in p)!xn(w)&&!(w in m)&&o(h,w,p[w],null,x,_);for(const w in m){if(xn(w))continue;const L=m[w],R=p[w];L!==R&&w!=="value"&&o(h,w,R,L,x,_)}"value"in m&&o(h,"value",p.value,m.value,x)}},k=(h,p,m,_,x,w,L,R,C)=>{const S=p.el=h?h.el:l(""),V=p.anchor=h?h.anchor:l("");let{patchFlag:$,dynamicChildren:B,slotScopeIds:Y}=p;Y&&(R=R?R.concat(Y):Y),h==null?(r(S,m,_),r(V,m,_),q(p.children||[],m,V,x,w,L,R,C)):$>0&&$&64&&B&&h.dynamicChildren?(H(h.dynamicChildren,B,m,x,w,L,R),(p.key!=null||x&&p===x.subTree)&&to(h,p,!0)):oe(h,p,m,V,x,w,L,R,C)},Z=(h,p,m,_,x,w,L,R,C)=>{p.slotScopeIds=R,h==null?p.shapeFlag&512?x.ctx.activate(p,m,_,L,C):N(p,m,_,x,w,L,C):ee(h,p,C)},N=(h,p,m,_,x,w,L)=>{const R=h.component=qu(h,_,x);if(kr(h)&&(R.ctx.renderer=F),Fu(R,!1,L),R.asyncDep){if(x&&x.registerDep(R,re,L),!h.el){const C=R.subTree=Ae(Se);T(null,C,p,m)}}else re(R,h,p,m,x,w,L)},ee=(h,p,m)=>{const _=p.component=h.component;if(Au(h,p,m))if(_.asyncDep&&!_.asyncResolved){X(_,p,m);return}else _.next=p,_.update();else p.el=h.el,_.vnode=p},re=(h,p,m,_,x,w,L)=>{const R=()=>{if(h.isMounted){let{next:$,bu:B,u:Y,parent:te,vnode:ue}=h;{const Je=Kl(h);if(Je){$&&($.el=ue.el,X(h,$,L)),Je.asyncDep.then(()=>{h.isUnmounted||R()});return}}let ae=$,Ie;At(h,!1),$?($.el=ue.el,X(h,$,L)):$=ue,B&&Hr(B),(Ie=$.props&&$.props.onVnodeBeforeUpdate)&&Ze(Ie,te,$,ue),At(h,!0);const Oe=To(h),Ye=h.subTree;h.subTree=Oe,y(Ye,Oe,f(Ye.el),b(Ye),h,x,w),$.el=Oe.el,ae===null&&ku(h,Oe.el),Y&&Re(Y,x),(Ie=$.props&&$.props.onVnodeUpdated)&&Re(()=>Ze(Ie,te,$,ue),x)}else{let $;const{el:B,props:Y}=p,{bm:te,m:ue,parent:ae,root:Ie,type:Oe}=h,Ye=on(p);At(h,!1),te&&Hr(te),!Ye&&($=Y&&Y.onVnodeBeforeMount)&&Ze($,ae,p),At(h,!0);{Ie.ce&&Ie.ce._injectChildStyle(Oe);const Je=h.subTree=To(h);y(null,Je,m,_,h,x,w),p.el=Je.el}if(ue&&Re(ue,x),!Ye&&($=Y&&Y.onVnodeMounted)){const Je=p;Re(()=>Ze($,ae,Je),x)}(p.shapeFlag&256||ae&&on(ae.vnode)&&ae.vnode.shapeFlag&256)&&h.a&&Re(h.a,x),h.isMounted=!0,p=m=_=null}};h.scope.on();const C=h.effect=new Gi(R);h.scope.off();const S=h.update=C.run.bind(C),V=h.job=C.runIfDirty.bind(C);V.i=h,V.id=h.uid,C.scheduler=()=>Gs(V),At(h,!0),S()},X=(h,p,m)=>{p.component=h;const _=h.vnode.props;h.vnode=p,h.next=null,hu(h,p.props,_,m),vu(h,p.children,m),ut(),vo(h),ft()},oe=(h,p,m,_,x,w,L,R,C=!1)=>{const S=h&&h.children,V=h?h.shapeFlag:0,$=p.children,{patchFlag:B,shapeFlag:Y}=p;if(B>0){if(B&128){pt(S,$,m,_,x,w,L,R,C);return}else if(B&256){st(S,$,m,_,x,w,L,R,C);return}}Y&8?(V&16&&qe(S,x,w),$!==S&&c(m,$)):V&16?Y&16?pt(S,$,m,_,x,w,L,R,C):qe(S,x,w,!0):(V&8&&c(m,""),Y&16&&q($,m,_,x,w,L,R,C))},st=(h,p,m,_,x,w,L,R,C)=>{h=h||nn,p=p||nn;const S=h.length,V=p.length,$=Math.min(S,V);let B;for(B=0;B<$;B++){const Y=p[B]=C?wt(p[B]):nt(p[B]);y(h[B],Y,m,null,x,w,L,R,C)}S>V?qe(h,x,w,!0,!1,$):q(p,m,_,x,w,L,R,C,$)},pt=(h,p,m,_,x,w,L,R,C)=>{let S=0;const V=p.length;let $=h.length-1,B=V-1;for(;S<=$&&S<=B;){const Y=h[S],te=p[S]=C?wt(p[S]):nt(p[S]);if(It(Y,te))y(Y,te,m,null,x,w,L,R,C);else break;S++}for(;S<=$&&S<=B;){const Y=h[$],te=p[B]=C?wt(p[B]):nt(p[B]);if(It(Y,te))y(Y,te,m,null,x,w,L,R,C);else break;$--,B--}if(S>$){if(S<=B){const Y=B+1,te=Y<V?p[Y].el:_;for(;S<=B;)y(null,p[S]=C?wt(p[S]):nt(p[S]),m,te,x,w,L,R,C),S++}}else if(S>B)for(;S<=$;)ke(h[S],x,w,!0),S++;else{const Y=S,te=S,ue=new Map;for(S=te;S<=B;S++){const Ne=p[S]=C?wt(p[S]):nt(p[S]);Ne.key!=null&&ue.set(Ne.key,S)}let ae,Ie=0;const Oe=B-te+1;let Ye=!1,Je=0;const fn=new Array(Oe);for(S=0;S<Oe;S++)fn[S]=0;for(S=Y;S<=$;S++){const Ne=h[S];if(Ie>=Oe){ke(Ne,x,w,!0);continue}let Xe;if(Ne.key!=null)Xe=ue.get(Ne.key);else for(ae=te;ae<=B;ae++)if(fn[ae-te]===0&&It(Ne,p[ae])){Xe=ae;break}Xe===void 0?ke(Ne,x,w,!0):(fn[Xe-te]=S+1,Xe>=Je?Je=Xe:Ye=!0,y(Ne,p[Xe],m,null,x,w,L,R,C),Ie++)}const uo=Ye?wu(fn):nn;for(ae=uo.length-1,S=Oe-1;S>=0;S--){const Ne=te+S,Xe=p[Ne],fo=Ne+1<V?p[Ne+1].el:_;fn[S]===0?y(null,Xe,m,fo,x,w,L,R,C):Ye&&(ae<0||S!==uo[ae]?Qe(Xe,m,fo,2):ae--)}}},Qe=(h,p,m,_,x=null)=>{const{el:w,type:L,transition:R,children:C,shapeFlag:S}=h;if(S&6){Qe(h.component.subTree,p,m,_);return}if(S&128){h.suspense.move(p,m,_);return}if(S&64){L.move(h,p,m,F);return}if(L===$e){r(w,p,m);for(let $=0;$<C.length;$++)Qe(C[$],p,m,_);r(h.anchor,p,m);return}if(L===Qr){j(h,p,m);return}if(_!==2&&S&1&&R)if(_===0)R.beforeEnter(w),r(w,p,m),Re(()=>R.enter(w),x);else{const{leave:$,delayLeave:B,afterLeave:Y}=R,te=()=>{h.ctx.isUnmounted?s(w):r(w,p,m)},ue=()=>{$(w,()=>{te(),Y&&Y()})};B?B(w,te,ue):ue()}else r(w,p,m)},ke=(h,p,m,_=!1,x=!1)=>{const{type:w,props:L,ref:R,children:C,dynamicChildren:S,shapeFlag:V,patchFlag:$,dirs:B,cacheIndex:Y}=h;if($===-2&&(x=!1),R!=null&&(ut(),fr(R,null,m,h,!0),ft()),Y!=null&&(p.renderCache[Y]=void 0),V&256){p.ctx.deactivate(h);return}const te=V&1&&B,ue=!on(h);let ae;if(ue&&(ae=L&&L.onVnodeBeforeUnmount)&&Ze(ae,p,h),V&6)Kn(h.component,m,_);else{if(V&128){h.suspense.unmount(m,_);return}te&&Tt(h,null,p,"beforeUnmount"),V&64?h.type.remove(h,p,m,F,_):S&&!S.hasOnce&&(w!==$e||$>0&&$&64)?qe(S,p,m,!1,!0):(w===$e&&$&384||!x&&V&16)&&qe(C,p,m),_&&Kt(h)}(ue&&(ae=L&&L.onVnodeUnmounted)||te)&&Re(()=>{ae&&Ze(ae,p,h),te&&Tt(h,null,p,"unmounted")},m)},Kt=h=>{const{type:p,el:m,anchor:_,transition:x}=h;if(p===$e){Wt(m,_);return}if(p===Qr){A(h);return}const w=()=>{s(m),x&&!x.persisted&&x.afterLeave&&x.afterLeave()};if(h.shapeFlag&1&&x&&!x.persisted){const{leave:L,delayLeave:R}=x,C=()=>L(m,w);R?R(h.el,w,C):C()}else w()},Wt=(h,p)=>{let m;for(;h!==p;)m=d(h),s(h),h=m;s(p)},Kn=(h,p,m)=>{const{bum:_,scope:x,job:w,subTree:L,um:R,m:C,a:S,parent:V,slots:{__:$}}=h;Ro(C),Ro(S),_&&Hr(_),V&&K($)&&$.forEach(B=>{V.renderCache[B]=void 0}),x.stop(),w&&(w.flags|=8,ke(L,h,p,m)),R&&Re(R,p),Re(()=>{h.isUnmounted=!0},p),p&&p.pendingBranch&&!p.isUnmounted&&h.asyncDep&&!h.asyncResolved&&h.suspenseId===p.pendingId&&(p.deps--,p.deps===0&&p.resolve())},qe=(h,p,m,_=!1,x=!1,w=0)=>{for(let L=w;L<h.length;L++)ke(h[L],p,m,_,x)},b=h=>{if(h.shapeFlag&6)return b(h.component.subTree);if(h.shapeFlag&128)return h.suspense.next();const p=d(h.anchor||h.el),m=p&&p[_l];return m?d(m):p};let D=!1;const M=(h,p,m)=>{h==null?p._vnode&&ke(p._vnode,null,null,!0):y(p._vnode||null,h,p,null,null,null,m),p._vnode=h,D||(D=!0,vo(),pl(),D=!1)},F={p:y,um:ke,m:Qe,r:Kt,mt:N,mc:q,pc:oe,pbc:H,n:b,o:e};return{render:M,hydrate:void 0,createApp:uu(M)}}function Gr({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function At({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function bu(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function to(e,t,n=!1){const r=e.children,s=t.children;if(K(r)&&K(s))for(let o=0;o<r.length;o++){const i=r[o];let l=s[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=s[o]=wt(s[o]),l.el=i.el),!n&&l.patchFlag!==-2&&to(i,l)),l.type===$r&&(l.el=i.el),l.type===Se&&!l.el&&(l.el=i.el)}}function wu(e){const t=e.slice(),n=[0];let r,s,o,i,l;const a=e.length;for(r=0;r<a;r++){const u=e[r];if(u!==0){if(s=n[n.length-1],e[s]<u){t[r]=s,n.push(r);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<u?o=l+1:i=l;u<e[n[o]]&&(o>0&&(t[r]=n[o-1]),n[o]=r)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function Kl(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Kl(t)}function Ro(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const xu=Symbol.for("v-scx"),Su=()=>Ve(xu);function _p(e,t){return Lr(e,null,t)}function Eu(e,t){return Lr(e,null,{flush:"sync"})}function Rn(e,t,n){return Lr(e,t,n)}function Lr(e,t,n=se){const{immediate:r,deep:s,flush:o,once:i}=n,l=ge({},n),a=t&&r||!t&&o!=="post";let u;if(Dn){if(o==="sync"){const g=Su();u=g.__watcherHandles||(g.__watcherHandles=[])}else if(!a){const g=()=>{};return g.stop=rt,g.resume=rt,g.pause=rt,g}}const c=_e;l.call=(g,v,y)=>We(g,c,v,y);let f=!1;o==="post"?l.scheduler=g=>{Re(g,c&&c.suspense)}:o!=="sync"&&(f=!0,l.scheduler=(g,v)=>{v?g():Gs(g)}),l.augmentJob=g=>{t&&(g.flags|=4),f&&(g.flags|=2,c&&(g.id=c.uid,g.i=c))};const d=qc(e,t,l);return Dn&&(u?u.push(d):a&&d()),d}function Cu(e,t,n){const r=this.proxy,s=he(e)?e.includes(".")?Wl(r,e):()=>r[e]:e.bind(r,r);let o;J(t)?o=t:(o=t.handler,n=t);const i=Vn(this),l=Lr(s,o.bind(r),n);return i(),l}function Wl(e,t){const n=t.split(".");return()=>{let r=e;for(let s=0;s<n.length&&r;s++)r=r[n[s]];return r}}function yp(e,t,n=se){const r=un(),s=De(t),o=Rt(t),i=Gl(e,s),l=Lc((a,u)=>{let c,f=se,d;return Eu(()=>{const g=e[s];Me(c,g)&&(c=g,u())}),{get(){return a(),n.get?n.get(c):c},set(g){const v=n.set?n.set(g):g;if(!Me(v,c)&&!(f!==se&&Me(g,f)))return;const y=r.vnode.props;y&&(t in y||s in y||o in y)&&(`onUpdate:${t}`in y||`onUpdate:${s}`in y||`onUpdate:${o}`in y)||(c=g,u()),r.emit(`update:${t}`,v),Me(g,v)&&Me(g,f)&&!Me(v,d)&&u(),f=g,d=v}}});return l[Symbol.iterator]=()=>{let a=0;return{next(){return a<2?{value:a++?i||se:l,done:!1}:{done:!0}}}},l}const Gl=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${De(t)}Modifiers`]||e[`${Rt(t)}Modifiers`];function Pu(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||se;let s=n;const o=t.startsWith("update:"),i=o&&Gl(r,t.slice(7));i&&(i.trim&&(s=n.map(c=>he(c)?c.trim():c)),i.number&&(s=n.map(Za)));let l,a=r[l=Fr(t)]||r[l=Fr(De(t))];!a&&o&&(a=r[l=Fr(Rt(t))]),a&&We(a,e,6,s);const u=r[l+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,We(u,e,6,s)}}function Ql(e,t,n=!1){const r=t.emitsCache,s=r.get(e);if(s!==void 0)return s;const o=e.emits;let i={},l=!1;if(!J(e)){const a=u=>{const c=Ql(u,t,!0);c&&(l=!0,ge(i,c))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!o&&!l?(de(e)&&r.set(e,null),null):(K(o)?o.forEach(a=>i[a]=null):ge(i,o),de(e)&&r.set(e,i),i)}function Mr(e,t){return!e||!xr(t)?!1:(t=t.slice(2).replace(/Once$/,""),le(e,t[0].toLowerCase()+t.slice(1))||le(e,Rt(t))||le(e,t))}function To(e){const{type:t,vnode:n,proxy:r,withProxy:s,propsOptions:[o],slots:i,attrs:l,emit:a,render:u,renderCache:c,props:f,data:d,setupState:g,ctx:v,inheritAttrs:y}=e,P=ur(e);let T,I;try{if(n.shapeFlag&4){const A=s||r,O=A;T=nt(u.call(O,A,c,f,g,d,v)),I=l}else{const A=t;T=nt(A.length>1?A(f,{attrs:l,slots:i,emit:a}):A(f,null)),I=t.props?l:Ru(l)}}catch(A){Tn.length=0,Ar(A,e,1),T=Ae(Se)}let j=T;if(I&&y!==!1){const A=Object.keys(I),{shapeFlag:O}=j;A.length&&O&7&&(o&&A.some(Ns)&&(I=Tu(I,o)),j=Pt(j,I,!1,!0))}return n.dirs&&(j=Pt(j,null,!1,!0),j.dirs=j.dirs?j.dirs.concat(n.dirs):n.dirs),n.transition&&Ht(j,n.transition),T=j,ur(P),T}const Ru=e=>{let t;for(const n in e)(n==="class"||n==="style"||xr(n))&&((t||(t={}))[n]=e[n]);return t},Tu=(e,t)=>{const n={};for(const r in e)(!Ns(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function Au(e,t,n){const{props:r,children:s,component:o}=e,{props:i,children:l,patchFlag:a}=t,u=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return r?Ao(r,i,u):!!i;if(a&8){const c=t.dynamicProps;for(let f=0;f<c.length;f++){const d=c[f];if(i[d]!==r[d]&&!Mr(u,d))return!0}}}else return(s||l)&&(!l||!l.$stable)?!0:r===i?!1:r?i?Ao(r,i,u):!0:!!i;return!1}function Ao(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let s=0;s<r.length;s++){const o=r[s];if(t[o]!==e[o]&&!Mr(n,o))return!0}return!1}function ku({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const Yl=e=>e.__isSuspense;function Ou(e,t){t&&t.pendingBranch?K(e)?t.effects.push(...e):t.effects.push(e):Bc(e)}const $e=Symbol.for("v-fgt"),$r=Symbol.for("v-txt"),Se=Symbol.for("v-cmt"),Qr=Symbol.for("v-stc"),Tn=[];let je=null;function pr(e=!1){Tn.push(je=e?null:[])}function Lu(){Tn.pop(),je=Tn[Tn.length-1]||null}let Nn=1;function ko(e,t=!1){Nn+=e,e<0&&je&&t&&(je.hasOnce=!0)}function Jl(e){return e.dynamicChildren=Nn>0?je||nn:null,Lu(),Nn>0&&je&&je.push(e),e}function bp(e,t,n,r,s,o){return Jl(Zl(e,t,n,r,s,o,!0))}function gr(e,t,n,r,s){return Jl(Ae(e,t,n,r,s,!0))}function jn(e){return e?e.__v_isVNode===!0:!1}function It(e,t){return e.type===t.type&&e.key===t.key}const Xl=({key:e})=>e??null,nr=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?he(e)||pe(e)||J(e)?{i:me,r:e,k:t,f:!!n}:e:null);function Zl(e,t=null,n=null,r=0,s=null,o=e===$e?0:1,i=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Xl(t),ref:t&&nr(t),scopeId:ml,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:r,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:me};return l?(no(a,n),o&128&&e.normalize(a)):n&&(a.shapeFlag|=he(n)?8:16),Nn>0&&!i&&je&&(a.patchFlag>0||o&6)&&a.patchFlag!==32&&je.push(a),a}const Ae=Mu;function Mu(e,t=null,n=null,r=0,s=null,o=!1){if((!e||e===Ll)&&(e=Se),jn(e)){const l=Pt(e,t,!0);return n&&no(l,n),Nn>0&&!o&&je&&(l.shapeFlag&6?je[je.indexOf(e)]=l:je.push(l)),l.patchFlag=-2,l}if(Uu(e)&&(e=e.__vccOpts),t){t=$u(t);let{class:l,style:a}=t;l&&!he(l)&&(t.class=Fs(l)),de(a)&&(Ks(a)&&!K(a)&&(a=ge({},a)),t.style=qs(a))}const i=he(e)?1:Yl(e)?128:yl(e)?64:de(e)?4:J(e)?2:0;return Zl(e,t,n,r,s,i,o,!0)}function $u(e){return e?Ks(e)||Fl(e)?ge({},e):e:null}function Pt(e,t,n=!1,r=!1){const{props:s,ref:o,patchFlag:i,children:l,transition:a}=e,u=t?Nu(s||{},t):s,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Xl(u),ref:t&&t.ref?n&&o?K(o)?o.concat(nr(t)):[o,nr(t)]:nr(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==$e?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Pt(e.ssContent),ssFallback:e.ssFallback&&Pt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&r&&Ht(c,a.clone(c)),c}function Iu(e=" ",t=0){return Ae($r,null,e,t)}function wp(e="",t=!1){return t?(pr(),gr(Se,null,e)):Ae(Se,null,e)}function nt(e){return e==null||typeof e=="boolean"?Ae(Se):K(e)?Ae($e,null,e.slice()):jn(e)?wt(e):Ae($r,null,String(e))}function wt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Pt(e)}function no(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(K(t))n=16;else if(typeof t=="object")if(r&65){const s=t.default;s&&(s._c&&(s._d=!1),no(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!Fl(t)?t._ctx=me:s===3&&me&&(me.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else J(t)?(t={default:t,_ctx:me},n=32):(t=String(t),r&64?(n=16,t=[Iu(t)]):n=8);e.children=t,e.shapeFlag|=n}function Nu(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const s in r)if(s==="class")t.class!==r.class&&(t.class=Fs([t.class,r.class]));else if(s==="style")t.style=qs([t.style,r.style]);else if(xr(s)){const o=t[s],i=r[s];i&&o!==i&&!(K(o)&&o.includes(i))&&(t[s]=o?[].concat(o,i):i)}else s!==""&&(t[s]=r[s])}return t}function Ze(e,t,n,r=null){We(e,t,7,[n,r])}const ju=jl();let Du=0;function qu(e,t,n){const r=e.type,s=(t?t.appContext:e.appContext)||ju,o={uid:Du++,vnode:e,type:r,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Ui(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Bl(r,s),emitsOptions:Ql(r,s),emit:null,emitted:null,propsDefaults:se,inheritAttrs:r.inheritAttrs,ctx:se,data:se,props:se,attrs:se,slots:se,refs:se,setupState:se,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=Pu.bind(null,o),e.ce&&e.ce(o),o}let _e=null;const un=()=>_e||me;let mr,ws;{const e=Pr(),t=(n,r)=>{let s;return(s=e[n])||(s=e[n]=[]),s.push(r),o=>{s.length>1?s.forEach(i=>i(o)):s[0](o)}};mr=t("__VUE_INSTANCE_SETTERS__",n=>_e=n),ws=t("__VUE_SSR_SETTERS__",n=>Dn=n)}const Vn=e=>{const t=_e;return mr(e),e.scope.on(),()=>{e.scope.off(),mr(t)}},Oo=()=>{_e&&_e.scope.off(),mr(null)};function ea(e){return e.vnode.shapeFlag&4}let Dn=!1;function Fu(e,t=!1,n=!1){t&&ws(t);const{props:r,children:s}=e.vnode,o=ea(e);du(e,r,o,t),mu(e,s,n||t);const i=o?Hu(e,t):void 0;return t&&ws(!1),i}function Hu(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,ru);const{setup:r}=n;if(r){ut();const s=e.setupContext=r.length>1?Vu(e):null,o=Vn(e),i=Bn(r,e,0,[e.props,s]),l=Di(i);if(ft(),o(),(l||e.sp)&&!on(e)&&Rl(e),l){if(i.then(Oo,Oo),t)return i.then(a=>{Lo(e,a)}).catch(a=>{Ar(a,e,0)});e.asyncDep=i}else Lo(e,i)}else ta(e)}function Lo(e,t,n){J(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:de(t)&&(e.setupState=fl(t)),ta(e)}function ta(e,t,n){const r=e.type;e.render||(e.render=r.render||rt);{const s=Vn(e);ut();try{su(e)}finally{ft(),s()}}}const Bu={get(e,t){return xe(e,"get",""),e[t]}};function Vu(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Bu),slots:e.slots,emit:e.emit,expose:t}}function Ir(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(fl(Ut(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Pn)return Pn[n](e)},has(t,n){return n in t||n in Pn}})):e.proxy}function zu(e,t=!0){return J(e)?e.displayName||e.name:e.name||t&&e.__name}function Uu(e){return J(e)&&"__vccOpts"in e}const Q=(e,t)=>jc(e,t,Dn);function z(e,t,n){const r=arguments.length;return r===2?de(t)&&!K(t)?jn(t)?Ae(e,null,[t]):Ae(e,t):Ae(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&jn(n)&&(n=[n]),Ae(e,t,n))}const Ku="3.5.14";/**
* @vue/runtime-dom v3.5.14
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let xs;const Mo=typeof window<"u"&&window.trustedTypes;if(Mo)try{xs=Mo.createPolicy("vue",{createHTML:e=>e})}catch{}const na=xs?e=>xs.createHTML(e):e=>e,Wu="http://www.w3.org/2000/svg",Gu="http://www.w3.org/1998/Math/MathML",lt=typeof document<"u"?document:null,$o=lt&&lt.createElement("template"),Qu={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const s=t==="svg"?lt.createElementNS(Wu,e):t==="mathml"?lt.createElementNS(Gu,e):n?lt.createElement(e,{is:n}):lt.createElement(e);return e==="select"&&r&&r.multiple!=null&&s.setAttribute("multiple",r.multiple),s},createText:e=>lt.createTextNode(e),createComment:e=>lt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>lt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,s,o){const i=n?n.previousSibling:t.lastChild;if(s&&(s===o||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===o||!(s=s.nextSibling)););else{$o.innerHTML=na(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const l=$o.content;if(r==="svg"||r==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},gt="transition",hn="animation",ln=Symbol("_vtc"),ra={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},sa=ge({},Sl,ra),Yu=e=>(e.displayName="Transition",e.props=sa,e),oa=Yu((e,{slots:t})=>z(Kc,ia(e),t)),kt=(e,t=[])=>{K(e)?e.forEach(n=>n(...t)):e&&e(...t)},Io=e=>e?K(e)?e.some(t=>t.length>1):e.length>1:!1;function ia(e){const t={};for(const k in e)k in ra||(t[k]=e[k]);if(e.css===!1)return t;const{name:n="v",type:r,duration:s,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:a=o,appearActiveClass:u=i,appearToClass:c=l,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:d=`${n}-leave-active`,leaveToClass:g=`${n}-leave-to`}=e,v=Ju(s),y=v&&v[0],P=v&&v[1],{onBeforeEnter:T,onEnter:I,onEnterCancelled:j,onLeave:A,onLeaveCancelled:O,onBeforeAppear:W=T,onAppear:U=I,onAppearCancelled:q=j}=t,E=(k,Z,N,ee)=>{k._enterCancelled=ee,vt(k,Z?c:l),vt(k,Z?u:i),N&&N()},H=(k,Z)=>{k._isLeaving=!1,vt(k,f),vt(k,g),vt(k,d),Z&&Z()},G=k=>(Z,N)=>{const ee=k?U:I,re=()=>E(Z,k,N);kt(ee,[Z,re]),No(()=>{vt(Z,k?a:o),et(Z,k?c:l),Io(ee)||jo(Z,r,y,re)})};return ge(t,{onBeforeEnter(k){kt(T,[k]),et(k,o),et(k,i)},onBeforeAppear(k){kt(W,[k]),et(k,a),et(k,u)},onEnter:G(!1),onAppear:G(!0),onLeave(k,Z){k._isLeaving=!0;const N=()=>H(k,Z);et(k,f),k._enterCancelled?(et(k,d),Ss()):(Ss(),et(k,d)),No(()=>{k._isLeaving&&(vt(k,f),et(k,g),Io(A)||jo(k,r,P,N))}),kt(A,[k,N])},onEnterCancelled(k){E(k,!1,void 0,!0),kt(j,[k])},onAppearCancelled(k){E(k,!0,void 0,!0),kt(q,[k])},onLeaveCancelled(k){H(k),kt(O,[k])}})}function Ju(e){if(e==null)return null;if(de(e))return[Yr(e.enter),Yr(e.leave)];{const t=Yr(e);return[t,t]}}function Yr(e){return ec(e)}function et(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[ln]||(e[ln]=new Set)).add(t)}function vt(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.remove(r));const n=e[ln];n&&(n.delete(t),n.size||(e[ln]=void 0))}function No(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Xu=0;function jo(e,t,n,r){const s=e._endId=++Xu,o=()=>{s===e._endId&&r()};if(n!=null)return setTimeout(o,n);const{type:i,timeout:l,propCount:a}=la(e,t);if(!i)return r();const u=i+"end";let c=0;const f=()=>{e.removeEventListener(u,d),o()},d=g=>{g.target===e&&++c>=a&&f()};setTimeout(()=>{c<a&&f()},l+1),e.addEventListener(u,d)}function la(e,t){const n=window.getComputedStyle(e),r=v=>(n[v]||"").split(", "),s=r(`${gt}Delay`),o=r(`${gt}Duration`),i=Do(s,o),l=r(`${hn}Delay`),a=r(`${hn}Duration`),u=Do(l,a);let c=null,f=0,d=0;t===gt?i>0&&(c=gt,f=i,d=o.length):t===hn?u>0&&(c=hn,f=u,d=a.length):(f=Math.max(i,u),c=f>0?i>u?gt:hn:null,d=c?c===gt?o.length:a.length:0);const g=c===gt&&/\b(transform|all)(,|$)/.test(r(`${gt}Property`).toString());return{type:c,timeout:f,propCount:d,hasTransform:g}}function Do(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,r)=>qo(n)+qo(e[r])))}function qo(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Ss(){return document.body.offsetHeight}function Zu(e,t,n){const r=e[ln];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const vr=Symbol("_vod"),aa=Symbol("_vsh"),xp={beforeMount(e,{value:t},{transition:n}){e[vr]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):pn(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),pn(e,!0),r.enter(e)):r.leave(e,()=>{pn(e,!1)}):pn(e,t))},beforeUnmount(e,{value:t}){pn(e,t)}};function pn(e,t){e.style.display=t?e[vr]:"none",e[aa]=!t}const ef=Symbol(""),tf=/(^|;)\s*display\s*:/;function nf(e,t,n){const r=e.style,s=he(n);let o=!1;if(n&&!s){if(t)if(he(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&rr(r,l,"")}else for(const i in t)n[i]==null&&rr(r,i,"");for(const i in n)i==="display"&&(o=!0),rr(r,i,n[i])}else if(s){if(t!==n){const i=r[ef];i&&(n+=";"+i),r.cssText=n,o=tf.test(n)}}else t&&e.removeAttribute("style");vr in e&&(e[vr]=o?r.display:"",e[aa]&&(r.display="none"))}const Fo=/\s*!important$/;function rr(e,t,n){if(K(n))n.forEach(r=>rr(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=rf(e,t);Fo.test(n)?e.setProperty(Rt(r),n.replace(Fo,""),"important"):e[r]=n}}const Ho=["Webkit","Moz","ms"],Jr={};function rf(e,t){const n=Jr[t];if(n)return n;let r=De(t);if(r!=="filter"&&r in e)return Jr[t]=r;r=Cr(r);for(let s=0;s<Ho.length;s++){const o=Ho[s]+r;if(o in e)return Jr[t]=o}return t}const Bo="http://www.w3.org/1999/xlink";function Vo(e,t,n,r,s,o=ic(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Bo,t.slice(6,t.length)):e.setAttributeNS(Bo,t,n):n==null||o&&!Bi(n)?e.removeAttribute(t):e.setAttribute(t,o?"":dt(n)?String(n):n)}function zo(e,t,n,r,s){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?na(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,a=n==null?e.type==="checkbox"?"on":"":String(n);(l!==a||!("_value"in e))&&(e.value=a),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Bi(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(s||t)}function sf(e,t,n,r){e.addEventListener(t,n,r)}function of(e,t,n,r){e.removeEventListener(t,n,r)}const Uo=Symbol("_vei");function lf(e,t,n,r,s=null){const o=e[Uo]||(e[Uo]={}),i=o[t];if(r&&i)i.value=r;else{const[l,a]=af(t);if(r){const u=o[t]=ff(r,s);sf(e,l,u,a)}else i&&(of(e,l,i,a),o[t]=void 0)}}const Ko=/(?:Once|Passive|Capture)$/;function af(e){let t;if(Ko.test(e)){t={};let r;for(;r=e.match(Ko);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Rt(e.slice(2)),t]}let Xr=0;const cf=Promise.resolve(),uf=()=>Xr||(cf.then(()=>Xr=0),Xr=Date.now());function ff(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;We(df(r,n.value),t,5,[r])};return n.value=e,n.attached=uf(),n}function df(e,t){if(K(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>s=>!s._stopped&&r&&r(s))}else return t}const Wo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,hf=(e,t,n,r,s,o)=>{const i=s==="svg";t==="class"?Zu(e,r,i):t==="style"?nf(e,n,r):xr(t)?Ns(t)||lf(e,t,n,r,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):pf(e,t,r,i))?(zo(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Vo(e,t,r,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!he(r))?zo(e,De(t),r,o,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),Vo(e,t,r,i))};function pf(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&Wo(t)&&J(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const s=e.tagName;if(s==="IMG"||s==="VIDEO"||s==="CANVAS"||s==="SOURCE")return!1}return Wo(t)&&he(n)?!1:t in e}const ca=new WeakMap,ua=new WeakMap,_r=Symbol("_moveCb"),Go=Symbol("_enterCb"),gf=e=>(delete e.props.mode,e),mf=gf({name:"TransitionGroup",props:ge({},sa,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=un(),r=xl();let s,o;return Al(()=>{if(!s.length)return;const i=e.moveClass||`${e.name||"v"}-move`;if(!wf(s[0].el,n.vnode.el,i)){s=[];return}s.forEach(_f),s.forEach(yf);const l=s.filter(bf);Ss(),l.forEach(a=>{const u=a.el,c=u.style;et(u,i),c.transform=c.webkitTransform=c.transitionDuration="";const f=u[_r]=d=>{d&&d.target!==u||(!d||/transform$/.test(d.propertyName))&&(u.removeEventListener("transitionend",f),u[_r]=null,vt(u,i))};u.addEventListener("transitionend",f)}),s=[]}),()=>{const i=ne(e),l=ia(i);let a=i.tag||$e;if(s=[],o)for(let u=0;u<o.length;u++){const c=o[u];c.el&&c.el instanceof Element&&(s.push(c),Ht(c,In(c,l,r,n)),ca.set(c,c.el.getBoundingClientRect()))}o=t.default?Qs(t.default()):[];for(let u=0;u<o.length;u++){const c=o[u];c.key!=null&&Ht(c,In(c,l,r,n))}return Ae(a,null,o)}}}),vf=mf;function _f(e){const t=e.el;t[_r]&&t[_r](),t[Go]&&t[Go]()}function yf(e){ua.set(e,e.el.getBoundingClientRect())}function bf(e){const t=ca.get(e),n=ua.get(e),r=t.left-n.left,s=t.top-n.top;if(r||s){const o=e.el.style;return o.transform=o.webkitTransform=`translate(${r}px,${s}px)`,o.transitionDuration="0s",e}}function wf(e,t,n){const r=e.cloneNode(),s=e[ln];s&&s.forEach(l=>{l.split(/\s+/).forEach(a=>a&&r.classList.remove(a))}),n.split(/\s+/).forEach(l=>l&&r.classList.add(l)),r.style.display="none";const o=t.nodeType===1?t:t.parentNode;o.appendChild(r);const{hasTransform:i}=la(r);return o.removeChild(r),i}const xf=ge({patchProp:hf},Qu);let Qo;function Sf(){return Qo||(Qo=_u(xf))}const fa=(...e)=>{const t=Sf().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=Cf(r);if(!s)return;const o=t._component;!J(o)&&!o.render&&!o.template&&(o.template=s.innerHTML),s.nodeType===1&&(s.textContent="");const i=n(s,!1,Ef(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),i},t};function Ef(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Cf(e){return he(e)?document.querySelector(e):e}function Nr(e,t,n,r){return Object.defineProperty(e,t,{get:n,set:r,enumerable:!0}),e}function Sp(e,t){for(const n in t)Nr(e,n,t[n]);return e}const Bt=Ft(!1);let Es;function Pf(e,t){const n=/(edg|edge|edga|edgios)\/([\w.]+)/.exec(e)||/(opr)[\/]([\w.]+)/.exec(e)||/(vivaldi)[\/]([\w.]+)/.exec(e)||/(chrome|crios)[\/]([\w.]+)/.exec(e)||/(version)(applewebkit)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+).*(version)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(firefox|fxios)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[\/]([\w.]+)/.exec(e)||[];return{browser:n[5]||n[3]||n[1]||"",version:n[4]||n[2]||"0",platform:t[0]||""}}function Rf(e){return/(ipad)/.exec(e)||/(ipod)/.exec(e)||/(windows phone)/.exec(e)||/(iphone)/.exec(e)||/(kindle)/.exec(e)||/(silk)/.exec(e)||/(android)/.exec(e)||/(win)/.exec(e)||/(mac)/.exec(e)||/(linux)/.exec(e)||/(cros)/.exec(e)||/(playbook)/.exec(e)||/(bb)/.exec(e)||/(blackberry)/.exec(e)||[]}const da="ontouchstart"in window||window.navigator.maxTouchPoints>0;function Tf(e){const t=e.toLowerCase(),n=Rf(t),r=Pf(t,n),s={mobile:!1,desktop:!1,cordova:!1,capacitor:!1,nativeMobile:!1,electron:!1,bex:!1,linux:!1,mac:!1,win:!1,cros:!1,chrome:!1,firefox:!1,opera:!1,safari:!1,vivaldi:!1,edge:!1,edgeChromium:!1,ie:!1,webkit:!1,android:!1,ios:!1,ipad:!1,iphone:!1,ipod:!1,kindle:!1,winphone:!1,blackberry:!1,playbook:!1,silk:!1};r.browser&&(s[r.browser]=!0,s.version=r.version,s.versionNumber=parseInt(r.version,10)),r.platform&&(s[r.platform]=!0);const o=s.android||s.ios||s.bb||s.blackberry||s.ipad||s.iphone||s.ipod||s.kindle||s.playbook||s.silk||s["windows phone"];if(o===!0||t.indexOf("mobile")!==-1?s.mobile=!0:s.desktop=!0,s["windows phone"]&&(s.winphone=!0,delete s["windows phone"]),s.edga||s.edgios||s.edg?(s.edge=!0,r.browser="edge"):s.crios?(s.chrome=!0,r.browser="chrome"):s.fxios&&(s.firefox=!0,r.browser="firefox"),(s.ipod||s.ipad||s.iphone)&&(s.ios=!0),s.vivaldi&&(r.browser="vivaldi",s.vivaldi=!0),(s.chrome||s.opr||s.safari||s.vivaldi||s.mobile===!0&&s.ios!==!0&&o!==!0)&&(s.webkit=!0),s.opr&&(r.browser="opera",s.opera=!0),s.safari&&(s.blackberry||s.bb?(r.browser="blackberry",s.blackberry=!0):s.playbook?(r.browser="playbook",s.playbook=!0):s.android?(r.browser="android",s.android=!0):s.kindle?(r.browser="kindle",s.kindle=!0):s.silk&&(r.browser="silk",s.silk=!0)),s.name=r.browser,s.platform=r.platform,t.indexOf("electron")!==-1)s.electron=!0;else if(document.location.href.indexOf("-extension://")!==-1)s.bex=!0;else{if(window.Capacitor!==void 0?(s.capacitor=!0,s.nativeMobile=!0,s.nativeMobileWrapper="capacitor"):(window._cordovaNative!==void 0||window.cordova!==void 0)&&(s.cordova=!0,s.nativeMobile=!0,s.nativeMobileWrapper="cordova"),Bt.value===!0&&(Es={is:{...s}}),da===!0&&s.mac===!0&&(s.desktop===!0&&s.safari===!0||s.nativeMobile===!0&&s.android!==!0&&s.ios!==!0&&s.ipad!==!0)){delete s.mac,delete s.desktop;const i=Math.min(window.innerHeight,window.innerWidth)>414?"ipad":"iphone";Object.assign(s,{mobile:!0,ios:!0,platform:i,[i]:!0})}s.mobile!==!0&&window.navigator.userAgentData&&window.navigator.userAgentData.mobile&&(delete s.desktop,s.mobile=!0)}return s}const Yo=navigator.userAgent||navigator.vendor||window.opera,Af={has:{touch:!1,webStorage:!1},within:{iframe:!1}},Ee={userAgent:Yo,is:Tf(Yo),has:{touch:da},within:{iframe:window.self!==window.top}},Cs={install(e){const{$q:t}=e;Bt.value===!0?(e.onSSRHydrated.push(()=>{Object.assign(t.platform,Ee),Bt.value=!1}),t.platform=zt(this)):t.platform=this}};{let e;Nr(Ee.has,"webStorage",()=>{if(e!==void 0)return e;try{if(window.localStorage)return e=!0,!0}catch{}return e=!1,!1}),Object.assign(Cs,Ee),Bt.value===!0&&(Object.assign(Cs,Es,Af),Es=null)}function zn(e){return Ut(Ys(e))}function kf(e){return Ut(e)}const Un=(e,t)=>{const n=zt(e);for(const r in e)Nr(t,r,()=>n[r],s=>{n[r]=s});return t},Le={hasPassive:!1,passiveCapture:!0,notPassiveCapture:!0};try{const e=Object.defineProperty({},"passive",{get(){Object.assign(Le,{hasPassive:!0,passive:{passive:!0},notPassive:{passive:!1},passiveCapture:{passive:!0,capture:!0},notPassiveCapture:{passive:!1,capture:!0}})}});window.addEventListener("qtest",null,e),window.removeEventListener("qtest",null,e)}catch{}function qn(){}function Ep(e){return e.button===0}function Of(e){return e.touches&&e.touches[0]?e=e.touches[0]:e.changedTouches&&e.changedTouches[0]?e=e.changedTouches[0]:e.targetTouches&&e.targetTouches[0]&&(e=e.targetTouches[0]),{top:e.clientY,left:e.clientX}}function Lf(e){if(e.path)return e.path;if(e.composedPath)return e.composedPath();const t=[];let n=e.target;for(;n;){if(t.push(n),n.tagName==="HTML")return t.push(document),t.push(window),t;n=n.parentElement}}function ha(e){e.stopPropagation()}function Ps(e){e.cancelable!==!1&&e.preventDefault()}function $t(e){e.cancelable!==!1&&e.preventDefault(),e.stopPropagation()}function Cp(e,t){if(e===void 0||t===!0&&e.__dragPrevented===!0)return;const n=t===!0?r=>{r.__dragPrevented=!0,r.addEventListener("dragstart",Ps,Le.notPassiveCapture)}:r=>{delete r.__dragPrevented,r.removeEventListener("dragstart",Ps,Le.notPassiveCapture)};e.querySelectorAll("a, img").forEach(n)}function Mf(e,t,n){const r=`__q_${t}_evt`;e[r]=e[r]!==void 0?e[r].concat(n):n,n.forEach(s=>{s[0].addEventListener(s[1],e[s[2]],Le[s[3]])})}function $f(e,t){const n=`__q_${t}_evt`;e[n]!==void 0&&(e[n].forEach(r=>{r[0].removeEventListener(r[1],e[r[2]],Le[r[3]])}),e[n]=void 0)}function If(e,t=250,n){let r=null;function s(){const o=arguments,i=()=>{r=null,e.apply(this,o)};r!==null&&clearTimeout(r),r=setTimeout(i,t)}return s.cancel=()=>{r!==null&&clearTimeout(r)},s}const Zr=["sm","md","lg","xl"],{passive:Jo}=Le,Nf=Un({width:0,height:0,name:"xs",sizes:{sm:600,md:1024,lg:1440,xl:1920},lt:{sm:!0,md:!0,lg:!0,xl:!0},gt:{xs:!1,sm:!1,md:!1,lg:!1},xs:!0,sm:!1,md:!1,lg:!1,xl:!1},{setSizes:qn,setDebounce:qn,install({$q:e,onSSRHydrated:t}){if(e.screen=this,this.__installed===!0){e.config.screen!==void 0&&(e.config.screen.bodyClasses===!1?document.body.classList.remove(`screen--${this.name}`):this.__update(!0));return}const{visualViewport:n}=window,r=n||window,s=document.scrollingElement||document.documentElement,o=n===void 0||Ee.is.mobile===!0?()=>[Math.max(window.innerWidth,s.clientWidth),Math.max(window.innerHeight,s.clientHeight)]:()=>[n.width*n.scale+window.innerWidth-s.clientWidth,n.height*n.scale+window.innerHeight-s.clientHeight],i=e.config.screen?.bodyClasses===!0;this.__update=f=>{const[d,g]=o();if(g!==this.height&&(this.height=g),d!==this.width)this.width=d;else if(f!==!0)return;let v=this.sizes;this.gt.xs=d>=v.sm,this.gt.sm=d>=v.md,this.gt.md=d>=v.lg,this.gt.lg=d>=v.xl,this.lt.sm=d<v.sm,this.lt.md=d<v.md,this.lt.lg=d<v.lg,this.lt.xl=d<v.xl,this.xs=this.lt.sm,this.sm=this.gt.xs===!0&&this.lt.md===!0,this.md=this.gt.sm===!0&&this.lt.lg===!0,this.lg=this.gt.md===!0&&this.lt.xl===!0,this.xl=this.gt.lg,v=this.xs===!0&&"xs"||this.sm===!0&&"sm"||this.md===!0&&"md"||this.lg===!0&&"lg"||"xl",v!==this.name&&(i===!0&&(document.body.classList.remove(`screen--${this.name}`),document.body.classList.add(`screen--${v}`)),this.name=v)};let l,a={},u=16;this.setSizes=f=>{Zr.forEach(d=>{f[d]!==void 0&&(a[d]=f[d])})},this.setDebounce=f=>{u=f};const c=()=>{const f=getComputedStyle(document.body);f.getPropertyValue("--q-size-sm")&&Zr.forEach(d=>{this.sizes[d]=parseInt(f.getPropertyValue(`--q-size-${d}`),10)}),this.setSizes=d=>{Zr.forEach(g=>{d[g]&&(this.sizes[g]=d[g])}),this.__update(!0)},this.setDebounce=d=>{l!==void 0&&r.removeEventListener("resize",l,Jo),l=d>0?If(this.__update,d):this.__update,r.addEventListener("resize",l,Jo)},this.setDebounce(u),Object.keys(a).length!==0?(this.setSizes(a),a=void 0):this.__update(),i===!0&&this.name==="xs"&&document.body.classList.add("screen--xs")};Bt.value===!0?t.push(c):c()}}),be=Un({isActive:!1,mode:!1},{__media:void 0,set(e){be.mode=e,e==="auto"?(be.__media===void 0&&(be.__media=window.matchMedia("(prefers-color-scheme: dark)"),be.__updateMedia=()=>{be.set("auto")},be.__media.addListener(be.__updateMedia)),e=be.__media.matches):be.__media!==void 0&&(be.__media.removeListener(be.__updateMedia),be.__media=void 0),be.isActive=e===!0,document.body.classList.remove(`body--${e===!0?"light":"dark"}`),document.body.classList.add(`body--${e===!0?"dark":"light"}`)},toggle(){be.set(be.isActive===!1)},install({$q:e,ssrContext:t}){const{dark:n}=e.config;e.dark=this,this.__installed!==!0&&this.set(n!==void 0?n:!1)}});function jf(e,t,n=document.body){if(typeof e!="string")throw new TypeError("Expected a string as propName");if(typeof t!="string")throw new TypeError("Expected a string as value");if(!(n instanceof Element))throw new TypeError("Expected a DOM element");n.style.setProperty(`--q-${e}`,t)}let pa=!1;function Df(e){pa=e.isComposing===!0}function qf(e){return pa===!0||e!==Object(e)||e.isComposing===!0||e.qKeyEvent===!0}function Rs(e,t){return qf(e)===!0?!1:[].concat(t).includes(e.keyCode)}function ga(e){if(e.ios===!0)return"ios";if(e.android===!0)return"android"}function Ff({is:e,has:t,within:n},r){const s=[e.desktop===!0?"desktop":"mobile",`${t.touch===!1?"no-":""}touch`];if(e.mobile===!0){const o=ga(e);o!==void 0&&s.push("platform-"+o)}if(e.nativeMobile===!0){const o=e.nativeMobileWrapper;s.push(o),s.push("native-mobile"),e.ios===!0&&(r[o]===void 0||r[o].iosStatusBarPadding!==!1)&&s.push("q-ios-padding")}else e.electron===!0?s.push("electron"):e.bex===!0&&s.push("bex");return n.iframe===!0&&s.push("within-iframe"),s}function Hf(){const{is:e}=Ee,t=document.body.className,n=new Set(t.replace(/ {2}/g," ").split(" "));if(e.nativeMobile!==!0&&e.electron!==!0&&e.bex!==!0){if(e.desktop===!0)n.delete("mobile"),n.delete("platform-ios"),n.delete("platform-android"),n.add("desktop");else if(e.mobile===!0){n.delete("desktop"),n.add("mobile"),n.delete("platform-ios"),n.delete("platform-android");const s=ga(e);s!==void 0&&n.add(`platform-${s}`)}}Ee.has.touch===!0&&(n.delete("no-touch"),n.add("touch")),Ee.within.iframe===!0&&n.add("within-iframe");const r=Array.from(n).join(" ");t!==r&&(document.body.className=r)}function Bf(e){for(const t in e)jf(t,e[t])}const Vf={install(e){if(this.__installed!==!0){if(Bt.value===!0)Hf();else{const{$q:t}=e;t.config.brand!==void 0&&Bf(t.config.brand);const n=Ff(Ee,t.config);document.body.classList.add.apply(document.body.classList,n)}Ee.is.ios===!0&&document.body.addEventListener("touchstart",qn),window.addEventListener("keydown",Df,!0)}}},ma=()=>!0;function zf(e){return typeof e=="string"&&e!==""&&e!=="/"&&e!=="#/"}function Uf(e){return e.startsWith("#")===!0&&(e=e.substring(1)),e.startsWith("/")===!1&&(e="/"+e),e.endsWith("/")===!0&&(e=e.substring(0,e.length-1)),"#"+e}function Kf(e){if(e.backButtonExit===!1)return()=>!1;if(e.backButtonExit==="*")return ma;const t=["#/"];return Array.isArray(e.backButtonExit)===!0&&t.push(...e.backButtonExit.filter(zf).map(Uf)),()=>t.includes(window.location.hash)}const Wf={__history:[],add:qn,remove:qn,install({$q:e}){if(this.__installed===!0)return;const{cordova:t,capacitor:n}=Ee.is;if(t!==!0&&n!==!0)return;const r=e.config[t===!0?"cordova":"capacitor"];if(r?.backButton===!1||n===!0&&(window.Capacitor===void 0||window.Capacitor.Plugins.App===void 0))return;this.add=i=>{i.condition===void 0&&(i.condition=ma),this.__history.push(i)},this.remove=i=>{const l=this.__history.indexOf(i);l>=0&&this.__history.splice(l,1)};const s=Kf(Object.assign({backButtonExit:!0},r)),o=()=>{if(this.__history.length){const i=this.__history[this.__history.length-1];i.condition()===!0&&(this.__history.pop(),i.handler())}else s()===!0?navigator.app.exitApp():window.history.back()};t===!0?document.addEventListener("deviceready",()=>{document.addEventListener("backbutton",o,!1)}):window.Capacitor.Plugins.App.addListener("backButton",o)}},Xo={isoName:"en-US",nativeName:"English (US)",label:{clear:"Clear",ok:"OK",cancel:"Cancel",close:"Close",set:"Set",select:"Select",reset:"Reset",remove:"Remove",update:"Update",create:"Create",search:"Search",filter:"Filter",refresh:"Refresh",expand:e=>e?`Expand "${e}"`:"Expand",collapse:e=>e?`Collapse "${e}"`:"Collapse"},date:{days:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),daysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),firstDayOfWeek:0,format24h:!1,pluralDay:"days",prevMonth:"Previous month",nextMonth:"Next month",prevYear:"Previous year",nextYear:"Next year",today:"Today",prevRangeYears:e=>`Previous ${e} years`,nextRangeYears:e=>`Next ${e} years`},table:{noData:"No data available",noResults:"No matching records found",loading:"Loading...",selectedRecords:e=>e===1?"1 record selected.":(e===0?"No":e)+" records selected.",recordsPerPage:"Records per page:",allRows:"All",pagination:(e,t,n)=>e+"-"+t+" of "+n,columns:"Columns"},pagination:{first:"First page",prev:"Previous page",next:"Next page",last:"Last page"},editor:{url:"URL",bold:"Bold",italic:"Italic",strikethrough:"Strikethrough",underline:"Underline",unorderedList:"Unordered List",orderedList:"Ordered List",subscript:"Subscript",superscript:"Superscript",hyperlink:"Hyperlink",toggleFullscreen:"Toggle Fullscreen",quote:"Quote",left:"Left align",center:"Center align",right:"Right align",justify:"Justify align",print:"Print",outdent:"Decrease indentation",indent:"Increase indentation",removeFormat:"Remove formatting",formatting:"Formatting",fontSize:"Font Size",align:"Align",hr:"Insert Horizontal Rule",undo:"Undo",redo:"Redo",heading1:"Heading 1",heading2:"Heading 2",heading3:"Heading 3",heading4:"Heading 4",heading5:"Heading 5",heading6:"Heading 6",paragraph:"Paragraph",code:"Code",size1:"Very small",size2:"A bit small",size3:"Normal",size4:"Medium-large",size5:"Big",size6:"Very big",size7:"Maximum",defaultFont:"Default Font",viewSource:"View Source"},tree:{noNodes:"No nodes available",noResults:"No matching nodes found"}};function Zo(){const e=Array.isArray(navigator.languages)===!0&&navigator.languages.length!==0?navigator.languages[0]:navigator.language;if(typeof e=="string")return e.split(/[-_]/).map((t,n)=>n===0?t.toLowerCase():n>1||t.length<4?t.toUpperCase():t[0].toUpperCase()+t.slice(1).toLowerCase()).join("-")}const xt=Un({__qLang:{}},{getLocale:Zo,set(e=Xo,t){const n={...e,rtl:e.rtl===!0,getLocale:Zo};{if(n.set=xt.set,xt.__langConfig===void 0||xt.__langConfig.noHtmlAttrs!==!0){const r=document.documentElement;r.setAttribute("dir",n.rtl===!0?"rtl":"ltr"),r.setAttribute("lang",n.isoName)}Object.assign(xt.__qLang,n)}},install({$q:e,lang:t,ssrContext:n}){e.lang=xt.__qLang,xt.__langConfig=e.config.lang,this.__installed===!0?t!==void 0&&this.set(t):(this.props=new Proxy(this.__qLang,{get(){return Reflect.get(...arguments)},ownKeys(r){return Reflect.ownKeys(r).filter(s=>s!=="set"&&s!=="getLocale")}}),this.set(t||Xo))}}),Gf={name:"material-icons",type:{positive:"check_circle",negative:"warning",info:"info",warning:"priority_high"},arrow:{up:"arrow_upward",right:"arrow_forward",down:"arrow_downward",left:"arrow_back",dropdown:"arrow_drop_down"},chevron:{left:"chevron_left",right:"chevron_right"},colorPicker:{spectrum:"gradient",tune:"tune",palette:"style"},pullToRefresh:{icon:"refresh"},carousel:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down",navigationIcon:"lens"},chip:{remove:"cancel",selected:"check"},datetime:{arrowLeft:"chevron_left",arrowRight:"chevron_right",now:"access_time",today:"today"},editor:{bold:"format_bold",italic:"format_italic",strikethrough:"strikethrough_s",underline:"format_underlined",unorderedList:"format_list_bulleted",orderedList:"format_list_numbered",subscript:"vertical_align_bottom",superscript:"vertical_align_top",hyperlink:"link",toggleFullscreen:"fullscreen",quote:"format_quote",left:"format_align_left",center:"format_align_center",right:"format_align_right",justify:"format_align_justify",print:"print",outdent:"format_indent_decrease",indent:"format_indent_increase",removeFormat:"format_clear",formatting:"text_format",fontSize:"format_size",align:"format_align_left",hr:"remove",undo:"undo",redo:"redo",heading:"format_size",code:"code",size:"format_size",font:"font_download",viewSource:"code"},expansionItem:{icon:"keyboard_arrow_down",denseIcon:"arrow_drop_down"},fab:{icon:"add",activeIcon:"close"},field:{clear:"cancel",error:"error"},pagination:{first:"first_page",prev:"keyboard_arrow_left",next:"keyboard_arrow_right",last:"last_page"},rating:{icon:"grade"},stepper:{done:"check",active:"edit",error:"warning"},tabs:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down"},table:{arrowUp:"arrow_upward",warning:"warning",firstPage:"first_page",prevPage:"chevron_left",nextPage:"chevron_right",lastPage:"last_page"},tree:{icon:"play_arrow"},uploader:{done:"done",clear:"clear",add:"add_box",upload:"cloud_upload",removeQueue:"clear_all",removeUploaded:"done_all"}},yr=Un({iconMapFn:null,__qIconSet:{}},{set(e,t){const n={...e};n.set=yr.set,Object.assign(yr.__qIconSet,n)},install({$q:e,iconSet:t,ssrContext:n}){e.config.iconMapFn!==void 0&&(this.iconMapFn=e.config.iconMapFn),e.iconSet=this.__qIconSet,Nr(e,"iconMapFn",()=>this.iconMapFn,r=>{this.iconMapFn=r}),this.__installed===!0?t!==void 0&&this.set(t):(this.props=new Proxy(this.__qIconSet,{get(){return Reflect.get(...arguments)},ownKeys(r){return Reflect.ownKeys(r).filter(s=>s!=="set")}}),this.set(t||Gf))}}),Qf="_q_",Pp="_q_l_",Rp="_q_pc_",Tp="_q_fo_",Ap="_q_tabs_";function kp(){}const br={};let va=!1;function Yf(){va=!0}function es(e,t){if(e===t)return!0;if(e!==null&&t!==null&&typeof e=="object"&&typeof t=="object"){if(e.constructor!==t.constructor)return!1;let n,r;if(e.constructor===Array){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(es(e[r],t[r])!==!0)return!1;return!0}if(e.constructor===Map){if(e.size!==t.size)return!1;let o=e.entries();for(r=o.next();r.done!==!0;){if(t.has(r.value[0])!==!0)return!1;r=o.next()}for(o=e.entries(),r=o.next();r.done!==!0;){if(es(r.value[1],t.get(r.value[0]))!==!0)return!1;r=o.next()}return!0}if(e.constructor===Set){if(e.size!==t.size)return!1;const o=e.entries();for(r=o.next();r.done!==!0;){if(t.has(r.value[0])!==!0)return!1;r=o.next()}return!0}if(e.buffer!=null&&e.buffer.constructor===ArrayBuffer){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(e[r]!==t[r])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();const s=Object.keys(e).filter(o=>e[o]!==void 0);if(n=s.length,n!==Object.keys(t).filter(o=>t[o]!==void 0).length)return!1;for(r=n;r--!==0;){const o=s[r];if(es(e[o],t[o])!==!0)return!1}return!0}return e!==e&&t!==t}function Vt(e){return e!==null&&typeof e=="object"&&Array.isArray(e)!==!0}function Op(e){return Object.prototype.toString.call(e)==="[object Date]"}function Lp(e){return typeof e=="number"&&isFinite(e)}const ei=[Cs,Vf,be,Nf,Wf,xt,yr];function _a(e,t){const n=fa(e);n.config.globalProperties=t.config.globalProperties;const{reload:r,...s}=t._context;return Object.assign(n._context,s),n}function ti(e,t){t.forEach(n=>{n.install(e),n.__installed=!0})}function Jf(e,t,n){e.config.globalProperties.$q=n.$q,e.provide(Qf,n.$q),ti(n,ei),t.components!==void 0&&Object.values(t.components).forEach(r=>{Vt(r)===!0&&r.name!==void 0&&e.component(r.name,r)}),t.directives!==void 0&&Object.values(t.directives).forEach(r=>{Vt(r)===!0&&r.name!==void 0&&e.directive(r.name,r)}),t.plugins!==void 0&&ti(n,Object.values(t.plugins).filter(r=>typeof r.install=="function"&&ei.includes(r)===!1)),Bt.value===!0&&(n.$q.onSSRHydrated=()=>{n.onSSRHydrated.forEach(r=>{r()}),n.$q.onSSRHydrated=()=>{}})}const Xf=function(e,t={}){const n={version:"2.18.1"};va===!1?(t.config!==void 0&&Object.assign(br,t.config),n.config={...br},Yf()):n.config=t.config||{},Jf(e,t,{parentApp:e,$q:n,lang:t.lang,iconSet:t.iconSet,onSSRHydrated:[]})},Zf={name:"Quasar",version:"2.18.1",install:Xf,lang:xt,iconSet:yr},ed={__name:"App",setup(e){return(t,n)=>{const r=nu("router-view");return pr(),gr(r)}}},ro=e=>e,Mp=ro,td=ro,nd=ro;/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let ya;const jr=e=>ya=e,ba=Symbol();function Ts(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var An;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(An||(An={}));function rd(){const e=Ki(!0),t=e.run(()=>Ft({}));let n=[],r=[];const s=Ut({install(o){jr(s),s._a=o,o.provide(ba,s),o.config.globalProperties.$pinia=s,r.forEach(i=>n.push(i)),r=[]},use(o){return this._a?n.push(o):r.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return s}const wa=()=>{};function ni(e,t,n,r=wa){e.push(t);const s=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),r())};return!n&&Wi()&&ac(s),s}function Qt(e,...t){e.slice().forEach(n=>{n(...t)})}const sd=e=>e(),ri=Symbol(),ts=Symbol();function As(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,r)=>e.set(r,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],s=e[n];Ts(s)&&Ts(r)&&e.hasOwnProperty(n)&&!pe(r)&&!Et(r)?e[n]=As(s,r):e[n]=r}return e}const od=Symbol();function id(e){return!Ts(e)||!e.hasOwnProperty(od)}const{assign:_t}=Object;function ld(e){return!!(pe(e)&&e.effect)}function ad(e,t,n,r){const{state:s,actions:o,getters:i}=t,l=n.state.value[e];let a;function u(){l||(n.state.value[e]=s?s():{});const c=Mc(n.state.value[e]);return _t(c,o,Object.keys(i||{}).reduce((f,d)=>(f[d]=Ut(Q(()=>{jr(n);const g=n._s.get(e);return i[d].call(g,g)})),f),{}))}return a=xa(e,u,t,n,r,!0),a}function xa(e,t,n={},r,s,o){let i;const l=_t({actions:{}},n),a={deep:!0};let u,c,f=[],d=[],g;const v=r.state.value[e];!o&&!v&&(r.state.value[e]={}),Ft({});let y;function P(q){let E;u=c=!1,typeof q=="function"?(q(r.state.value[e]),E={type:An.patchFunction,storeId:e,events:g}):(As(r.state.value[e],q),E={type:An.patchObject,payload:q,storeId:e,events:g});const H=y=Symbol();Ws().then(()=>{y===H&&(u=!0)}),c=!0,Qt(f,E,r.state.value[e])}const T=o?function(){const{state:E}=n,H=E?E():{};this.$patch(G=>{_t(G,H)})}:wa;function I(){i.stop(),f=[],d=[],r._s.delete(e)}const j=(q,E="")=>{if(ri in q)return q[ts]=E,q;const H=function(){jr(r);const G=Array.from(arguments),k=[],Z=[];function N(X){k.push(X)}function ee(X){Z.push(X)}Qt(d,{args:G,name:H[ts],store:O,after:N,onError:ee});let re;try{re=q.apply(this&&this.$id===e?this:O,G)}catch(X){throw Qt(Z,X),X}return re instanceof Promise?re.then(X=>(Qt(k,X),X)).catch(X=>(Qt(Z,X),Promise.reject(X))):(Qt(k,re),re)};return H[ri]=!0,H[ts]=E,H},A={_p:r,$id:e,$onAction:ni.bind(null,d),$patch:P,$reset:T,$subscribe(q,E={}){const H=ni(f,q,E.detached,()=>G()),G=i.run(()=>Rn(()=>r.state.value[e],k=>{(E.flush==="sync"?c:u)&&q({storeId:e,type:An.direct,events:g},k)},_t({},a,E)));return H},$dispose:I},O=zt(A);r._s.set(e,O);const U=(r._a&&r._a.runWithContext||sd)(()=>r._e.run(()=>(i=Ki()).run(()=>t({action:j}))));for(const q in U){const E=U[q];if(pe(E)&&!ld(E)||Et(E))o||(v&&id(E)&&(pe(E)?E.value=v[q]:As(E,v[q])),r.state.value[e][q]=E);else if(typeof E=="function"){const H=j(E,q);U[q]=H,l.actions[q]=E}}return _t(O,U),_t(ne(O),U),Object.defineProperty(O,"$state",{get:()=>r.state.value[e],set:q=>{P(E=>{_t(E,q)})}}),r._p.forEach(q=>{_t(O,i.run(()=>q({store:O,app:r._a,pinia:r,options:l})))}),v&&o&&n.hydrate&&n.hydrate(O.$state,v),u=!0,c=!0,O}/*! #__NO_SIDE_EFFECTS__ */function $p(e,t,n){let r,s;const o=typeof t=="function";r=e,s=o?n:t;function i(l,a){const u=fu();return l=l||(u?Ve(ba,null):null),l&&jr(l),l=ya,l._s.has(r)||(o?xa(r,t,s,l):ad(r,s,l)),l._s.get(r)}return i.$id=r,i}const ns=nd(()=>rd());/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const tn=typeof document<"u";function Sa(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function cd(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Sa(e.default)}const ie=Object.assign;function rs(e,t){const n={};for(const r in t){const s=t[r];n[r]=Ge(s)?s.map(e):e(s)}return n}const kn=()=>{},Ge=Array.isArray,Ea=/#/g,ud=/&/g,fd=/\//g,dd=/=/g,hd=/\?/g,Ca=/\+/g,pd=/%5B/g,gd=/%5D/g,Pa=/%5E/g,md=/%60/g,Ra=/%7B/g,vd=/%7C/g,Ta=/%7D/g,_d=/%20/g;function so(e){return encodeURI(""+e).replace(vd,"|").replace(pd,"[").replace(gd,"]")}function yd(e){return so(e).replace(Ra,"{").replace(Ta,"}").replace(Pa,"^")}function ks(e){return so(e).replace(Ca,"%2B").replace(_d,"+").replace(Ea,"%23").replace(ud,"%26").replace(md,"`").replace(Ra,"{").replace(Ta,"}").replace(Pa,"^")}function bd(e){return ks(e).replace(dd,"%3D")}function wd(e){return so(e).replace(Ea,"%23").replace(hd,"%3F")}function xd(e){return e==null?"":wd(e).replace(fd,"%2F")}function Fn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Sd=/\/$/,Ed=e=>e.replace(Sd,"");function ss(e,t,n="/"){let r,s={},o="",i="";const l=t.indexOf("#");let a=t.indexOf("?");return l<a&&l>=0&&(a=-1),a>-1&&(r=t.slice(0,a),o=t.slice(a+1,l>-1?l:t.length),s=e(o)),l>-1&&(r=r||t.slice(0,l),i=t.slice(l,t.length)),r=Td(r??t,n),{fullPath:r+(o&&"?")+o+i,path:r,query:s,hash:Fn(i)}}function Cd(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function si(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Pd(e,t,n){const r=t.matched.length-1,s=n.matched.length-1;return r>-1&&r===s&&an(t.matched[r],n.matched[s])&&Aa(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function an(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Aa(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Rd(e[n],t[n]))return!1;return!0}function Rd(e,t){return Ge(e)?oi(e,t):Ge(t)?oi(t,e):e===t}function oi(e,t){return Ge(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function Td(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),s=r[r.length-1];(s===".."||s===".")&&r.push("");let o=n.length-1,i,l;for(i=0;i<r.length;i++)if(l=r[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+r.slice(i).join("/")}const mt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Hn;(function(e){e.pop="pop",e.push="push"})(Hn||(Hn={}));var On;(function(e){e.back="back",e.forward="forward",e.unknown=""})(On||(On={}));function Ad(e){if(!e)if(tn){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Ed(e)}const kd=/^[^#]+#/;function Od(e,t){return e.replace(kd,"#")+t}function Ld(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const Dr=()=>({left:window.scrollX,top:window.scrollY});function Md(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),s=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!s)return;t=Ld(s,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function ii(e,t){return(history.state?history.state.position-t:-1)+e}const Os=new Map;function $d(e,t){Os.set(e,t)}function Id(e){const t=Os.get(e);return Os.delete(e),t}let Nd=()=>location.protocol+"//"+location.host;function ka(e,t){const{pathname:n,search:r,hash:s}=t,o=e.indexOf("#");if(o>-1){let l=s.includes(e.slice(o))?e.slice(o).length:1,a=s.slice(l);return a[0]!=="/"&&(a="/"+a),si(a,"")}return si(n,e)+r+s}function jd(e,t,n,r){let s=[],o=[],i=null;const l=({state:d})=>{const g=ka(e,location),v=n.value,y=t.value;let P=0;if(d){if(n.value=g,t.value=d,i&&i===v){i=null;return}P=y?d.position-y.position:0}else r(g);s.forEach(T=>{T(n.value,v,{delta:P,type:Hn.pop,direction:P?P>0?On.forward:On.back:On.unknown})})};function a(){i=n.value}function u(d){s.push(d);const g=()=>{const v=s.indexOf(d);v>-1&&s.splice(v,1)};return o.push(g),g}function c(){const{history:d}=window;d.state&&d.replaceState(ie({},d.state,{scroll:Dr()}),"")}function f(){for(const d of o)d();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:a,listen:u,destroy:f}}function li(e,t,n,r=!1,s=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:s?Dr():null}}function Dd(e){const{history:t,location:n}=window,r={value:ka(e,n)},s={value:t.state};s.value||o(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(a,u,c){const f=e.indexOf("#"),d=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+a:Nd()+e+a;try{t[c?"replaceState":"pushState"](u,"",d),s.value=u}catch(g){console.error(g),n[c?"replace":"assign"](d)}}function i(a,u){const c=ie({},t.state,li(s.value.back,a,s.value.forward,!0),u,{position:s.value.position});o(a,c,!0),r.value=a}function l(a,u){const c=ie({},s.value,t.state,{forward:a,scroll:Dr()});o(c.current,c,!0);const f=ie({},li(r.value,a,null),{position:c.position+1},u);o(a,f,!1),r.value=a}return{location:r,state:s,push:l,replace:i}}function qd(e){e=Ad(e);const t=Dd(e),n=jd(e,t.state,t.location,t.replace);function r(o,i=!0){i||n.pauseListeners(),history.go(o)}const s=ie({location:"",base:e,go:r,createHref:Od.bind(null,e)},t,n);return Object.defineProperty(s,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(s,"state",{enumerable:!0,get:()=>t.state.value}),s}function Fd(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),qd(e)}function Hd(e){return typeof e=="string"||e&&typeof e=="object"}function Oa(e){return typeof e=="string"||typeof e=="symbol"}const La=Symbol("");var ai;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(ai||(ai={}));function cn(e,t){return ie(new Error,{type:e,[La]:!0},t)}function it(e,t){return e instanceof Error&&La in e&&(t==null||!!(e.type&t))}const ci="[^/]+?",Bd={sensitive:!1,strict:!1,start:!0,end:!0},Vd=/[.+*?^${}()[\]/\\]/g;function zd(e,t){const n=ie({},Bd,t),r=[];let s=n.start?"^":"";const o=[];for(const u of e){const c=u.length?[]:[90];n.strict&&!u.length&&(s+="/");for(let f=0;f<u.length;f++){const d=u[f];let g=40+(n.sensitive?.25:0);if(d.type===0)f||(s+="/"),s+=d.value.replace(Vd,"\\$&"),g+=40;else if(d.type===1){const{value:v,repeatable:y,optional:P,regexp:T}=d;o.push({name:v,repeatable:y,optional:P});const I=T||ci;if(I!==ci){g+=10;try{new RegExp(`(${I})`)}catch(A){throw new Error(`Invalid custom RegExp for param "${v}" (${I}): `+A.message)}}let j=y?`((?:${I})(?:/(?:${I}))*)`:`(${I})`;f||(j=P&&u.length<2?`(?:/${j})`:"/"+j),P&&(j+="?"),s+=j,g+=20,P&&(g+=-8),y&&(g+=-20),I===".*"&&(g+=-50)}c.push(g)}r.push(c)}if(n.strict&&n.end){const u=r.length-1;r[u][r[u].length-1]+=.7000000000000001}n.strict||(s+="/?"),n.end?s+="$":n.strict&&!s.endsWith("/")&&(s+="(?:/|$)");const i=new RegExp(s,n.sensitive?"":"i");function l(u){const c=u.match(i),f={};if(!c)return null;for(let d=1;d<c.length;d++){const g=c[d]||"",v=o[d-1];f[v.name]=g&&v.repeatable?g.split("/"):g}return f}function a(u){let c="",f=!1;for(const d of e){(!f||!c.endsWith("/"))&&(c+="/"),f=!1;for(const g of d)if(g.type===0)c+=g.value;else if(g.type===1){const{value:v,repeatable:y,optional:P}=g,T=v in u?u[v]:"";if(Ge(T)&&!y)throw new Error(`Provided param "${v}" is an array but it is not repeatable (* or + modifiers)`);const I=Ge(T)?T.join("/"):T;if(!I)if(P)d.length<2&&(c.endsWith("/")?c=c.slice(0,-1):f=!0);else throw new Error(`Missing required param "${v}"`);c+=I}}return c||"/"}return{re:i,score:r,keys:o,parse:l,stringify:a}}function Ud(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Ma(e,t){let n=0;const r=e.score,s=t.score;for(;n<r.length&&n<s.length;){const o=Ud(r[n],s[n]);if(o)return o;n++}if(Math.abs(s.length-r.length)===1){if(ui(r))return 1;if(ui(s))return-1}return s.length-r.length}function ui(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Kd={type:0,value:""},Wd=/[a-zA-Z0-9_]/;function Gd(e){if(!e)return[[]];if(e==="/")return[[Kd]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(g){throw new Error(`ERR (${n})/"${u}": ${g}`)}let n=0,r=n;const s=[];let o;function i(){o&&s.push(o),o=[]}let l=0,a,u="",c="";function f(){u&&(n===0?o.push({type:0,value:u}):n===1||n===2||n===3?(o.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:u,regexp:c,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),u="")}function d(){u+=a}for(;l<e.length;){if(a=e[l++],a==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:a==="/"?(u&&f(),i()):a===":"?(f(),n=1):d();break;case 4:d(),n=r;break;case 1:a==="("?n=2:Wd.test(a)?d():(f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--);break;case 2:a===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+a:n=3:c+=a;break;case 3:f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),i(),s}function Qd(e,t,n){const r=zd(Gd(e.path),n),s=ie(r,{record:e,parent:t,children:[],alias:[]});return t&&!s.record.aliasOf==!t.record.aliasOf&&t.children.push(s),s}function Yd(e,t){const n=[],r=new Map;t=pi({strict:!1,end:!0,sensitive:!1},t);function s(f){return r.get(f)}function o(f,d,g){const v=!g,y=di(f);y.aliasOf=g&&g.record;const P=pi(t,f),T=[y];if("alias"in f){const A=typeof f.alias=="string"?[f.alias]:f.alias;for(const O of A)T.push(di(ie({},y,{components:g?g.record.components:y.components,path:O,aliasOf:g?g.record:y})))}let I,j;for(const A of T){const{path:O}=A;if(d&&O[0]!=="/"){const W=d.record.path,U=W[W.length-1]==="/"?"":"/";A.path=d.record.path+(O&&U+O)}if(I=Qd(A,d,P),g?g.alias.push(I):(j=j||I,j!==I&&j.alias.push(I),v&&f.name&&!hi(I)&&i(f.name)),$a(I)&&a(I),y.children){const W=y.children;for(let U=0;U<W.length;U++)o(W[U],I,g&&g.children[U])}g=g||I}return j?()=>{i(j)}:kn}function i(f){if(Oa(f)){const d=r.get(f);d&&(r.delete(f),n.splice(n.indexOf(d),1),d.children.forEach(i),d.alias.forEach(i))}else{const d=n.indexOf(f);d>-1&&(n.splice(d,1),f.record.name&&r.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function l(){return n}function a(f){const d=Zd(f,n);n.splice(d,0,f),f.record.name&&!hi(f)&&r.set(f.record.name,f)}function u(f,d){let g,v={},y,P;if("name"in f&&f.name){if(g=r.get(f.name),!g)throw cn(1,{location:f});P=g.record.name,v=ie(fi(d.params,g.keys.filter(j=>!j.optional).concat(g.parent?g.parent.keys.filter(j=>j.optional):[]).map(j=>j.name)),f.params&&fi(f.params,g.keys.map(j=>j.name))),y=g.stringify(v)}else if(f.path!=null)y=f.path,g=n.find(j=>j.re.test(y)),g&&(v=g.parse(y),P=g.record.name);else{if(g=d.name?r.get(d.name):n.find(j=>j.re.test(d.path)),!g)throw cn(1,{location:f,currentLocation:d});P=g.record.name,v=ie({},d.params,f.params),y=g.stringify(v)}const T=[];let I=g;for(;I;)T.unshift(I.record),I=I.parent;return{name:P,path:y,params:v,matched:T,meta:Xd(T)}}e.forEach(f=>o(f));function c(){n.length=0,r.clear()}return{addRoute:o,resolve:u,removeRoute:i,clearRoutes:c,getRoutes:l,getRecordMatcher:s}}function fi(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function di(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Jd(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Jd(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function hi(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Xd(e){return e.reduce((t,n)=>ie(t,n.meta),{})}function pi(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function Zd(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;Ma(e,t[o])<0?r=o:n=o+1}const s=eh(e);return s&&(r=t.lastIndexOf(s,r-1)),r}function eh(e){let t=e;for(;t=t.parent;)if($a(t)&&Ma(e,t)===0)return t}function $a({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function th(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let s=0;s<r.length;++s){const o=r[s].replace(Ca," "),i=o.indexOf("="),l=Fn(i<0?o:o.slice(0,i)),a=i<0?null:Fn(o.slice(i+1));if(l in t){let u=t[l];Ge(u)||(u=t[l]=[u]),u.push(a)}else t[l]=a}return t}function gi(e){let t="";for(let n in e){const r=e[n];if(n=bd(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(Ge(r)?r.map(o=>o&&ks(o)):[r&&ks(r)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function nh(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=Ge(r)?r.map(s=>s==null?null:""+s):r==null?r:""+r)}return t}const rh=Symbol(""),mi=Symbol(""),qr=Symbol(""),oo=Symbol(""),Ls=Symbol("");function gn(){let e=[];function t(r){return e.push(r),()=>{const s=e.indexOf(r);s>-1&&e.splice(s,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function St(e,t,n,r,s,o=i=>i()){const i=r&&(r.enterCallbacks[s]=r.enterCallbacks[s]||[]);return()=>new Promise((l,a)=>{const u=d=>{d===!1?a(cn(4,{from:n,to:t})):d instanceof Error?a(d):Hd(d)?a(cn(2,{from:t,to:d})):(i&&r.enterCallbacks[s]===i&&typeof d=="function"&&i.push(d),l())},c=o(()=>e.call(r&&r.instances[s],t,n,u));let f=Promise.resolve(c);e.length<3&&(f=f.then(u)),f.catch(d=>a(d))})}function os(e,t,n,r,s=o=>o()){const o=[];for(const i of e)for(const l in i.components){let a=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(Sa(a)){const c=(a.__vccOpts||a)[t];c&&o.push(St(c,n,r,i,l,s))}else{let u=a();o.push(()=>u.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const f=cd(c)?c.default:c;i.mods[l]=c,i.components[l]=f;const g=(f.__vccOpts||f)[t];return g&&St(g,n,r,i,l,s)()}))}}return o}function vi(e){const t=Ve(qr),n=Ve(oo),r=Q(()=>{const a=Dt(e.to);return t.resolve(a)}),s=Q(()=>{const{matched:a}=r.value,{length:u}=a,c=a[u-1],f=n.matched;if(!c||!f.length)return-1;const d=f.findIndex(an.bind(null,c));if(d>-1)return d;const g=_i(a[u-2]);return u>1&&_i(c)===g&&f[f.length-1].path!==g?f.findIndex(an.bind(null,a[u-2])):d}),o=Q(()=>s.value>-1&&ah(n.params,r.value.params)),i=Q(()=>s.value>-1&&s.value===n.matched.length-1&&Aa(n.params,r.value.params));function l(a={}){if(lh(a)){const u=t[Dt(e.replace)?"replace":"push"](Dt(e.to)).catch(kn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:r,href:Q(()=>r.value.href),isActive:o,isExactActive:i,navigate:l}}function sh(e){return e.length===1?e[0]:e}const oh=Ys({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:vi,setup(e,{slots:t}){const n=zt(vi(e)),{options:r}=Ve(qr),s=Q(()=>({[yi(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[yi(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&sh(t.default(n));return e.custom?o:z("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:s.value},o)}}}),ih=oh;function lh(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function ah(e,t){for(const n in t){const r=t[n],s=e[n];if(typeof r=="string"){if(r!==s)return!1}else if(!Ge(s)||s.length!==r.length||r.some((o,i)=>o!==s[i]))return!1}return!0}function _i(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const yi=(e,t,n)=>e??t??n,ch=Ys({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=Ve(Ls),s=Q(()=>e.route||r.value),o=Ve(mi,0),i=Q(()=>{let u=Dt(o);const{matched:c}=s.value;let f;for(;(f=c[u])&&!f.components;)u++;return u}),l=Q(()=>s.value.matched[i.value]);tr(mi,Q(()=>i.value+1)),tr(rh,l),tr(Ls,s);const a=Ft();return Rn(()=>[a.value,l.value,e.name],([u,c,f],[d,g,v])=>{c&&(c.instances[f]=u,g&&g!==c&&u&&u===d&&(c.leaveGuards.size||(c.leaveGuards=g.leaveGuards),c.updateGuards.size||(c.updateGuards=g.updateGuards))),u&&c&&(!g||!an(c,g)||!d)&&(c.enterCallbacks[f]||[]).forEach(y=>y(u))},{flush:"post"}),()=>{const u=s.value,c=e.name,f=l.value,d=f&&f.components[c];if(!d)return bi(n.default,{Component:d,route:u});const g=f.props[c],v=g?g===!0?u.params:typeof g=="function"?g(u):g:null,P=z(d,ie({},v,t,{onVnodeUnmounted:T=>{T.component.isUnmounted&&(f.instances[c]=null)},ref:a}));return bi(n.default,{Component:P,route:u})||P}}});function bi(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const uh=ch;function fh(e){const t=Yd(e.routes,e),n=e.parseQuery||th,r=e.stringifyQuery||gi,s=e.history,o=gn(),i=gn(),l=gn(),a=Tc(mt);let u=mt;tn&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=rs.bind(null,b=>""+b),f=rs.bind(null,xd),d=rs.bind(null,Fn);function g(b,D){let M,F;return Oa(b)?(M=t.getRecordMatcher(b),F=D):F=b,t.addRoute(F,M)}function v(b){const D=t.getRecordMatcher(b);D&&t.removeRoute(D)}function y(){return t.getRoutes().map(b=>b.record)}function P(b){return!!t.getRecordMatcher(b)}function T(b,D){if(D=ie({},D||a.value),typeof b=="string"){const m=ss(n,b,D.path),_=t.resolve({path:m.path},D),x=s.createHref(m.fullPath);return ie(m,_,{params:d(_.params),hash:Fn(m.hash),redirectedFrom:void 0,href:x})}let M;if(b.path!=null)M=ie({},b,{path:ss(n,b.path,D.path).path});else{const m=ie({},b.params);for(const _ in m)m[_]==null&&delete m[_];M=ie({},b,{params:f(m)}),D.params=f(D.params)}const F=t.resolve(M,D),ce=b.hash||"";F.params=c(d(F.params));const h=Cd(r,ie({},b,{hash:yd(ce),path:F.path})),p=s.createHref(h);return ie({fullPath:h,hash:ce,query:r===gi?nh(b.query):b.query||{}},F,{redirectedFrom:void 0,href:p})}function I(b){return typeof b=="string"?ss(n,b,a.value.path):ie({},b)}function j(b,D){if(u!==b)return cn(8,{from:D,to:b})}function A(b){return U(b)}function O(b){return A(ie(I(b),{replace:!0}))}function W(b){const D=b.matched[b.matched.length-1];if(D&&D.redirect){const{redirect:M}=D;let F=typeof M=="function"?M(b):M;return typeof F=="string"&&(F=F.includes("?")||F.includes("#")?F=I(F):{path:F},F.params={}),ie({query:b.query,hash:b.hash,params:F.path!=null?{}:b.params},F)}}function U(b,D){const M=u=T(b),F=a.value,ce=b.state,h=b.force,p=b.replace===!0,m=W(M);if(m)return U(ie(I(m),{state:typeof m=="object"?ie({},ce,m.state):ce,force:h,replace:p}),D||M);const _=M;_.redirectedFrom=D;let x;return!h&&Pd(r,F,M)&&(x=cn(16,{to:_,from:F}),Qe(F,F,!0,!1)),(x?Promise.resolve(x):H(_,F)).catch(w=>it(w)?it(w,2)?w:pt(w):oe(w,_,F)).then(w=>{if(w){if(it(w,2))return U(ie({replace:p},I(w.to),{state:typeof w.to=="object"?ie({},ce,w.to.state):ce,force:h}),D||_)}else w=k(_,F,!0,p,ce);return G(_,F,w),w})}function q(b,D){const M=j(b,D);return M?Promise.reject(M):Promise.resolve()}function E(b){const D=Wt.values().next().value;return D&&typeof D.runWithContext=="function"?D.runWithContext(b):b()}function H(b,D){let M;const[F,ce,h]=dh(b,D);M=os(F.reverse(),"beforeRouteLeave",b,D);for(const m of F)m.leaveGuards.forEach(_=>{M.push(St(_,b,D))});const p=q.bind(null,b,D);return M.push(p),qe(M).then(()=>{M=[];for(const m of o.list())M.push(St(m,b,D));return M.push(p),qe(M)}).then(()=>{M=os(ce,"beforeRouteUpdate",b,D);for(const m of ce)m.updateGuards.forEach(_=>{M.push(St(_,b,D))});return M.push(p),qe(M)}).then(()=>{M=[];for(const m of h)if(m.beforeEnter)if(Ge(m.beforeEnter))for(const _ of m.beforeEnter)M.push(St(_,b,D));else M.push(St(m.beforeEnter,b,D));return M.push(p),qe(M)}).then(()=>(b.matched.forEach(m=>m.enterCallbacks={}),M=os(h,"beforeRouteEnter",b,D,E),M.push(p),qe(M))).then(()=>{M=[];for(const m of i.list())M.push(St(m,b,D));return M.push(p),qe(M)}).catch(m=>it(m,8)?m:Promise.reject(m))}function G(b,D,M){l.list().forEach(F=>E(()=>F(b,D,M)))}function k(b,D,M,F,ce){const h=j(b,D);if(h)return h;const p=D===mt,m=tn?history.state:{};M&&(F||p?s.replace(b.fullPath,ie({scroll:p&&m&&m.scroll},ce)):s.push(b.fullPath,ce)),a.value=b,Qe(b,D,M,p),pt()}let Z;function N(){Z||(Z=s.listen((b,D,M)=>{if(!Kn.listening)return;const F=T(b),ce=W(F);if(ce){U(ie(ce,{replace:!0,force:!0}),F).catch(kn);return}u=F;const h=a.value;tn&&$d(ii(h.fullPath,M.delta),Dr()),H(F,h).catch(p=>it(p,12)?p:it(p,2)?(U(ie(I(p.to),{force:!0}),F).then(m=>{it(m,20)&&!M.delta&&M.type===Hn.pop&&s.go(-1,!1)}).catch(kn),Promise.reject()):(M.delta&&s.go(-M.delta,!1),oe(p,F,h))).then(p=>{p=p||k(F,h,!1),p&&(M.delta&&!it(p,8)?s.go(-M.delta,!1):M.type===Hn.pop&&it(p,20)&&s.go(-1,!1)),G(F,h,p)}).catch(kn)}))}let ee=gn(),re=gn(),X;function oe(b,D,M){pt(b);const F=re.list();return F.length?F.forEach(ce=>ce(b,D,M)):console.error(b),Promise.reject(b)}function st(){return X&&a.value!==mt?Promise.resolve():new Promise((b,D)=>{ee.add([b,D])})}function pt(b){return X||(X=!b,N(),ee.list().forEach(([D,M])=>b?M(b):D()),ee.reset()),b}function Qe(b,D,M,F){const{scrollBehavior:ce}=e;if(!tn||!ce)return Promise.resolve();const h=!M&&Id(ii(b.fullPath,0))||(F||!M)&&history.state&&history.state.scroll||null;return Ws().then(()=>ce(b,D,h)).then(p=>p&&Md(p)).catch(p=>oe(p,b,D))}const ke=b=>s.go(b);let Kt;const Wt=new Set,Kn={currentRoute:a,listening:!0,addRoute:g,removeRoute:v,clearRoutes:t.clearRoutes,hasRoute:P,getRoutes:y,resolve:T,options:e,push:A,replace:O,go:ke,back:()=>ke(-1),forward:()=>ke(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:re.add,isReady:st,install(b){const D=this;b.component("RouterLink",ih),b.component("RouterView",uh),b.config.globalProperties.$router=D,Object.defineProperty(b.config.globalProperties,"$route",{enumerable:!0,get:()=>Dt(a)}),tn&&!Kt&&a.value===mt&&(Kt=!0,A(s.location).catch(ce=>{}));const M={};for(const ce in mt)Object.defineProperty(M,ce,{get:()=>a.value[ce],enumerable:!0});b.provide(qr,D),b.provide(oo,al(M)),b.provide(Ls,a);const F=b.unmount;Wt.add(b),b.unmount=function(){Wt.delete(b),Wt.size<1&&(u=mt,Z&&Z(),Z=null,a.value=mt,Kt=!1,X=!1),F()}}};function qe(b){return b.reduce((D,M)=>D.then(()=>E(M)),Promise.resolve())}return Kn}function dh(e,t){const n=[],r=[],s=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(u=>an(u,l))?r.push(l):n.push(l));const a=e.matched[i];a&&(t.matched.find(u=>an(u,a))||s.push(a))}return[n,r,s]}function Ip(){return Ve(qr)}function Np(e){return Ve(oo)}//! COPYRIGHT © 2024-, Michael Alford and Pravista Inc. - All Rights Reserved
//! NOTICE: All information contained herein is, and remains the property of Michael Alford,
//! Pravista Inc., and their suppliers, if any. The intellectual and technical concepts
//! contained herein are proprietary to Michael Alford, Pravista Inc., and their suppliers
//! and are protected by trade secret or copyright law. Dissemination of this information or
//! reproduction of this material is strictly forbidden unless prior written permission is
//! obtained from Michael Alford and Pravista Inc..
//!============================================================================================
const hh=[{path:"/",component:()=>Pe(()=>import("./MainLayout-C0Qc3H1M.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21])),children:[{path:"",redirect:"/dash"},{path:"/trends",component:()=>Pe(()=>import("./TrendsPage-BJjzRfl9.js"),__vite__mapDeps([22,23,7,4,2,24,10,25,26,14,17,27,28,20,18,29,12,3,30,16,13,31]))},{path:"/setup/curves",component:()=>Pe(()=>import("./CurvesPage-DH6BRJjh.js"),__vite__mapDeps([32,8,14,4,2,23,33,18,34,12,35,36,7,5,25,37,38,11,29,19,39]))},{path:"/setup/schedules",component:()=>Pe(()=>import("./SchedulesPage-Ditq5I8c.js"),__vite__mapDeps([40,14,4,2,5,26,7,10,25,11,23,17,33,8,36,34,18,12,35,37,38,24,19,41]))},{path:"/setup/settings",component:()=>Pe(()=>import("./SettingsPage-CbZE7FuX.js"),__vite__mapDeps([42,4,2,24,10,7,25,14,30,16,23,33,8,34,18,12,35,36,5,37,38,20,13,43]))},{path:"/dash",component:()=>Pe(()=>import("./DashboardPage-DGEqqYZR.js"),__vite__mapDeps([44,24,4,2,10,7,25,23,38,45,18,12,46,47]))},{path:"/download",component:()=>Pe(()=>import("./DataPage-CWaPc1cJ.js"),__vite__mapDeps([48,4,2,6,24,10,7,25,26,14,16,15,30,23,33,8,38,18,27,28,49]))},{path:"/alarm",component:()=>Pe(()=>import("./AlarmsPage-B9f8q1vm.js"),__vite__mapDeps([50,4,2,51,5,9,24,10,7,25,15,16,23,52,45,18,12,46,38,53,19,13,54]))},{path:"/registers",component:()=>Pe(()=>import("./RegistersPage-B_NHAgbr.js"),__vite__mapDeps([55,24,4,2,10,7,25,51,5,9,15,16,23,52,45,18,12,46,38,53,13,56]))},{path:"/debug",component:()=>Pe(()=>import("./DebugPage-Cn9cP68R.js"),__vite__mapDeps([57,23,33,8,18,58]))},{path:"/placeholder",component:()=>Pe(()=>import("./PlaceholderPage-vDkLOcz1.js"),__vite__mapDeps([59,1,2,23]))},{path:"/documents",component:()=>Pe(()=>import("./DocumentsPage-CztO03un.js"),__vite__mapDeps([60,14,4,2,11,34,18,12,35,23,33,8,38,28,53,61]))},{path:"/serverstatus",component:()=>Pe(()=>import("./ServerStatusPage-IMfjPm_X.js"),__vite__mapDeps([62,51,5,4,2,9,24,10,7,25,15,16,23,33,8,45,18,12,46,38,53,27,28,63]))}]},{path:"/:catchAll(.*)*",redirect:"/placeholder"}],is=td(function(){return fh({scrollBehavior:()=>({left:0,top:0}),routes:hh,history:Fd("/")})});async function ph(e,t){const n=e(ed);n.use(Zf,t);const r=typeof ns=="function"?await ns({}):ns;n.use(r);const s=Ut(typeof is=="function"?await is({store:r}):is);return r.use(({store:o})=>{o.router=s}),{app:n,store:r,router:s}}const Ms={xs:18,sm:24,md:32,lg:38,xl:46},io={size:String};function lo(e,t=Ms){return Q(()=>e.size!==void 0?{fontSize:e.size in t?`${t[e.size]}px`:e.size}:null)}function gh(e,t){return e!==void 0&&e()||t}function jp(e,t){if(e!==void 0){const n=e();if(n!=null)return n.slice()}return t}function yn(e,t){return e!==void 0?t.concat(e()):t}function mh(e,t){return e===void 0?t:t!==void 0?t.concat(e()):e()}function Dp(e,t,n,r,s,o){t.key=r+s;const i=z(e,t,n);return s===!0?vl(i,o()):i}const wi="0 0 24 24",ls=e=>e,as=e=>`ionicons ${e}`,Ia={"mdi-":e=>`mdi ${e}`,"icon-":ls,"bt-":e=>`bt ${e}`,"eva-":e=>`eva ${e}`,"ion-md":as,"ion-ios":as,"ion-logo":as,"iconfont ":ls,"ti-":e=>`themify-icon ${e}`,"bi-":e=>`bootstrap-icons ${e}`,"i-":ls},Na={o_:"-outlined",r_:"-round",s_:"-sharp"},ja={sym_o_:"-outlined",sym_r_:"-rounded",sym_s_:"-sharp"},vh=new RegExp("^("+Object.keys(Ia).join("|")+")"),_h=new RegExp("^("+Object.keys(Na).join("|")+")"),xi=new RegExp("^("+Object.keys(ja).join("|")+")"),yh=/^[Mm]\s?[-+]?\.?\d/,bh=/^img:/,wh=/^svguse:/,xh=/^ion-/,Sh=/^(fa-(classic|sharp|solid|regular|light|brands|duotone|thin)|[lf]a[srlbdk]?) /,wr=zn({name:"QIcon",props:{...io,tag:{type:String,default:"i"},name:String,color:String,left:Boolean,right:Boolean},setup(e,{slots:t}){const{proxy:{$q:n}}=un(),r=lo(e),s=Q(()=>"q-icon"+(e.left===!0?" on-left":"")+(e.right===!0?" on-right":"")+(e.color!==void 0?` text-${e.color}`:"")),o=Q(()=>{let i,l=e.name;if(l==="none"||!l)return{none:!0};if(n.iconMapFn!==null){const c=n.iconMapFn(l);if(c!==void 0)if(c.icon!==void 0){if(l=c.icon,l==="none"||!l)return{none:!0}}else return{cls:c.cls,content:c.content!==void 0?c.content:" "}}if(yh.test(l)===!0){const[c,f=wi]=l.split("|");return{svg:!0,viewBox:f,nodes:c.split("&&").map(d=>{const[g,v,y]=d.split("@@");return z("path",{style:v,d:g,transform:y})})}}if(bh.test(l)===!0)return{img:!0,src:l.substring(4)};if(wh.test(l)===!0){const[c,f=wi]=l.split("|");return{svguse:!0,src:c.substring(7),viewBox:f}}let a=" ";const u=l.match(vh);if(u!==null)i=Ia[u[1]](l);else if(Sh.test(l)===!0)i=l;else if(xh.test(l)===!0)i=`ionicons ion-${n.platform.is.ios===!0?"ios":"md"}${l.substring(3)}`;else if(xi.test(l)===!0){i="notranslate material-symbols";const c=l.match(xi);c!==null&&(l=l.substring(6),i+=ja[c[1]]),a=l}else{i="notranslate material-icons";const c=l.match(_h);c!==null&&(l=l.substring(2),i+=Na[c[1]]),a=l}return{cls:i,content:a}});return()=>{const i={class:s.value,style:r.value,"aria-hidden":"true"};return o.value.none===!0?z(e.tag,i,gh(t.default)):o.value.img===!0?z(e.tag,i,yn(t.default,[z("img",{src:o.value.src})])):o.value.svg===!0?z(e.tag,i,yn(t.default,[z("svg",{viewBox:o.value.viewBox||"0 0 24 24"},o.value.nodes)])):o.value.svguse===!0?z(e.tag,i,yn(t.default,[z("svg",{viewBox:o.value.viewBox},[z("use",{"xlink:href":o.value.src})])])):(o.value.cls!==void 0&&(i.class+=" "+o.value.cls),z(e.tag,i,yn(t.default,[o.value.content])))}}}),Eh=zn({name:"QAvatar",props:{...io,fontSize:String,color:String,textColor:String,icon:String,square:Boolean,rounded:Boolean},setup(e,{slots:t}){const n=lo(e),r=Q(()=>"q-avatar"+(e.color?` bg-${e.color}`:"")+(e.textColor?` text-${e.textColor} q-chip--colored`:"")+(e.square===!0?" q-avatar--square":e.rounded===!0?" rounded-borders":"")),s=Q(()=>e.fontSize?{fontSize:e.fontSize}:null);return()=>{const o=e.icon!==void 0?[z(wr,{name:e.icon})]:void 0;return z("div",{class:r.value,style:n.value},[z("div",{class:"q-avatar__content row flex-center overflow-hidden",style:s.value},mh(t.default,o))])}}}),Ch={size:{type:[String,Number],default:"1em"},color:String};function Ph(e){return{cSize:Q(()=>e.size in Ms?`${Ms[e.size]}px`:e.size),classes:Q(()=>"q-spinner"+(e.color?` text-${e.color}`:""))}}const ao=zn({name:"QSpinner",props:{...Ch,thickness:{type:Number,default:5}},setup(e){const{cSize:t,classes:n}=Ph(e);return()=>z("svg",{class:n.value+" q-spinner-mat",width:t.value,height:t.value,viewBox:"25 25 50 50"},[z("circle",{class:"path",cx:"50",cy:"50",r:"20",fill:"none",stroke:"currentColor","stroke-width":e.thickness,"stroke-miterlimit":"10"})])}});function $s(e,t){const n=e.style;for(const r in t)n[r]=t[r]}function Rh(e){if(e==null)return;if(typeof e=="string")try{return document.querySelector(e)||void 0}catch{return}const t=Dt(e);if(t)return t.$el||t}function qp(e,t){if(e==null||e.contains(t)===!0)return!0;for(let n=e.nextElementSibling;n!==null;n=n.nextElementSibling)if(n.contains(t))return!0;return!1}function Th(e,t=250){let n=!1,r;return function(){return n===!1&&(n=!0,setTimeout(()=>{n=!1},t),r=e.apply(this,arguments)),r}}function Si(e,t,n,r){n.modifiers.stop===!0&&ha(e);const s=n.modifiers.color;let o=n.modifiers.center;o=o===!0||r===!0;const i=document.createElement("span"),l=document.createElement("span"),a=Of(e),{left:u,top:c,width:f,height:d}=t.getBoundingClientRect(),g=Math.sqrt(f*f+d*d),v=g/2,y=`${(f-g)/2}px`,P=o?y:`${a.left-u-v}px`,T=`${(d-g)/2}px`,I=o?T:`${a.top-c-v}px`;l.className="q-ripple__inner",$s(l,{height:`${g}px`,width:`${g}px`,transform:`translate3d(${P},${I},0) scale3d(.2,.2,1)`,opacity:0}),i.className=`q-ripple${s?" text-"+s:""}`,i.setAttribute("dir","ltr"),i.appendChild(l),t.appendChild(i);const j=()=>{i.remove(),clearTimeout(A)};n.abort.push(j);let A=setTimeout(()=>{l.classList.add("q-ripple__inner--enter"),l.style.transform=`translate3d(${y},${T},0) scale3d(1,1,1)`,l.style.opacity=.2,A=setTimeout(()=>{l.classList.remove("q-ripple__inner--enter"),l.classList.add("q-ripple__inner--leave"),l.style.opacity=0,A=setTimeout(()=>{i.remove(),n.abort.splice(n.abort.indexOf(j),1)},275)},250)},50)}function Ei(e,{modifiers:t,value:n,arg:r}){const s=Object.assign({},e.cfg.ripple,t,n);e.modifiers={early:s.early===!0,stop:s.stop===!0,center:s.center===!0,color:s.color||r,keyCodes:[].concat(s.keyCodes||13)}}const Ah=kf({name:"ripple",beforeMount(e,t){const n=t.instance.$.appContext.config.globalProperties.$q.config||{};if(n.ripple===!1)return;const r={cfg:n,enabled:t.value!==!1,modifiers:{},abort:[],start(s){r.enabled===!0&&s.qSkipRipple!==!0&&s.type===(r.modifiers.early===!0?"pointerdown":"click")&&Si(s,e,r,s.qKeyEvent===!0)},keystart:Th(s=>{r.enabled===!0&&s.qSkipRipple!==!0&&Rs(s,r.modifiers.keyCodes)===!0&&s.type===`key${r.modifiers.early===!0?"down":"up"}`&&Si(s,e,r,!0)},300)};Ei(r,t),e.__qripple=r,Mf(r,"main",[[e,"pointerdown","start","passive"],[e,"click","start","passive"],[e,"keydown","keystart","passive"],[e,"keyup","keystart","passive"]])},updated(e,t){if(t.oldValue!==t.value){const n=e.__qripple;n!==void 0&&(n.enabled=t.value!==!1,n.enabled===!0&&Object(t.value)===t.value&&Ei(n,t))}},beforeUnmount(e){const t=e.__qripple;t!==void 0&&(t.abort.forEach(n=>{n()}),$f(t,"main"),delete e._qripple)}}),Da={left:"start",center:"center",right:"end",between:"between",around:"around",evenly:"evenly",stretch:"stretch"},kh=Object.keys(Da),Oh={align:{type:String,validator:e=>kh.includes(e)}};function Lh(e){return Q(()=>{const t=e.align===void 0?e.vertical===!0?"stretch":"left":e.align;return`${e.vertical===!0?"items":"justify"}-${Da[t]}`})}function Fp(e){if(Object(e.$parent)===e.$parent)return e.$parent;let{parent:t}=e.$;for(;Object(t)===t;){if(Object(t.proxy)===t.proxy)return t.proxy;t=t.parent}}function Mh(e){return e.appContext.config.globalProperties.$router!==void 0}function Hp(e){return e.isUnmounted===!0||e.isDeactivated===!0}function Ci(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}function Pi(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function $h(e,t){for(const n in t){const r=t[n],s=e[n];if(typeof r=="string"){if(r!==s)return!1}else if(Array.isArray(s)===!1||s.length!==r.length||r.some((o,i)=>o!==s[i]))return!1}return!0}function Ri(e,t){return Array.isArray(t)===!0?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function Ih(e,t){return Array.isArray(e)===!0?Ri(e,t):Array.isArray(t)===!0?Ri(t,e):e===t}function Nh(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(Ih(e[n],t[n])===!1)return!1;return!0}const qa={to:[String,Object],replace:Boolean,href:String,target:String,disable:Boolean},Bp={...qa,exact:Boolean,activeClass:{type:String,default:"q-router-link--active"},exactActiveClass:{type:String,default:"q-router-link--exact-active"}};function jh({fallbackTag:e,useDisableForRouterLinkProps:t=!0}={}){const n=un(),{props:r,proxy:s,emit:o}=n,i=Mh(n),l=Q(()=>r.disable!==!0&&r.href!==void 0),a=Q(t===!0?()=>i===!0&&r.disable!==!0&&l.value!==!0&&r.to!==void 0&&r.to!==null&&r.to!=="":()=>i===!0&&l.value!==!0&&r.to!==void 0&&r.to!==null&&r.to!==""),u=Q(()=>a.value===!0?I(r.to):null),c=Q(()=>u.value!==null),f=Q(()=>l.value===!0||c.value===!0),d=Q(()=>r.type==="a"||f.value===!0?"a":r.tag||e||"div"),g=Q(()=>l.value===!0?{href:r.href,target:r.target}:c.value===!0?{href:u.value.href,target:r.target}:{}),v=Q(()=>{if(c.value===!1)return-1;const{matched:O}=u.value,{length:W}=O,U=O[W-1];if(U===void 0)return-1;const q=s.$route.matched;if(q.length===0)return-1;const E=q.findIndex(Pi.bind(null,U));if(E!==-1)return E;const H=Ci(O[W-2]);return W>1&&Ci(U)===H&&q[q.length-1].path!==H?q.findIndex(Pi.bind(null,O[W-2])):E}),y=Q(()=>c.value===!0&&v.value!==-1&&$h(s.$route.params,u.value.params)),P=Q(()=>y.value===!0&&v.value===s.$route.matched.length-1&&Nh(s.$route.params,u.value.params)),T=Q(()=>c.value===!0?P.value===!0?` ${r.exactActiveClass} ${r.activeClass}`:r.exact===!0?"":y.value===!0?` ${r.activeClass}`:"":"");function I(O){try{return s.$router.resolve(O)}catch{}return null}function j(O,{returnRouterError:W,to:U=r.to,replace:q=r.replace}={}){if(r.disable===!0)return O.preventDefault(),Promise.resolve(!1);if(O.metaKey||O.altKey||O.ctrlKey||O.shiftKey||O.button!==void 0&&O.button!==0||r.target==="_blank")return Promise.resolve(!1);O.preventDefault();const E=s.$router[q===!0?"replace":"push"](U);return W===!0?E:E.then(()=>{}).catch(()=>{})}function A(O){if(c.value===!0){const W=U=>j(O,U);o("click",O,W),O.defaultPrevented!==!0&&W()}else o("click",O)}return{hasRouterLink:c,hasHrefLink:l,hasLink:f,linkTag:d,resolvedLink:u,linkIsActive:y,linkIsExactActive:P,linkClass:T,linkAttrs:g,getLink:I,navigateToRouterLink:j,navigateOnClick:A}}const Ti={none:0,xs:4,sm:8,md:16,lg:24,xl:32},Dh={xs:8,sm:10,md:14,lg:20,xl:24},qh=["button","submit","reset"],Fh=/[^\s]\/[^\s]/,Hh=["flat","outline","push","unelevated"];function Bh(e,t){return e.flat===!0?"flat":e.outline===!0?"outline":e.push===!0?"push":e.unelevated===!0?"unelevated":t}const Vh={...io,...qa,type:{type:String,default:"button"},label:[Number,String],icon:String,iconRight:String,...Hh.reduce((e,t)=>(e[t]=Boolean)&&e,{}),square:Boolean,rounded:Boolean,glossy:Boolean,size:String,fab:Boolean,fabMini:Boolean,padding:String,color:String,textColor:String,noCaps:Boolean,noWrap:Boolean,dense:Boolean,tabindex:[Number,String],ripple:{type:[Boolean,Object],default:!0},align:{...Oh.align,default:"center"},stack:Boolean,stretch:Boolean,loading:{type:Boolean,default:null},disable:Boolean},zh={...Vh,round:Boolean};function Uh(e){const t=lo(e,Dh),n=Lh(e),{hasRouterLink:r,hasLink:s,linkTag:o,linkAttrs:i,navigateOnClick:l}=jh({fallbackTag:"button"}),a=Q(()=>{const P=e.fab===!1&&e.fabMini===!1?t.value:{};return e.padding!==void 0?Object.assign({},P,{padding:e.padding.split(/\s+/).map(T=>T in Ti?Ti[T]+"px":T).join(" "),minWidth:"0",minHeight:"0"}):P}),u=Q(()=>e.rounded===!0||e.fab===!0||e.fabMini===!0),c=Q(()=>e.disable!==!0&&e.loading!==!0),f=Q(()=>c.value===!0?e.tabindex||0:-1),d=Q(()=>Bh(e,"standard")),g=Q(()=>{const P={tabindex:f.value};return s.value===!0?Object.assign(P,i.value):qh.includes(e.type)===!0&&(P.type=e.type),o.value==="a"?(e.disable===!0?P["aria-disabled"]="true":P.href===void 0&&(P.role="button"),r.value!==!0&&Fh.test(e.type)===!0&&(P.type=e.type)):e.disable===!0&&(P.disabled="",P["aria-disabled"]="true"),e.loading===!0&&e.percentage!==void 0&&Object.assign(P,{role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":e.percentage}),P}),v=Q(()=>{let P;e.color!==void 0?e.flat===!0||e.outline===!0?P=`text-${e.textColor||e.color}`:P=`bg-${e.color} text-${e.textColor||"white"}`:e.textColor&&(P=`text-${e.textColor}`);const T=e.round===!0?"round":`rectangle${u.value===!0?" q-btn--rounded":e.square===!0?" q-btn--square":""}`;return`q-btn--${d.value} q-btn--${T}`+(P!==void 0?" "+P:"")+(c.value===!0?" q-btn--actionable q-focusable q-hoverable":e.disable===!0?" disabled":"")+(e.fab===!0?" q-btn--fab":e.fabMini===!0?" q-btn--fab-mini":"")+(e.noCaps===!0?" q-btn--no-uppercase":"")+(e.dense===!0?" q-btn--dense":"")+(e.stretch===!0?" no-border-radius self-stretch":"")+(e.glossy===!0?" glossy":"")+(e.square?" q-btn--square":"")}),y=Q(()=>n.value+(e.stack===!0?" column":" row")+(e.noWrap===!0?" no-wrap text-no-wrap":"")+(e.loading===!0?" q-btn__content--hidden":""));return{classes:v,style:a,innerClasses:y,attributes:g,hasLink:s,linkTag:o,navigateOnClick:l,isActionable:c}}const{passiveCapture:He}=Le;let Yt=null,Jt=null,Xt=null;const Kh=zn({name:"QBtn",props:{...zh,percentage:Number,darkPercentage:Boolean,onTouchstart:[Function,Array]},emits:["click","keydown","mousedown","keyup"],setup(e,{slots:t,emit:n}){const{proxy:r}=un(),{classes:s,style:o,innerClasses:i,attributes:l,hasLink:a,linkTag:u,navigateOnClick:c,isActionable:f}=Uh(e),d=Ft(null),g=Ft(null);let v=null,y,P=null;const T=Q(()=>e.label!==void 0&&e.label!==null&&e.label!==""),I=Q(()=>e.disable===!0||e.ripple===!1?!1:{keyCodes:a.value===!0?[13,32]:[13],...e.ripple===!0?{}:e.ripple}),j=Q(()=>({center:e.round})),A=Q(()=>{const N=Math.max(0,Math.min(100,e.percentage));return N>0?{transition:"transform 0.6s",transform:`translateX(${N-100}%)`}:{}}),O=Q(()=>{if(e.loading===!0)return{onMousedown:Z,onTouchstart:Z,onClick:Z,onKeydown:Z,onKeyup:Z};if(f.value===!0){const N={onClick:U,onKeydown:q,onMousedown:H};if(r.$q.platform.has.touch===!0){const ee=e.onTouchstart!==void 0?"":"Passive";N[`onTouchstart${ee}`]=E}return N}return{onClick:$t}}),W=Q(()=>({ref:d,class:"q-btn q-btn-item non-selectable no-outline "+s.value,style:o.value,...l.value,...O.value}));function U(N){if(d.value!==null){if(N!==void 0){if(N.defaultPrevented===!0)return;const ee=document.activeElement;if(e.type==="submit"&&ee!==document.body&&d.value.contains(ee)===!1&&ee.contains(d.value)===!1){N.qAvoidFocus!==!0&&d.value.focus();const re=()=>{document.removeEventListener("keydown",$t,!0),document.removeEventListener("keyup",re,He),d.value?.removeEventListener("blur",re,He)};document.addEventListener("keydown",$t,!0),document.addEventListener("keyup",re,He),d.value.addEventListener("blur",re,He)}}c(N)}}function q(N){d.value!==null&&(n("keydown",N),Rs(N,[13,32])===!0&&Jt!==d.value&&(Jt!==null&&k(),N.defaultPrevented!==!0&&(N.qAvoidFocus!==!0&&d.value.focus(),Jt=d.value,d.value.classList.add("q-btn--active"),document.addEventListener("keyup",G,!0),d.value.addEventListener("blur",G,He)),$t(N)))}function E(N){d.value!==null&&(n("touchstart",N),N.defaultPrevented!==!0&&(Yt!==d.value&&(Yt!==null&&k(),Yt=d.value,v=N.target,v.addEventListener("touchcancel",G,He),v.addEventListener("touchend",G,He)),y=!0,P!==null&&clearTimeout(P),P=setTimeout(()=>{P=null,y=!1},200)))}function H(N){d.value!==null&&(N.qSkipRipple=y===!0,n("mousedown",N),N.defaultPrevented!==!0&&Xt!==d.value&&(Xt!==null&&k(),Xt=d.value,d.value.classList.add("q-btn--active"),document.addEventListener("mouseup",G,He)))}function G(N){if(d.value!==null&&!(N?.type==="blur"&&document.activeElement===d.value)){if(N?.type==="keyup"){if(Jt===d.value&&Rs(N,[13,32])===!0){const ee=new MouseEvent("click",N);ee.qKeyEvent=!0,N.defaultPrevented===!0&&Ps(ee),N.cancelBubble===!0&&ha(ee),d.value.dispatchEvent(ee),$t(N),N.qKeyEvent=!0}n("keyup",N)}k()}}function k(N){const ee=g.value;N!==!0&&(Yt===d.value||Xt===d.value)&&ee!==null&&ee!==document.activeElement&&(ee.setAttribute("tabindex",-1),ee.focus()),Yt===d.value&&(v!==null&&(v.removeEventListener("touchcancel",G,He),v.removeEventListener("touchend",G,He)),Yt=v=null),Xt===d.value&&(document.removeEventListener("mouseup",G,He),Xt=null),Jt===d.value&&(document.removeEventListener("keyup",G,!0),d.value?.removeEventListener("blur",G,He),Jt=null),d.value?.classList.remove("q-btn--active")}function Z(N){$t(N),N.qSkipRipple=!0}return Xs(()=>{k(!0)}),Object.assign(r,{click:N=>{f.value===!0&&U(N)}}),()=>{let N=[];e.icon!==void 0&&N.push(z(wr,{name:e.icon,left:e.stack!==!0&&T.value===!0,role:"img"})),T.value===!0&&N.push(z("span",{class:"block"},[e.label])),N=yn(t.default,N),e.iconRight!==void 0&&e.round===!1&&N.push(z(wr,{name:e.iconRight,right:e.stack!==!0&&T.value===!0,role:"img"}));const ee=[z("span",{class:"q-focus-helper",ref:g})];return e.loading===!0&&e.percentage!==void 0&&ee.push(z("span",{class:"q-btn__progress absolute-full overflow-hidden"+(e.darkPercentage===!0?" q-btn__progress--dark":"")},[z("span",{class:"q-btn__progress-indicator fit block",style:A.value})])),ee.push(z("span",{class:"q-btn__content text-center col items-center q-anchor--skip "+i.value},N)),e.loading!==null&&ee.push(z(oa,{name:"q-transition--fade"},()=>e.loading===!0?[z("span",{key:"loading",class:"absolute-full flex flex-center"},t.loading!==void 0?t.loading():[z(ao)])]:null)),vl(z(u.value,W.value,ee),[[Ah,I.value,void 0,j.value]])}}});let Wh=1,Gh=document.body;function Fa(e,t){const n=document.createElement("div");if(n.id=t!==void 0?`q-portal--${t}--${Wh++}`:e,br.globalNodes!==void 0){const r=br.globalNodes.class;r!==void 0&&(n.className=r)}return Gh.appendChild(n),n}function Qh(e){e.remove()}let Yh=0;const sr={},or={},Ue={},Ha={},Jh=/^\s*$/,Ba=[],Xh=[void 0,null,!0,!1,""],co=["top-left","top-right","bottom-left","bottom-right","top","bottom","left","right","center"],Zh=["top-left","top-right","bottom-left","bottom-right"],bn={positive:{icon:e=>e.iconSet.type.positive,color:"positive"},negative:{icon:e=>e.iconSet.type.negative,color:"negative"},warning:{icon:e=>e.iconSet.type.warning,color:"warning",textColor:"dark"},info:{icon:e=>e.iconSet.type.info,color:"info"},ongoing:{group:!1,timeout:0,spinner:!0,color:"grey-8"}};function Va(e,t,n){if(!e)return mn("parameter required");let r;const s={textColor:"white"};if(e.ignoreDefaults!==!0&&Object.assign(s,sr),Vt(e)===!1&&(s.type&&Object.assign(s,bn[s.type]),e={message:e}),Object.assign(s,bn[e.type||s.type],e),typeof s.icon=="function"&&(s.icon=s.icon(t)),s.spinner?(s.spinner===!0&&(s.spinner=ao),s.spinner=Ut(s.spinner)):s.spinner=!1,s.meta={hasMedia:!!(s.spinner!==!1||s.icon||s.avatar),hasText:Ai(s.message)||Ai(s.caption)},s.position){if(co.includes(s.position)===!1)return mn("wrong position",e)}else s.position="bottom";if(Xh.includes(s.timeout)===!0)s.timeout=5e3;else{const a=Number(s.timeout);if(isNaN(a)||a<0)return mn("wrong timeout",e);s.timeout=Number.isFinite(a)?a:0}s.timeout===0?s.progress=!1:s.progress===!0&&(s.meta.progressClass="q-notification__progress"+(s.progressClass?` ${s.progressClass}`:""),s.meta.progressStyle={animationDuration:`${s.timeout+1e3}ms`});const o=(Array.isArray(e.actions)===!0?e.actions:[]).concat(e.ignoreDefaults!==!0&&Array.isArray(sr.actions)===!0?sr.actions:[]).concat(Array.isArray(bn[e.type]?.actions)===!0?bn[e.type].actions:[]),{closeBtn:i}=s;if(i&&o.push({label:typeof i=="string"?i:t.lang.label.close}),s.actions=o.map(({handler:a,noDismiss:u,...c})=>({flat:!0,...c,onClick:typeof a=="function"?()=>{a(),u!==!0&&l()}:()=>{l()}})),s.multiLine===void 0&&(s.multiLine=s.actions.length>1),Object.assign(s.meta,{class:`q-notification row items-stretch q-notification--${s.multiLine===!0?"multi-line":"standard"}`+(s.color!==void 0?` bg-${s.color}`:"")+(s.textColor!==void 0?` text-${s.textColor}`:"")+(s.classes!==void 0?` ${s.classes}`:""),wrapperClass:"q-notification__wrapper col relative-position border-radius-inherit "+(s.multiLine===!0?"column no-wrap justify-center":"row items-center"),contentClass:"q-notification__content row items-center"+(s.multiLine===!0?"":" col"),leftClass:s.meta.hasText===!0?"additional":"single",attrs:{role:"alert",...s.attrs}}),s.group===!1?(s.group=void 0,s.meta.group=void 0):((s.group===void 0||s.group===!0)&&(s.group=[s.message,s.caption,s.multiline].concat(s.actions.map(a=>`${a.label}*${a.icon}`)).join("|")),s.meta.group=s.group+"|"+s.position),s.actions.length===0?s.actions=void 0:s.meta.actionsClass="q-notification__actions row items-center "+(s.multiLine===!0?"justify-end":"col-auto")+(s.meta.hasMedia===!0?" q-notification__actions--with-media":""),n!==void 0){n.notif.meta.timer&&(clearTimeout(n.notif.meta.timer),n.notif.meta.timer=void 0),s.meta.uid=n.notif.meta.uid;const a=Ue[s.position].value.indexOf(n.notif);Ue[s.position].value[a]=s}else{const a=or[s.meta.group];if(a===void 0){if(s.meta.uid=Yh++,s.meta.badge=1,["left","right","center"].indexOf(s.position)!==-1)Ue[s.position].value.splice(Math.floor(Ue[s.position].value.length/2),0,s);else{const u=s.position.indexOf("top")!==-1?"unshift":"push";Ue[s.position].value[u](s)}s.group!==void 0&&(or[s.meta.group]=s)}else{if(a.meta.timer&&(clearTimeout(a.meta.timer),a.meta.timer=void 0),s.badgePosition!==void 0){if(Zh.includes(s.badgePosition)===!1)return mn("wrong badgePosition",e)}else s.badgePosition=`top-${s.position.indexOf("left")!==-1?"right":"left"}`;s.meta.uid=a.meta.uid,s.meta.badge=a.meta.badge+1,s.meta.badgeClass=`q-notification__badge q-notification__badge--${s.badgePosition}`+(s.badgeColor!==void 0?` bg-${s.badgeColor}`:"")+(s.badgeTextColor!==void 0?` text-${s.badgeTextColor}`:"")+(s.badgeClass?` ${s.badgeClass}`:"");const u=Ue[s.position].value.indexOf(a);Ue[s.position].value[u]=or[s.meta.group]=s}}const l=()=>{ep(s),r=void 0};if(s.timeout>0&&(s.meta.timer=setTimeout(()=>{s.meta.timer=void 0,l()},s.timeout+1e3)),s.group!==void 0)return a=>{a!==void 0?mn("trying to update a grouped one which is forbidden",e):l()};if(r={dismiss:l,config:e,notif:s},n!==void 0){Object.assign(n,r);return}return a=>{if(r!==void 0)if(a===void 0)r.dismiss();else{const u=Object.assign({},r.config,a,{group:!1,position:s.position});Va(u,t,r)}}}function ep(e){e.meta.timer&&(clearTimeout(e.meta.timer),e.meta.timer=void 0);const t=Ue[e.position].value.indexOf(e);if(t!==-1){e.group!==void 0&&delete or[e.meta.group];const n=Ba[""+e.meta.uid];if(n){const{width:r,height:s}=getComputedStyle(n);n.style.left=`${n.offsetLeft}px`,n.style.width=r,n.style.height=s}Ue[e.position].value.splice(t,1),typeof e.onDismiss=="function"&&e.onDismiss()}}function Ai(e){return e!=null&&Jh.test(e)!==!0}function mn(e,t){return console.error(`Notify: ${e}`,t),!1}function tp(){return zn({name:"QNotifications",devtools:{hide:!0},setup(){return()=>z("div",{class:"q-notifications"},co.map(e=>z(vf,{key:e,class:Ha[e],tag:"div",name:`q-notification--${e}`},()=>Ue[e].value.map(t=>{const n=t.meta,r=[];if(n.hasMedia===!0&&(t.spinner!==!1?r.push(z(t.spinner,{class:"q-notification__spinner q-notification__spinner--"+n.leftClass,color:t.spinnerColor,size:t.spinnerSize})):t.icon?r.push(z(wr,{class:"q-notification__icon q-notification__icon--"+n.leftClass,name:t.icon,color:t.iconColor,size:t.iconSize,role:"img"})):t.avatar&&r.push(z(Eh,{class:"q-notification__avatar q-notification__avatar--"+n.leftClass},()=>z("img",{src:t.avatar,"aria-hidden":"true"})))),n.hasText===!0){let o;const i={class:"q-notification__message col"};if(t.html===!0)i.innerHTML=t.caption?`<div>${t.message}</div><div class="q-notification__caption">${t.caption}</div>`:t.message;else{const l=[t.message];o=t.caption?[z("div",l),z("div",{class:"q-notification__caption"},[t.caption])]:l}r.push(z("div",i,o))}const s=[z("div",{class:n.contentClass},r)];return t.progress===!0&&s.push(z("div",{key:`${n.uid}|p|${n.badge}`,class:n.progressClass,style:n.progressStyle})),t.actions!==void 0&&s.push(z("div",{class:n.actionsClass},t.actions.map(o=>z(Kh,o)))),n.badge>1&&s.push(z("div",{key:`${n.uid}|${n.badge}`,class:t.meta.badgeClass,style:t.badgeStyle},[n.badge])),z("div",{ref:o=>{Ba[""+n.uid]=o},key:n.uid,class:n.class,...n.attrs},[z("div",{class:n.wrapperClass},s)])}))))}})}const np={setDefaults(e){Vt(e)===!0&&Object.assign(sr,e)},registerType(e,t){Vt(t)===!0&&(bn[e]=t)},install({$q:e,parentApp:t}){if(e.notify=this.create=n=>Va(n,e),e.notify.setDefaults=this.setDefaults,e.notify.registerType=this.registerType,e.config.notify!==void 0&&this.setDefaults(e.config.notify),this.__installed!==!0){co.forEach(r=>{Ue[r]=Ft([]);const s=["left","center","right"].includes(r)===!0?"center":r.indexOf("top")!==-1?"top":"bottom",o=r.indexOf("left")!==-1?"start":r.indexOf("right")!==-1?"end":"center",i=["left","right"].includes(r)?`items-${r==="left"?"start":"end"} justify-center`:r==="center"?"flex-center":`items-${o}`;Ha[r]=`q-notifications__list q-notifications__list--${s} fixed column no-wrap ${i}`});const n=Fa("q-notify");_a(tp(),t).mount(n)}}},Vp=[Element,String],rp=[null,document,document.body,document.scrollingElement,document.documentElement];function zp(e,t){let n=Rh(t);if(n===void 0){if(e==null)return window;n=e.closest(".scroll,.scroll-y,.overflow-auto")}return rp.includes(n)?window:n}function sp(e){return e===window?window.pageYOffset||window.scrollY||document.body.scrollTop||0:e.scrollTop}function op(e){return e===window?window.pageXOffset||window.scrollX||document.body.scrollLeft||0:e.scrollLeft}let Xn;function Up(){if(Xn!==void 0)return Xn;const e=document.createElement("p"),t=document.createElement("div");$s(e,{width:"100%",height:"200px"}),$s(t,{position:"absolute",top:"0px",left:"0px",visibility:"hidden",width:"200px",height:"150px",overflow:"hidden"}),t.appendChild(e),document.body.appendChild(t);const n=e.offsetWidth;t.style.overflow="scroll";let r=e.offsetWidth;return n===r&&(r=t.clientWidth),t.remove(),Xn=n-r,Xn}function ip(e,t=!0){return!e||e.nodeType!==Node.ELEMENT_NODE?!1:t?e.scrollHeight>e.clientHeight&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-y"])):e.scrollWidth>e.clientWidth&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-x"]))}let vn=0,cs,us,wn,fs=!1,ki,Oi,Li,Ot=null;function lp(e){ap(e)&&$t(e)}function ap(e){if(e.target===document.body||e.target.classList.contains("q-layout__backdrop"))return!0;const t=Lf(e),n=e.shiftKey&&!e.deltaX,r=!n&&Math.abs(e.deltaX)<=Math.abs(e.deltaY),s=n||r?e.deltaY:e.deltaX;for(let o=0;o<t.length;o++){const i=t[o];if(ip(i,r))return r?s<0&&i.scrollTop===0?!0:s>0&&i.scrollTop+i.clientHeight===i.scrollHeight:s<0&&i.scrollLeft===0?!0:s>0&&i.scrollLeft+i.clientWidth===i.scrollWidth}return!0}function Mi(e){e.target===document&&(document.scrollingElement.scrollTop=document.scrollingElement.scrollTop)}function Zn(e){fs!==!0&&(fs=!0,requestAnimationFrame(()=>{fs=!1;const{height:t}=e.target,{clientHeight:n,scrollTop:r}=document.scrollingElement;(wn===void 0||t!==window.innerHeight)&&(wn=n-t,document.scrollingElement.scrollTop=r),r>wn&&(document.scrollingElement.scrollTop-=Math.ceil((r-wn)/8))}))}function $i(e){const t=document.body,n=window.visualViewport!==void 0;if(e==="add"){const{overflowY:r,overflowX:s}=window.getComputedStyle(t);cs=op(window),us=sp(window),ki=t.style.left,Oi=t.style.top,Li=window.location.href,t.style.left=`-${cs}px`,t.style.top=`-${us}px`,s!=="hidden"&&(s==="scroll"||t.scrollWidth>window.innerWidth)&&t.classList.add("q-body--force-scrollbar-x"),r!=="hidden"&&(r==="scroll"||t.scrollHeight>window.innerHeight)&&t.classList.add("q-body--force-scrollbar-y"),t.classList.add("q-body--prevent-scroll"),document.qScrollPrevented=!0,Ee.is.ios===!0&&(n===!0?(window.scrollTo(0,0),window.visualViewport.addEventListener("resize",Zn,Le.passiveCapture),window.visualViewport.addEventListener("scroll",Zn,Le.passiveCapture),window.scrollTo(0,0)):window.addEventListener("scroll",Mi,Le.passiveCapture))}Ee.is.desktop===!0&&Ee.is.mac===!0&&window[`${e}EventListener`]("wheel",lp,Le.notPassive),e==="remove"&&(Ee.is.ios===!0&&(n===!0?(window.visualViewport.removeEventListener("resize",Zn,Le.passiveCapture),window.visualViewport.removeEventListener("scroll",Zn,Le.passiveCapture)):window.removeEventListener("scroll",Mi,Le.passiveCapture)),t.classList.remove("q-body--prevent-scroll"),t.classList.remove("q-body--force-scrollbar-x"),t.classList.remove("q-body--force-scrollbar-y"),document.qScrollPrevented=!1,t.style.left=ki,t.style.top=Oi,window.location.href===Li&&window.scrollTo(cs,us),wn=void 0)}function Ii(e){let t="add";if(e===!0){if(vn++,Ot!==null){clearTimeout(Ot),Ot=null;return}if(vn>1)return}else{if(vn===0||(vn--,vn>0))return;if(t="remove",Ee.is.ios===!0&&Ee.is.nativeMobile===!0){Ot!==null&&clearTimeout(Ot),Ot=setTimeout(()=>{$i(t),Ot=null},100);return}}$i(t)}let Zt,ds,Ni=0,Lt=null,ve={},Nt={};const za={group:"__default_quasar_group__",delay:0,message:!1,html:!1,spinnerSize:80,spinnerColor:"",messageColor:"",backgroundColor:"",boxClass:"",spinner:ao,customClass:""},Ua={...za};function cp(e){if(e?.group!==void 0&&Nt[e.group]!==void 0)return Object.assign(Nt[e.group],e);const t=Vt(e)===!0&&e.ignoreDefaults===!0?{...za,...e}:{...Ua,...e};return Nt[t.group]=t,t}const ze=Un({isActive:!1},{show(e){ve=cp(e);const{group:t}=ve;return ze.isActive=!0,Zt!==void 0?(ve.uid=Ni,ds.$forceUpdate()):(ve.uid=++Ni,Lt!==null&&clearTimeout(Lt),Lt=setTimeout(()=>{Lt=null;const n=Fa("q-loading");Zt=_a({name:"QLoading",setup(){Js(()=>{Ii(!0)});function r(){ze.isActive!==!0&&Zt!==void 0&&(Ii(!1),Zt.unmount(n),Qh(n),Zt=void 0,ds=void 0)}function s(){if(ze.isActive!==!0)return null;const o=[z(ve.spinner,{class:"q-loading__spinner",color:ve.spinnerColor,size:ve.spinnerSize})];return ve.message&&o.push(z("div",{class:"q-loading__message"+(ve.messageColor?` text-${ve.messageColor}`:""),[ve.html===!0?"innerHTML":"textContent"]:ve.message})),z("div",{class:"q-loading fullscreen flex flex-center z-max "+ve.customClass.trim(),key:ve.uid},[z("div",{class:"q-loading__backdrop"+(ve.backgroundColor?` bg-${ve.backgroundColor}`:"")}),z("div",{class:"q-loading__box column items-center "+ve.boxClass},o)])}return()=>z(oa,{name:"q-transition--fade",appear:!0,onAfterLeave:r},s)}},ze.__parentApp),ds=Zt.mount(n)},ve.delay)),n=>{if(n===void 0||Object(n)!==n){ze.hide(t);return}ze.show({...n,group:t})}},hide(e){if(ze.isActive===!0){if(e===void 0)Nt={};else{if(Nt[e]===void 0)return;{delete Nt[e];const t=Object.keys(Nt);if(t.length!==0){const n=t[t.length-1];ze.show({group:n});return}}}Lt!==null&&(clearTimeout(Lt),Lt=null),ze.isActive=!1}},setDefaults(e){Vt(e)===!0&&Object.assign(Ua,e)},install({$q:e,parentApp:t}){e.loading=this,ze.__parentApp=t,e.config.loading!==void 0&&this.setDefaults(e.config.loading)}}),up={config:{dark:!0},plugins:{Notify:np,Loading:ze}},fp="/";async function dp({app:e,router:t,store:n},r){let s=!1;const o=a=>{try{return t.resolve(a).href}catch{}return Object(a)===a?null:a},i=a=>{if(s=!0,typeof a=="string"&&/^https?:\/\//.test(a)){window.location.href=a;return}const u=o(a);u!==null&&(window.location.href=u,window.location.reload())},l=window.location.href.replace(window.location.origin,"");for(let a=0;s===!1&&a<r.length;a++)try{await r[a]({app:e,router:t,store:n,ssrContext:null,redirect:i,urlPath:l,publicPath:fp})}catch(u){if(u&&u.url){i(u.url);return}console.error("[Quasar] boot error:",u);return}s!==!0&&(e.use(t),e.mount("#q-app"))}ph(fa,up).then(e=>{const[t,n]=Promise.allSettled!==void 0?["allSettled",r=>r.map(s=>{if(s.status==="rejected"){console.error("[Quasar] boot error:",s.reason);return}return s.value.default})]:["all",r=>r.map(s=>s.default)];return Promise[t]([Pe(()=>import("./Login-BVtZ5S7B.js").then(r=>r.d),[]),Pe(()=>import("./notify-EMz9UkKM.js"),[])]).then(r=>{const s=n(r).filter(o=>typeof o=="function");dp(e,s)})});export{_p as $,zt as A,kl as B,yn as C,gr as D,wp as E,pr as F,Vc as G,Ae as H,Iu as I,lc as J,yp as K,Yc as L,Nu as M,np as N,Kh as O,Oh as P,wr as Q,Lh as R,Dt as S,bp as T,$e as U,Eh as V,nu as W,Zl as X,gp as Y,Np as Z,Ip as _,Q as a,vp as a0,mp as a1,Fs as a2,qs as a3,ao as a4,Lp as a5,Vt as a6,Of as a7,$t as a8,Hp as a9,Ep as aA,Tp as aB,xp as aC,Mh as aD,Wf as aE,Op as aF,Sp as aG,If as aH,es as aI,Jc as aJ,Al as aK,Ii as aL,mh as aM,qp as aN,Bp as aO,jh as aP,Cs as aQ,Fp as aR,hp as aS,Fa as aT,Qh as aU,Mp as aV,ze as aW,oa as aa,kf as ab,Rs as ac,$p as ad,Ys as ae,Mc as af,pe as ag,pp as ah,Ap as ai,Ah as aj,qf as ak,Gc as al,Wc as am,io as an,lo as ao,ne as ap,xt as aq,Xo as ar,Nr as as,Qf as at,$f as au,Ee as av,Cp as aw,Mf as ax,Ps as ay,ha as az,gh as b,zn as c,jp as d,kp as e,Js as f,un as g,z as h,Ve as i,vl as j,Dp as k,Pp as l,Rp as m,Ws as n,Xs as o,tr as p,Le as q,Ft as r,Vp as s,zp as t,qn as u,sp as v,Rn as w,op as x,Up as y,Bt as z};
