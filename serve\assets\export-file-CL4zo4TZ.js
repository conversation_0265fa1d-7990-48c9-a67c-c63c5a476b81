import{T as p,F as c,X as f,a1 as g,D as y,E as h,I as b,Q as S,J as T}from"./index-CzmOWWdj.js";const _={class:"text-h1 row q-col-gutter-sm"},w={class:"col-auto"},x={__name:"PageHeadingRow",props:{title:{type:String,required:!0},icon:{type:String,default:null}},setup(t){const e=t;return(o,r)=>(c(),p("div",_,[f("div",w,[e.icon?(c(),y(S,{key:0,name:e.icon,class:"q-mr-sm"},null,8,["name"])):h("",!0),b(T(e.title),1)]),g(o.$slots,"default")]))}};function M(t){return k(t*1e3)}function k(t){const e=new Date(t),o=e.getHours(),r=e.getMinutes(),a=e.getSeconds();return o.toString().padStart(2,"0")+":"+r.toString().padStart(2,"0")+":"+a.toString().padStart(2,"0")}function B(t){const e=Math.floor(t/86400);if(e>0&&t===e*3600*24)return e+" Days";const o=Math.floor(t/3600);if(o>0&&t===o*3600)return o+" Hours";const r=Math.floor(t/60);return r>0&&t===r*60?r+" Min.":t+" Sec."}function d(t){setTimeout(()=>{window.URL.revokeObjectURL(t.href)},1e4),t.remove()}function D(t,e,o={}){const{mimeType:r,byteOrderMark:a,encoding:s}=typeof o=="string"?{mimeType:o}:o,i=s!==void 0?new TextEncoder(s).encode([e]):e,u=a!==void 0?[a,i]:[i],l=new Blob(u,{type:r||"application/octet-stream"}),n=document.createElement("a");n.href=window.URL.createObjectURL(l),n.setAttribute("download",t),typeof n.download>"u"&&n.setAttribute("target","_blank"),n.classList.add("hidden"),n.style.position="fixed",document.body.appendChild(n);try{return n.click(),d(n),!0}catch(m){return d(n),m}}export{x as _,B as a,D as e,k as m,M as s};
