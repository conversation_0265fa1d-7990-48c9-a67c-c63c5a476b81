const fs = require('fs');
const { startupLogger, systemLogger, alertLogger, logFormat } = require('./src/logger.js');

console.log('=== Testing Complete Logger Functionality ===\n');

// Clean up any existing test files
const testFiles = fs.readdirSync('./logs').filter(f => f.includes('test-complete'));
testFiles.forEach(file => {
    fs.unlinkSync(`./logs/${file}`);
    console.log(`Removed ${file}`);
});

// Test 1: Basic logging functionality
console.log('1. Testing basic logging...');
startupLogger.log('System initialization started');
startupLogger.warn('Configuration warning detected');
startupLogger.error(new Error('Test error for logging'));

console.log('Memory logs count:', startupLogger.getLogs().length);
console.log('Sample memory log:', startupLogger.getLogs()[0]);

// Test 2: File rotation with custom logger
console.log('\n2. Testing file rotation...');

const { loggerConstructor } = require('./src/logger.js');
const testLogger = loggerConstructor({
    size: 50,
    filename: 'test-complete',
    filesize: 800, // Small size for testing rotation
    persist: true, // Test persist functionality
    storeOnly: ['info'],
    verboseStack: false,
    enableConsole: false, // Disable console for cleaner output
});

// Initialize to test persist functionality
testLogger.init();
console.log('Initial memory logs after init:', testLogger.getLogs().length);

// Generate logs to trigger rotation
console.log('Generating logs to trigger rotation...');
for(let i = 0; i < 20; i++) {
    testLogger.log(`Test message ${i}: This is a longer message to help trigger file rotation when size limit is reached. Message number ${i}.`);
}

// Wait for file operations to complete
setTimeout(() => {
    console.log('\n3. Checking rotation results...');
    
    const files = fs.readdirSync('./logs').filter(f => f.includes('test-complete'));
    console.log('Files created:', files);
    
    // Check file contents and sizes
    files.forEach(file => {
        const stats = fs.statSync(`./logs/${file}`);
        const content = fs.readFileSync(`./logs/${file}`, 'utf8');
        const lines = content.split('\n').filter(line => line.trim());
        console.log(`${file}: ${stats.size} bytes, ${lines.length} log entries`);
        
        // Show first few lines
        if (lines.length > 0) {
            console.log(`  First entry: ${lines[0].substring(0, 100)}...`);
            if (lines.length > 1) {
                console.log(`  Last entry: ${lines[lines.length-1].substring(0, 100)}...`);
            }
        }
    });
    
    // Test 4: Persist functionality
    console.log('\n4. Testing persist functionality...');
    
    const persistLogger = loggerConstructor({
        size: 50,
        filename: 'test-complete',
        filesize: 800,
        persist: true, // This should load existing logs
        storeOnly: ['info'],
        verboseStack: false,
        enableConsole: false,
    });
    
    persistLogger.init();
    const loadedLogs = persistLogger.getLogs();
    console.log(`Loaded ${loadedLogs.length} logs from files into memory`);
    
    if (loadedLogs.length > 0) {
        console.log('First loaded log:', loadedLogs[0]);
        console.log('Last loaded log:', loadedLogs[loadedLogs.length - 1]);
    }
    
    // Test 5: Verify maxFiles limit
    console.log('\n5. Testing maxFiles limit...');
    
    // Generate more logs to test if more than 2 files are created
    for(let i = 20; i < 40; i++) {
        testLogger.log(`Additional test message ${i}: More content to trigger additional rotations and verify maxFiles limit works correctly.`);
    }
    
    setTimeout(() => {
        const finalFiles = fs.readdirSync('./logs').filter(f => f.includes('test-complete'));
        console.log('Final files after additional logging:', finalFiles);
        console.log(`Total files: ${finalFiles.length} (should be <= 2)`);
        
        if (finalFiles.length > 2) {
            console.log('❌ ERROR: More than 2 files created! maxFiles not working correctly.');
        } else {
            console.log('✅ SUCCESS: maxFiles limit respected.');
        }
        
        console.log('\n=== Test Complete ===');
    }, 2000);
    
}, 2000);
