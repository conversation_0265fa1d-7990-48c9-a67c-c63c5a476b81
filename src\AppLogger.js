const { Logger } = require('./Logger.js');
const { sseBroadcast } = require('./routes/sse.js');

// loggers
const startupLogger = new Logger({
    size: 100,
    filename: 'startup',
    filesize: 3 * 1024,
    persist: true,
    storeOnly: ['error', 'warn', 'info'],
    verboseStack: true,
    enableConsole: true,
});

const systemLogger = new Logger({
    size: 100,
    filename: 'system',
    filesize: 10 * 1024 * 1024,
    persist: true,
    storeOnly: ['error', 'warn', 'info'],
    verboseStack: true,
    enableConsole: true,
    callback: (logEntry) => {
        try {
            sseBroadcast(logEntry, 'system');
            // console.log('System log broadcasted')
        } catch (error) {
            // console.error('Failed to broadcast system log:', error);
        }
    }
});

const alertLogger = new Logger({
    size: 100,
    filename: 'alerts',
    filesize: 10 * 1024 * 1024,
    persist: true,
    storeOnly: ['error', 'warn', 'info'],
    verboseStack: false,
    enableConsole: true,
    callback: (logEntry) => {
        try {
            sseBroadcast(logEntry, 'alerts');
            // console.log('Alert log broadcasted')
        } catch (error) {
            // console.error('Failed to broadcast alert log:', error);
        }
    }
});

class SwitchLogger extends Logger {
    constructor() {
        super({
            size: 0,
            enableConsole: false,
            persist: false
        });
        this.isInitialized = false;
    }

    get currentLogger() {
        return this.isInitialized ? systemLogger : startupLogger;
    }

    // Switch to system logger
    switchToSystemLogger() {
        this.isInitialized = true;
    }

    // Switch back to startup logger
    switchToStartupLogger() {
        this.isInitialized = false;
    }

    // Override the core logging method
    _log(level, msg, data) {
        this.currentLogger._log(level, msg, data);
    }

    // Override other methods that need special handling
    getLogs(limit, offset) {
        return this.currentLogger.getLogs(limit, offset);
    }
}

const logger = new SwitchLogger();

module.exports = {
    startupLogger,
    systemLogger,
    alertLogger,
    logger,
};

// logger.log('something in the way, startup log')
// logger.switchToSystemLogger()
// logger.log('something in the way, system log')

// test case

// startupLogger.init();
// console.log("before logging stuff", startupLogger.getLogs(limit = 10, offset = 0));
// console.log("length before", startupLogger.memoryArray.getLength());


// startupLogger.log('some other text', { a: 1, b: 2, c: 3 });
// startupLogger.warn('warning message with object', { a: 1, b: 2, c: 3 });
// const err = new Error("error message from error object");
// startupLogger.error('test error', err);
// startupLogger.error(err, { a: 1, b: 2, c: 3 });

// const object = { a: 9000, b: 2000, c: 3000 };
// startupLogger.log("some message"+" append text " + object.a + object.b + " more text");
// const err = new Error('Test error');
// startupLogger.error(err, { a: 1, b: 2, c: 3 });

// console.log("after logging stuff",startupLogger.getLogs());