import{a as I}from"./Login-BVtZ5S7B.js";import{A as E,a as h,c as P,r as q,al as R,am as D,f as T,g as Q,h as A,b as j,a9 as z,a8 as F,n as K,p as X,aB as G,i as H,e as w,l as L,D as B,F as N,G as m,j as M,H as S,X as U,E as Y,O as C,I as V,aC as W,aa as Z}from"./index-CzmOWWdj.js";import{n as tt,Q as J}from"./use-key-composition-CoMUTTxZ.js";import{_ as et}from"./_plugin-vue_export-helper-DlAUqK2U.js";//!============================================================================================
//! COPYRIGHT © 2024-, <PERSON> and Pravista Inc. - All Rights Reserved
//! NOTICE: All information contained herein is, and remains the property of <PERSON>,
//! Pravista Inc., and their suppliers, if any. The intellectual and technical concepts
//! contained herein are proprietary to Michael <PERSON>ford, Pravista Inc., and their suppliers
//! and are protected by trade secret or copyright law. Dissemination of this information or
//! reproduction of this material is strictly forbidden unless prior written permission is
//! obtained from <PERSON> Al<PERSON> and Pravista Inc.
//!============================================================================================
function dt(r,n){this.newObj=r||{},this.value=E([]),this.errors=E([]),this.srcString=JSON.stringify(this.value),this.deleteFilter=null,this.duplicateFunction=null,n?this.duplicateError=h(()=>{let t=!1;try{if(this.value.length>0){const e=[];this.value.some(s=>e.includes(s[n])?(t=s[n],!0):(e.push(s[n]),t))}}catch{}return t}):this.duplicateError=h(()=>null),this.write=function(t){console.log("reactiveApiArray.write",t),Array.isArray(t)?(this.writeValue(t),this.errors.length=0):typeof t=="object"&&(t.data&&(this.writeValue(t.data),this.errors.length=0),this.writeErrors(t.errors))},this.writeValue=function(t){const e=t!==void 0?t:this.value;this.value.length=0,this.value.push(...e),this.srcString=JSON.stringify(this.value)},this.writeErrors=function(t){this.errors.length=0,Array.isArray(t)&&t.length&&this.errors.push(...t)},this.reset=function(){this.write(JSON.parse(this.srcString)),this.errors.length=0},this.changed=h(()=>this.srcString!==JSON.stringify(this.value)),this.move=function(t,e){const s=this.value[t];if(this.value.splice(t,1),this.value.splice(e,0,s),this.errors.length){const l=this.errors[t];this.errors.splice(t,1),this.errors.splice(e,0,l)}},this.dragEnd=function(t){this.move(t.oldIndex,t.newIndex)},this.add=function(t,e){this.value.splice(t,0,JSON.parse(JSON.stringify(e||this.newObj))),this.errors.length&&this.errors.splice(t,0,{})},this.duplicate=function(t){const e=JSON.parse(JSON.stringify(this.value[t])),s=typeof this.duplicateFunction=="function"?this.duplicateFunction(e):e;if(console.log("reactiveApiArray.duplicate",s),this.value.splice(t+1,0,s),this.errors.length){const l=JSON.parse(JSON.stringify(this.errors[t]));this.errors.splice(t+1,0,l)}},this.action=function(t,e,s){switch(console.log("reactiveApiArray.action",t,e,s),e){case"add":case"add0":this.add(t,s);break;case"add1":this.add(t+1,s);break;case"duplicate":this.duplicate(t);break;case"delete":this.delete(t);break;default:console.log("reactiveApiArray.action",e,"not implemented");break}},this.delete=function(t){typeof this.deleteFilter=="function"&&this.deleteFilter(this.value[t])||(this.value.splice(t,1),this.errors.length&&this.errors.splice(t,1))},this.getError=function(t,e){return this.errors[t]&&this.errors[t][e]?this.errors[t][e]:""},this.isError=function(t,e){return!!(this.errors[t]&&this.errors[t][e])},this.api=function(){return I(...arguments).then(t=>(this.write(t.data),t))},this.get=function(t,e){const s=Object.assign({url:t,method:"get"},typeof e=="object"?e:{});return this.api(s)},this.post=function(t,e){const s=Object.assign({url:t,method:"post",data:{data:this.value}},typeof e=="object"?e:{});return this.api(s)}}const ht=P({name:"QForm",props:{autofocus:Boolean,noErrorFocus:Boolean,noResetFocus:Boolean,greedy:Boolean,onSubmit:Function},emits:["reset","validationSuccess","validationError"],setup(r,{slots:n,emit:t}){const e=Q(),s=q(null);let l=0;const u=[];function g(o){const c=typeof o=="boolean"?o:r.noErrorFocus!==!0,f=++l,_=(a,d)=>{t(`validation${a===!0?"Success":"Error"}`,d)},O=a=>{const d=a.validate();return typeof d.then=="function"?d.then(v=>({valid:v,comp:a}),v=>({valid:!1,comp:a,err:v})):Promise.resolve({valid:d,comp:a})};return(r.greedy===!0?Promise.all(u.map(O)).then(a=>a.filter(d=>d.valid!==!0)):u.reduce((a,d)=>a.then(()=>O(d).then(v=>{if(v.valid===!1)return Promise.reject(v)})),Promise.resolve()).catch(a=>[a])).then(a=>{if(a===void 0||a.length===0)return f===l&&_(!0),!0;if(f===l){const{comp:d,err:v}=a[0];if(v!==void 0&&console.error(v),_(!1,d),c===!0){const k=a.find(({comp:$})=>typeof $.focus=="function"&&z($.$)===!1);k!==void 0&&k.comp.focus()}}return!1})}function y(){l++,u.forEach(o=>{typeof o.resetValidation=="function"&&o.resetValidation()})}function b(o){o!==void 0&&F(o);const c=l+1;g().then(f=>{c===l&&f===!0&&(r.onSubmit!==void 0?t("submit",o):o?.target!==void 0&&typeof o.target.submit=="function"&&o.target.submit())})}function x(o){o!==void 0&&F(o),t("reset"),K(()=>{y(),r.autofocus===!0&&r.noResetFocus!==!0&&i()})}function i(){tt(()=>{if(s.value===null)return;(s.value.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||s.value.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||s.value.querySelector("[autofocus], [data-autofocus]")||Array.prototype.find.call(s.value.querySelectorAll("[tabindex]"),c=>c.tabIndex!==-1))?.focus({preventScroll:!0})})}X(G,{bindComponent(o){u.push(o)},unbindComponent(o){const c=u.indexOf(o);c!==-1&&u.splice(c,1)}});let p=!1;return R(()=>{p=!0}),D(()=>{p===!0&&r.autofocus===!0&&i()}),T(()=>{r.autofocus===!0&&i()}),Object.assign(e.proxy,{validate:g,resetValidation:y,submit:b,reset:x,focus:i,getValidationComponents:()=>u}),()=>A("form",{class:"q-form",ref:s,onSubmit:b,onReset:x},j(n.default))}}),st={position:{type:String,default:"bottom-right",validator:r=>["top-right","top-left","bottom-right","bottom-left","top","right","bottom","left"].includes(r)},offset:{type:Array,validator:r=>r.length===2},expand:Boolean};function ot(){const{props:r,proxy:{$q:n}}=Q(),t=H(L,w);if(t===w)return console.error("QPageSticky needs to be child of QLayout"),w;const e=h(()=>{const i=r.position;return{top:i.indexOf("top")!==-1,right:i.indexOf("right")!==-1,bottom:i.indexOf("bottom")!==-1,left:i.indexOf("left")!==-1,vertical:i==="top"||i==="bottom",horizontal:i==="left"||i==="right"}}),s=h(()=>t.header.offset),l=h(()=>t.right.offset),u=h(()=>t.footer.offset),g=h(()=>t.left.offset),y=h(()=>{let i=0,p=0;const o=e.value,c=n.lang.rtl===!0?-1:1;o.top===!0&&s.value!==0?p=`${s.value}px`:o.bottom===!0&&u.value!==0&&(p=`${-u.value}px`),o.left===!0&&g.value!==0?i=`${c*g.value}px`:o.right===!0&&l.value!==0&&(i=`${-c*l.value}px`);const f={transform:`translate(${i}, ${p})`};return r.offset&&(f.margin=`${r.offset[1]}px ${r.offset[0]}px`),o.vertical===!0?(g.value!==0&&(f[n.lang.rtl===!0?"right":"left"]=`${g.value}px`),l.value!==0&&(f[n.lang.rtl===!0?"left":"right"]=`${l.value}px`)):o.horizontal===!0&&(s.value!==0&&(f.top=`${s.value}px`),u.value!==0&&(f.bottom=`${u.value}px`)),f}),b=h(()=>`q-page-sticky row flex-center fixed-${r.position} q-page-sticky--${r.expand===!0?"expand":"shrink"}`);function x(i){const p=j(i.default);return A("div",{class:b.value,style:y.value},r.expand===!0?p:[A("div",p)])}return{$layout:t,getStickyContent:x}}const rt=P({name:"QPageSticky",props:st,setup(r,{slots:n}){const{getStickyContent:t}=ot();return()=>t(n)}}),it={class:"z-index-above-all save-btn-div"},at={__name:"SaveButton",props:{show:{type:Boolean,required:!0},reset:{type:Boolean,default:!1}},setup(r){const n=r;return(t,e)=>(N(),B(Z,{appear:"","enter-active-class":"animated slideInUp","leave-active-class":"animated slideOutDown"},{default:m(()=>[M(S(rt,{position:"bottom-right",offset:[18,18]},{default:m(()=>[U("div",it,[n.reset?(N(),B(C,{key:0,fab:"",icon:"undo",color:"grey-7",type:"reset",class:"q-mr-md"},{default:m(()=>[S(J,{anchor:"center left",self:"center right",class:"text-body2"},{default:m(()=>e[0]||(e[0]=[V(" Reset changes. ")])),_:1,__:[0]})]),_:1})):Y("",!0),S(C,{class:"pulsing-glow",fab:"",icon:"save_alt",color:"primary",type:"submit"},{default:m(()=>[S(J,{anchor:"center left",self:"center right",class:"bg-warning text-black text-body2"},{default:m(()=>e[1]||(e[1]=[V(" Save the changes! ")])),_:1,__:[1]})]),_:1})])]),_:1},512),[[W,n.show]])]),_:1}))}},pt=et(at,[["__scopeId","data-v-a4701ecc"]]);export{ht as Q,pt as S,dt as r};
