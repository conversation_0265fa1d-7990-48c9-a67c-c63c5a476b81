//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// NodeApp.js
//
// The top level file to import into NodeRed
//=============================================================================================
console.log('========== NodeApp.js ==============')

const fs = require('fs')
const Project          = require('./Project.js')
const { clearObject, startAlignedInterval, arrayToObject } = require('./Util.js')

const { init: IoServersInit   } = require('./IoServersInit.js')
const { init: IoRegistersInit } = require('./IoRegistersInit.js')
const IoService        = require('./IoService.js')
const HttpServer       = require('./HttpServer.js')
const System           = require('./System.js')
const SystemSettings   = require('./SystemSettings.js')
const Datalog          = require('./Datalog.js')
const CurvesInit       = require('./CurvesInit.js')
const ProjectConstants = require('./ProjectConstants.js')
const Schedules        = require('./Schedules.js')
const DocsInit         = require('./DocsInit.js')
const Alarms           = require('./Alarms.js')
const DataPoster       = require('./data_poster/data_poster.js')


const { sseBroadcast } = require('./routes/sse.js')

const {
	ProjectDefinitions,

	IoServersArray,
	IoServersArrayDev,

	RegistersList,
	// RegistersPersist,

	// Dashboards,
	// UnitsConversions,
	Trends,
	DatalogList,
	// CurvesFile,

	Poll: NarrativePoll, // This will be called every 1 second by the system
	Init: NarrativeInit, // This will be called once at the beginning of the application
	AlarmsList,
} = Project



const Global = require('./Global.js')
const {
	init: globalInit,
	registers,
	devicesList,
	ioServers,
	datalogSettings,
	projectSettings,
	systemSettings,
	timestamp,
	trends,
} = Global

// system loggers
const {logger} = require('./AppLogger.js')
let startupFatalError = false

function parseCommandLineSwitches() {
	// Get all command line arguments (skip first two which are node and script path)
	return process.argv.slice(2)
		.filter(arg => arg.startsWith('-'))
		.map(switchArg => switchArg.toLowerCase());
}

//-------------------------------------------------------------------------------------------
// Command line switches
//-------------------------------------------------------------------------------------------
const cmdSwitches = parseCommandLineSwitches()
const NOIO = cmdSwitches.includes('-noio')

let SystemTimerClear = null
const SystemTimerInterval = 1000
let systemTimerCallback = null
let systemTimerCounter = 0

const 	RegistersListClean = []

/**
 * Parses command line arguments, extracts switches (options starting with -),
 * and converts them to lowercase
 * @returns {string[]} Array of switch strings in lowercase
 */

/**
 * Main Initialization for the Node application to be used inside the NodeRed application
 * @function init
 * @description Initializes the external NodeRed application component
 * @returns {void}
 */
function init()
{
	// startup error state
	startupFatalError = false
	logger.switchToStartupLogger()

	// if(SystemTimerClear) {
	// 	SystemTimerClear()
	// 	SystemTimerClear = null
	// }

	// Make sure the system directory exists for User login
	try {
		logger.log('Initializing system directory and system settings')
		System.init()
		SystemSettings.init()
	} catch (err) {
		logger.error('Failed to initialize system directory and settings', err)
		startupFatalError = true
	}
	// System.init()
	// SystemSettings.init() // Load the system settings from the project directory
	// console.log('+++++>>> System.secrets.jwtSecret:', System.secrets.jwtSecret)
	// process.exit(0)

	//-----------------------------------------------------------
	// Clear system timer
	//-----------------------------------------------------------
	SystemTimerClear?.()
	SystemTimerClear = null

	systemTimerCounter = 0
	HttpServer.close()

	//-----------------------------------------------------------
	// Load All Elements of `Global`
	//-----------------------------------------------------------

	if (!startupFatalError) {
		try {
			logger.log('Initializing global components and project elements')
			globalInit()
			trends.length = 0
			trends.push(...Trends)
			ProjectConstants.init()
			Schedules.init()
			DocsInit.init()

			logger.log('Configuring IO servers')
			const myIoServersArray = process.env.PRAEVISTA_DEBUG ? IoServersArrayDev : IoServersArray
			const tmpIoServers = arrayToObject(myIoServersArray, 'name')
			clearObject(ioServers)
			Object.assign(ioServers, tmpIoServers)

			CurvesInit()

		} catch (err) {
			logger.error('Failed to initialize global components', err)
			startupFatalError = true
		}
	}


	
	//-----------------------------------------------------------
	// registers
	//-----------------------------------------------------------
	if (!startupFatalError) {
		try {
			logger.log('Initializing IO registers')
			clearObject(registers)
			const { registersObject, registersArray } = IoRegistersInit(RegistersList, ioServers, projectSettings)
			Object.assign(registers, registersObject)
			RegistersListClean.length = 0
			RegistersListClean.push(...registersArray)
			devicesList.length = 0
			devicesList.push(...Object.keys(registers))

			//-----------------------------------------------------------
			// ioServers - Global version used in realtime
			// CHANGE: close any outstanding connections
			//-----------------------------------------------------------
			// To set process.env.PRAEVISTA_DEBUG, edit `/lib/systemd/system/nodered.service`
			// Add: Environment="PRAEVISTA_DEBUG=1"
			// IoServersInit is only called after the registers are initialized
			logger.log('Initializing IO servers ')
			const myIoServers = IoServersInit(ioServers, RegistersListClean)

		} catch (err) {
			logger.error('Failed to initialize IO registers', err)
			startupFatalError = true
		}
	}




	//-----------------------------------------------------------
	// HTTP Server
	//-----------------------------------------------------------
	try {
		logger.log('Starting HTTP server')
		HttpServer.init()	// http failure : not fatal 
	} catch (err) {
		logger.error('Failed to start HTTP server', err)
	}
	

	//-----------------------------------------------------------
	// Datalog
	//-----------------------------------------------------------
	if (!startupFatalError) {
		try {
			logger.log('Initializing Datalog')
			clearObject(datalogSettings)
			Object.assign(datalogSettings, Datalog.init(ProjectDefinitions, DatalogList, registers))
			// Restore the persisted reggisters
			Datalog.restorePersist(datalogSettings.paths.persist, registers)
			// Update Metadata
			fs.writeFileSync(datalogSettings.paths.metadata, JSON.stringify(datalogSettings.metadata, null, '\t'))
			fs.writeFileSync(datalogSettings.paths.metacsv, Datalog.metadataToCsv(datalogSettings.metadata))
		} catch (err) {
			logger.error('Failed to initialize Datalog', err)
			startupFatalError = true
		}
	}
	

	//-----------------------------------------------------------
	// Data Poster - Initialize with required configuration
	//-----------------------------------------------------------
	if (!startupFatalError) {
		try {
			logger.log('Initializing Data Poster')
			const dataPosterConfig = {
				fine: datalogSettings.paths.fine,
				metadata: datalogSettings.paths.metadata,
				serverUrl: systemSettings.cloudUrl,
				enable: systemSettings.cloudEnable,
			}
			DataPoster.init(dataPosterConfig) // non fatal
		} catch (err) {
			logger.error('Failed to initialize Data Poster', err)
		}
	}

	//-----------------------------------------------------------
	// Init the Alarms List from the Project Directory
	//-----------------------------------------------------------
	if (!startupFatalError) {
		try {
			logger.log('Initializing Alarms system')
			Alarms.init(AlarmsList, ProjectDefinitions.alarmSettingsFile)
		} catch (err) {
			logger.error('Failed to initialize Alarms system', err)
			startupFatalError = true
		}
	}

	//-----------------------------------------------------------
	// Narrative from the Project Directory
	//-----------------------------------------------------------
	if (!startupFatalError) {
		try {
			logger.log('Initializing Narrative system')
			NarrativeInit(Global)
		} catch (err) {
			logger.error('Failed to initialize Narrative system', err)
			startupFatalError = true
		}
	}

	//-----------------------------------------------------------
	// System Timer
	//-----------------------------------------------------------
	// console.log('NodeRedApp.js:init()', registersNew)

	

	SystemTimerClear = startupFatalError ? null : startAlignedInterval(() => {
		const ms = Date.now()
		timestamp.ms = ms
		timestamp.s = Math.floor(ms / 1000)

		
		
		if (!startupFatalError) {
			//-------------------------------------------------------
			// START of Main Polling Loop
			//-------------------------------------------------------
			try {
				Schedules.svc() // Poll the Project Schedules

				NarrativePoll(Global)  // Poll the Project Narrative

				Alarms.svc() // Poll the Project Alarms

				if(!NOIO) {
					IoService.poll(ioServers, registers, timestamp.s) // Poll the Modbus devices
				}
			} catch (err) {
				logger.error('Error in main polling loop', err)
			}

			// The IoService.poll() should be finished by now
			setTimeout(function() {
				try {
					const datalog = Datalog.svc(timestamp, datalogSettings) // Log the data

					if(datalog) // Something just got written to the datalog
					{
						// Call the cloud data pusher
						// datalog is on Object with the following properties:
						// datalog.data - A Javascript array of the data to be logged
						// datalog.string - A string of the data exactly as written to the datalog file
						// datalog.coarse - A flag indicating it is a coarse log interval
						if (systemSettings.cloudEnable && systemSettings.cloudUrl){
							// console.log(`[SVC CALLED] +++++++++++++++++++++++++++++++++++++`)
							DataPoster.svc({
							enable: systemSettings.cloudEnable,
							serverUrl: systemSettings.cloudUrl
						}, datalog.data)
						}
					}
				} catch (err) {
					logger.error('Error in datalog service', err)
				}

				sseBroadcast({         // Broadcast Server Sent Events
					t: timestamp.s,
					count: systemTimerCounter,
					status: startupFatalError ? 'fatal-error' : 'normal'
				}, 'poll')
			}, 500)
		} else {
			sseBroadcast({         // Broadcast Server Sent Events
				t: timestamp.s,
				count: systemTimerCounter,
				status: 'fatal-error'
			}, 'poll')
		}

		//-------------------------------------------------------
		// END of Main Polling Loop
		//-------------------------------------------------------

		// If a callback has been registred, call it
		systemTimerCallback?.({timestamp, count: systemTimerCounter})
		systemTimerCounter++
	}, SystemTimerInterval);
	logger.switchToSystemLogger() 
}


/**
 * Sets a callback function to be executed on each system timer tick
 * @function setSystemTimerCallback
 * @param {Function} callback - The callback function to be called on each timer tick
 * @param {Object} callback.params - The parameters passed to the callback
 * @param {Object} callback.params.timestamp - The current timestamp object
 * @param {number} callback.params.count - The current timer counter value
 * @returns {void}
 */
function setSystemTimerCallback(callback) {
	systemTimerCallback = callback
}



// process.on('SIGINT', function() {
// 	socket.close()
// 	process.exit()
// })

async function gracefulShutdown()
{
	logger.switchToSystemLogger()
	logger.log('Received kill signal, shutting down gracefully')

	systemTimerCallback = null
	SystemTimerClear?.()
	SystemTimerClear = null

	logger.log('System Timer closed')


	try {
		await IoService.close(ioServers)
		logger.log('IoService closed')
	} catch (err) {
		logger.error('gracefulShutdown IoService.close Error', err)
	}

	// Save persisted registers
	Datalog.savePersist(datalogSettings)

	try {
		await DataPoster.shutdown()
		logger.log('DataPoster shutdown complete')
	} catch (err) {
		logger.error('gracefulShutdown DataPoster.shutdown Error', err)
	}

	try {
		await HttpServer.close()
		logger.log('HttpServer closed')
	} catch (err) {
		logger.error('gracefulShutdown HttpServer.close Error', err)
	}


	process.exit(0)


	// return HttpServer.close().then(() => {
	// 	console.log('HttpServer closed')
	// 	return IoService.close(ioServers)
	// }).then(() => {
	// 	console.log('IoService closed')
	// 	process.exit(0)
	// }).catch(err => {
	// 	console.error('gracefulShutdown Error', err)
	// 	process.exit(1)
	// })




}

process.on('SIGTERM', gracefulShutdown)
process.on('SIGINT', gracefulShutdown)



module.exports = {
	init,
	// ioPoll,
	ioServers,
	registers,
	RegistersList,
	RegistersListClean,
	projectSettings,     // Defined in /public/project-settings.json
	timestamp,
	env: process.env,
	// systemTimerCallback,
	setSystemTimerCallback, // setSystemTimerCallback(ioServers)
}

