import{c as y,a as c,h as v,b as A,aO as F,g as O,aP as Q,r as k,ac as q,a8 as R,d as $,av as g}from"./index-CzmOWWdj.js";import{a as j,c as D}from"./use-key-composition-CoMUTTxZ.js";const W=y({name:"QItemSection",props:{avatar:<PERSON><PERSON><PERSON>,thumbnail:<PERSON><PERSON><PERSON>,side:<PERSON><PERSON><PERSON>,top:<PERSON>olean,noWrap:<PERSON>olean},setup(e,{slots:t}){const a=c(()=>`q-item__section column q-item__section--${e.avatar===!0||e.side===!0||e.thumbnail===!0?"side":"main"}`+(e.top===!0?" q-item__section--top justify-start":" justify-center")+(e.avatar===!0?" q-item__section--avatar":"")+(e.thumbnail===!0?" q-item__section--thumbnail":"")+(e.noWrap===!0?" q-item__section--nowrap":""));return()=>v("div",{class:a.value},A(t.default))}}),U=y({name:"QItem",props:{...j,...F,tag:{type:String,default:"div"},active:{type:Boolean,default:null},clickable:Boolean,dense:Boolean,insetLevel:Number,tabindex:[String,Number],focused:Boolean,manualFocus:Boolean},emits:["click","keyup"],setup(e,{slots:t,emit:a}){const{proxy:{$q:u}}=O(),i=D(e,u),{hasLink:m,linkAttrs:_,linkClass:x,linkTag:B,navigateOnClick:C}=Q(),d=k(null),f=k(null),b=c(()=>e.clickable===!0||m.value===!0||e.tag==="label"),r=c(()=>e.disable!==!0&&b.value===!0),L=c(()=>"q-item q-item-type row no-wrap"+(e.dense===!0?" q-item--dense":"")+(i.value===!0?" q-item--dark":"")+(m.value===!0&&e.active===null?x.value:e.active===!0?` q-item--active${e.activeClass!==void 0?` ${e.activeClass}`:""}`:"")+(e.disable===!0?" disabled":"")+(r.value===!0?" q-item--clickable q-link cursor-pointer "+(e.manualFocus===!0?"q-manual-focusable":"q-focusable q-hoverable")+(e.focused===!0?" q-manual-focusable--focused":""):"")),K=c(()=>e.insetLevel===void 0?null:{["padding"+(u.lang.rtl===!0?"Right":"Left")]:16+e.insetLevel*56+"px"});function p(n){r.value===!0&&(f.value!==null&&n.qAvoidFocus!==!0&&(n.qKeyEvent!==!0&&document.activeElement===d.value?f.value.focus():document.activeElement===f.value&&d.value.focus()),C(n))}function I(n){if(r.value===!0&&q(n,[13,32])===!0){R(n),n.qKeyEvent=!0;const h=new MouseEvent("click",n);h.qKeyEvent=!0,d.value.dispatchEvent(h)}a("keyup",n)}function S(){const n=$(t.default,[]);return r.value===!0&&n.unshift(v("div",{class:"q-focus-helper",tabindex:-1,ref:f})),n}return()=>{const n={ref:d,class:L.value,style:K.value,role:"listitem",onClick:p,onKeyup:I};return r.value===!0?(n.tabindex=e.tabindex||"0",Object.assign(n,_.value)):b.value===!0&&(n["aria-disabled"]="true"),v(B.value,n,S())}}});function G(e,t,a){return a<=t?t:Math.min(a,Math.max(t,e))}function H(e,t,a){if(a<=t)return t;const u=a-t+1;let i=t+(e-t)%u;return i<t&&(i=u+i),i===0?0:i}function J(e,t=2,a="0"){if(e==null)return e;const u=""+e;return u.length>=t?u:new Array(t-u.length+1).join(a)+u}const l=[];let o;function P(e){o=e.keyCode===27}function M(){o===!0&&(o=!1)}function T(e){o===!0&&(o=!1,q(e,27)===!0&&l[l.length-1](e))}function w(e){window[e]("keydown",P),window[e]("blur",M),window[e]("keyup",T),o=!1}function V(e){g.is.desktop===!0&&(l.push(e),l.length===1&&w("addEventListener"))}function X(e){const t=l.indexOf(e);t!==-1&&(l.splice(t,1),l.length===0&&w("removeEventListener"))}const s=[];function E(e){s[s.length-1](e)}function Y(e){g.is.desktop===!0&&(s.push(e),s.length===1&&document.body.addEventListener("focusin",E))}function Z(e){const t=s.indexOf(e);t!==-1&&(s.splice(t,1),s.length===0&&document.body.removeEventListener("focusin",E))}export{W as Q,U as a,G as b,Y as c,V as d,X as e,H as n,J as p,Z as r};
