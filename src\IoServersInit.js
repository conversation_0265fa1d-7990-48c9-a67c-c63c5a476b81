//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// IoServersInit.js
//
// Initialize the register structure (the one that will be live updated)
//=============================================================================================
console.log('========== IoServersInit.js ========')

// const { cloneObject, arrayToObject } = require('./Util.js')
const { ModbusClient } = require('./ModbusOperations.js')

/**
 * Initializes the IO Servers object
 * @function init
 * @description Initializes the IO Servers object, the one that is processed to make Modbus calls and read registers
 * @returns {void}
 * `ioServers` should be initialy geneerate with arrayToObject and this init function will just decorate it
 */
function init(ioServers, registerList)
{
	// serversSrc can be an array (normally this way) or an object
	// const r = Array.isArray(serversSrc) ? arrayToObject(cloneObject(serversSrc), 'name') : cloneObject(serversSrc)
	const r = ioServers
	const timeout = 2000

	// Add the scan list array to each server
	Object.keys(r).forEach(key => {
		const { host, port } = r[key]
		r[key].xferCount = 0 // Number of modbus requests sucessfully completed
		r[key].updated = 0 // Timestamp of the last update
		r[key].requestIpr = false
		r[key].requestLatency = 0 // Latency of the last request
		r[key].promise = null     // The Modbus request promise is stored here
		r[key].readyState = null  // The ready state of the Modbus client
		r[key].error = {
			write: false, // For write errors
			state: false, // For packet based errors like timeout
			message: null,
			timestamp: 0, // Timestamp of the last error
			count: 0,     // Error counter
		}
		r[key].modbusClient = new ModbusClient(host, port, timeout)
		r[key].requestQueue = [] // The list of modbus register groups to scan
		r[key].writeQueue = []   // write transactions are posted here
		r[key].writeIpr = false  // Flag to indicate that write transaction(s) are in progress
	})

	// Distribute the register sets to the IO Servers Request Queue
	registerList.forEach(registerGroup => {
		if(registerGroup.server) // If the server isn't specified, it's virtual and doesn't need to be scanned
		{
			if(typeof registerGroup.server === 'string' && r[registerGroup.server] && registerGroup.params) {

				const thisServer = r[registerGroup.server]

				// link in the server error object to each register
				registerGroup.registers.forEach(reg => {
					reg.comError = thisServer.error
				})

				thisServer.requestQueue.push(registerGroup)
				// r[registerGroup.server].requestQueue.push(registerGroup)
			}
			else {
				console.log('Invalid server:', registerGroup.server, 'must be one of', Object.keys(r).join(', '))
			}
		}
	})

	return r
	// return {}
}

module.exports = {
	init,
}

