<!DOCTYPE html>
<html lang = "en" >
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<title>GeoSCADA Alert</title>
		<style>
			@media screen and (max-width: 640px) {
				.content {
					width: 100% !important;
					display: block !important;
				}
				.body {
					padding: 8px !important;
				}
			}
			.bg-green { background-color: #00CC00; }
			.bg-red { background-color: #CC0000; color: #FFFFFF; }
			.bg-yellow { background-color: #FA9600; }
			table.data-table tr td, table.data-table tr th { border: 1px solid #dededf; padding: 4px 8px; }
			.register-error { color: #CC0000; }
			.text-lock { background-color: #ffc8c8;  }
			.text-alarm { background-color: #fff0da;  }
		</style>
	</head>
	<body style="font-family: Poppins, Arial, sans-serif">
		<table width="100%" border="0" cellspacing="0" cellpadding="0">
			<tr>
				<td align="center" style="padding: 0;">
					<table class="content" width="640" border="0" cellspacing="0" cellpadding="0" style="border-collapse: collapse; border: 1px solid #cccccc; background-color: #F0F0F0;">
						<!--Header -->
						<tr>
							<td class="header" style="background-color: #2a81db; padding: 40px; text-align: center; color: white; font-size: 24px;">
								Alert From: %%PROJECT_NAME%%
								<div style="font-size: 18px; margin: 8px 0 0 0;">
									Location: %%PROJECT_LOCATION%%
								</div>
								<b style="color: #000; text-decoration: underline;">%%TEST_NOTE%%</b>
							</td>
						</tr>
						<!--Body -->
						<tr>
							<td class="body" style="padding: 10px 20px; text-align: left; font-size: 16px; line-height: 1.6;">
								Alarm/Lockout Status for <b>%%PROJECT_NAME%%</b> (Serial Number: <b>%%MAC_ADDRESS%%</b>)
							</td>
						</tr>
						<!--Alert Lockout Status-->
						<tr>
							<td style="padding: 0 6px;">
								<table class="data-table" width="100%" border="0" cellspacing="0" cellpadding="0" style="border-collapse: collapse; border: 1px solid #888; background-color: #FFF;">
									<tr>
										<th>
											Description
										</th>
										<th>
											Condition
										</th>
										<th>
											State
										</th>
										<th>
											Value
										</th>
									</tr>
									%%ALERT_ROWS%%
								</table>
							</td>
						</tr>
						<tr>
							<td class="body" style="padding: 10px 20px; text-align: left; font-size: 16px; line-height: 1.6;">
								System Status for <b>%%PROJECT_NAME%%</b>
							</td>
						</tr>
						<tr>
							<td style="padding: 0 6px;">
								<table class="data-table" width="100%" border="0" cellspacing="0" cellpadding="0" style="border-collapse: collapse; border: 1px solid #888; background-color: #FFF;">
									<tr>
										<th>
											Device
										</th>
										<th>
											Register
										</th>
										<th>
											Value
										</th>
										<th>
											Alt Val
										</th>
									</tr>
									%%STATUS_ROWS%%
								</table>
							</td>
						</tr>
						<tr>
							<td class="body" style="padding: 20px 20px 0 20px; text-align: left; font-size: 14px; line-height: 1.6;">
								Note: * A positive value means the building is being heated. A negative value, the building is being cooled.
							</td>
						</tr>
						<tr>
							<td class="body" style="padding: 20px; text-align: left; font-size: 14px; line-height: 1.6;">
								This is an automatically generated email from the <b>%%PROJECT_NAME%%</b> GeoSCADA system serial number: <b>%%MAC_ADDRESS%%</b> located at: <b>%%PROJECT_LOCATION%%</b>. Please do not reply. If you no longer wish to receive this type of email, change the email configuration of the GeoSCADA system. Contact your supplier for support.
							</td>
						</tr>
						<!-- Footer -->
						<tr>
							<td class="footer" style="background-color: #333333; padding: 20px; text-align: center; color: white; font-size: 14px;">
								&copy; 2025 | Praevista Inc.
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
	</body>
</html>