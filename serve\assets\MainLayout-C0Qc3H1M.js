import{c as j,a as u,h as L,b as oe,g as le,i as ve,e as F,l as me,r as C,w as S,o as ge,d as ut,n as De,f as Qe,j as Te,k as Ae,p as Ee,m as ct,q as dt,s as ft,t as vt,u as mt,v as gt,x as ht,y as _e,z as pt,A as re,B as yt,C as bt,D,E as A,F as _,G as v,H as l,Q as X,I as $,J as R,K as wt,L as Ue,M as Me,O as N,P as kt,R as St,S as V,T as I,U as Y,V as Ie,W as _t,X as Ce,Y as Ct}from"./index-CzmOWWdj.js";import{Q as xt}from"./QImg-DdyGoaK-.js";import{Q as qt,a as Lt,b as xe}from"./QCard-Dm-gpeRW.js";import{Q as qe}from"./QSeparator-D-fNGoQY.js";import{Q as $e}from"./QResizeObserver-Bk4whrbK.js";import{b as se,Q as te,a as fe}from"./focusout-C-pmmZED.js";import{Q as Tt}from"./QSpace-i2TdLNVM.js";import{u as $t,a as Bt,b as Qt,c as Mt,d as zt,Q as ae}from"./use-key-composition-CoMUTTxZ.js";import{Q as Vt}from"./QList-yWBbetAh.js";import{u as Pt,a as Ot,Q as ue,b as Dt,c as At}from"./QDialog-Cuvxr_1w.js";import{u as It}from"./use-timeout-DeCFbuIx.js";import{T as Le}from"./TouchPan-C63yVDWw.js";import{_ as Rt}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{u as je}from"./use-quasar-Li8tSQ3f.js";import{Q as Re}from"./QInput-CCOJFSf5.js";import{Q as Ht}from"./QCheckbox-BLQMX8bg.js";import{C as He}from"./ClosePopup-DhH_6eqd.js";import{u as W,v as Nt,i as de,t as Ne}from"./Login-BVtZ5S7B.js";import{d as ce}from"./Debug-gcimLaHD.js";import{u as Wt}from"./info-store-rjwJi5x9.js";import"./use-checkbox-DDSE4paG.js";const Be=j({name:"QToolbarTitle",props:{shrink:Boolean},setup(e,{slots:n}){const i=u(()=>"q-toolbar__title ellipsis"+(e.shrink===!0?" col-shrink":""));return()=>L("div",{class:i.value},oe(n.default))}}),Je=j({name:"QToolbar",props:{inset:Boolean},setup(e,{slots:n}){const i=u(()=>"q-toolbar row no-wrap items-center"+(e.inset===!0?" q-toolbar--inset":""));return()=>L("div",{class:i.value,role:"toolbar"},oe(n.default))}}),Ft=j({name:"QHeader",props:{modelValue:{type:Boolean,default:!0},reveal:Boolean,revealOffset:{type:Number,default:250},bordered:Boolean,elevated:Boolean,heightHint:{type:[String,Number],default:50}},emits:["reveal","focusin"],setup(e,{slots:n,emit:i}){const{proxy:{$q:s}}=le(),o=ve(me,F);if(o===F)return console.error("QHeader needs to be child of QLayout"),F;const d=C(parseInt(e.heightHint,10)),p=C(!0),x=u(()=>e.reveal===!0||o.view.value.indexOf("H")!==-1||s.platform.is.ios&&o.isContainer.value===!0),g=u(()=>{if(e.modelValue!==!0)return 0;if(x.value===!0)return p.value===!0?d.value:0;const c=d.value-o.scroll.value.position;return c>0?c:0}),w=u(()=>e.modelValue!==!0||x.value===!0&&p.value!==!0),a=u(()=>e.modelValue===!0&&w.value===!0&&e.reveal===!0),B=u(()=>"q-header q-layout__section--marginal "+(x.value===!0?"fixed":"absolute")+"-top"+(e.bordered===!0?" q-header--bordered":"")+(w.value===!0?" q-header--hidden":"")+(e.modelValue!==!0?" q-layout--prevent-focus":"")),q=u(()=>{const c=o.rows.value.top,Q={};return c[0]==="l"&&o.left.space===!0&&(Q[s.lang.rtl===!0?"right":"left"]=`${o.left.size}px`),c[2]==="r"&&o.right.space===!0&&(Q[s.lang.rtl===!0?"left":"right"]=`${o.right.size}px`),Q});function y(c,Q){o.update("header",c,Q)}function h(c,Q){c.value!==Q&&(c.value=Q)}function k({height:c}){h(d,c),y("size",c)}function r(c){a.value===!0&&h(p,!0),i("focusin",c)}S(()=>e.modelValue,c=>{y("space",c),h(p,!0),o.animate()}),S(g,c=>{y("offset",c)}),S(()=>e.reveal,c=>{c===!1&&h(p,e.modelValue)}),S(p,c=>{o.animate(),i("reveal",c)}),S(o.scroll,c=>{e.reveal===!0&&h(p,c.direction==="up"||c.position<=e.revealOffset||c.position-c.inflectionPoint<100)});const f={};return o.instances.header=f,e.modelValue===!0&&y("size",d.value),y("space",e.modelValue),y("offset",g.value),ge(()=>{o.instances.header===f&&(o.instances.header=void 0,y("size",0),y("offset",0),y("space",!1))}),()=>{const c=ut(n.default,[]);return e.elevated===!0&&c.push(L("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),c.push(L($e,{debounce:0,onResize:k})),L("header",{class:B.value,style:q.value,onFocusin:r},c)}}}),We=150,Et=j({name:"QDrawer",inheritAttrs:!1,props:{...Qt,...Bt,side:{type:String,default:"left",validator:e=>["left","right"].includes(e)},width:{type:Number,default:300},mini:Boolean,miniToOverlay:Boolean,miniWidth:{type:Number,default:57},noMiniAnimation:Boolean,breakpoint:{type:Number,default:1023},showIfAbove:Boolean,behavior:{type:String,validator:e=>["default","desktop","mobile"].includes(e),default:"default"},bordered:Boolean,elevated:Boolean,overlay:Boolean,persistent:Boolean,noSwipeOpen:Boolean,noSwipeClose:Boolean,noSwipeBackdrop:Boolean},emits:[...$t,"onLayout","miniState"],setup(e,{slots:n,emit:i,attrs:s}){const o=le(),{proxy:{$q:d}}=o,p=Mt(e,d),{preventBodyScroll:x}=Ot(),{registerTimeout:g,removeTimeout:w}=It(),a=ve(me,F);if(a===F)return console.error("QDrawer needs to be child of QLayout"),F;let B,q=null,y;const h=C(e.behavior==="mobile"||e.behavior!=="desktop"&&a.totalWidth.value<=e.breakpoint),k=u(()=>e.mini===!0&&h.value!==!0),r=u(()=>k.value===!0?e.miniWidth:e.width),f=C(e.showIfAbove===!0&&h.value===!1?!0:e.modelValue===!0),c=u(()=>e.persistent!==!0&&(h.value===!0||Ke.value===!0));function Q(t,m){if(P(),t!==!1&&a.animate(),O(0),h.value===!0){const z=a.instances[ne.value];z?.belowBreakpoint===!0&&z.hide(!1),E(1),a.isContainer.value!==!0&&x(!0)}else E(0),t!==!1&&we(!1);g(()=>{t!==!1&&we(!0),m!==!0&&i("show",t)},We)}function b(t,m){Z(),t!==!1&&a.animate(),E(0),O(K.value*r.value),ke(),m!==!0?g(()=>{i("hide",t)},We):w()}const{show:T,hide:M}=zt({showing:f,hideOnRouteChange:c,handleShow:Q,handleHide:b}),{addToHistory:P,removeFromHistory:Z}=Pt(f,M,c),J={belowBreakpoint:h,hide:M},H=u(()=>e.side==="right"),K=u(()=>(d.lang.rtl===!0?-1:1)*(H.value===!0?1:-1)),ze=C(0),G=C(!1),he=C(!1),Ve=C(r.value*K.value),ne=u(()=>H.value===!0?"left":"right"),pe=u(()=>f.value===!0&&h.value===!1&&e.overlay===!1?e.miniToOverlay===!0?e.miniWidth:r.value:0),ye=u(()=>e.overlay===!0||e.miniToOverlay===!0||a.view.value.indexOf(H.value?"R":"L")!==-1||d.platform.is.ios===!0&&a.isContainer.value===!0),ee=u(()=>e.overlay===!1&&f.value===!0&&h.value===!1),Ke=u(()=>e.overlay===!0&&f.value===!0&&h.value===!1),Ge=u(()=>"fullscreen q-drawer__backdrop"+(f.value===!1&&G.value===!1?" hidden":"")),Xe=u(()=>({backgroundColor:`rgba(0,0,0,${ze.value*.4})`})),Pe=u(()=>H.value===!0?a.rows.value.top[2]==="r":a.rows.value.top[0]==="l"),Ye=u(()=>H.value===!0?a.rows.value.bottom[2]==="r":a.rows.value.bottom[0]==="l"),Ze=u(()=>{const t={};return a.header.space===!0&&Pe.value===!1&&(ye.value===!0?t.top=`${a.header.offset}px`:a.header.space===!0&&(t.top=`${a.header.size}px`)),a.footer.space===!0&&Ye.value===!1&&(ye.value===!0?t.bottom=`${a.footer.offset}px`:a.footer.space===!0&&(t.bottom=`${a.footer.size}px`)),t}),et=u(()=>{const t={width:`${r.value}px`,transform:`translateX(${Ve.value}px)`};return h.value===!0?t:Object.assign(t,Ze.value)}),tt=u(()=>"q-drawer__content fit "+(a.isContainer.value!==!0?"scroll":"overflow-auto")),at=u(()=>`q-drawer q-drawer--${e.side}`+(he.value===!0?" q-drawer--mini-animate":"")+(e.bordered===!0?" q-drawer--bordered":"")+(p.value===!0?" q-drawer--dark q-dark":"")+(G.value===!0?" no-transition":f.value===!0?"":" q-layout--prevent-focus")+(h.value===!0?" fixed q-drawer--on-top q-drawer--mobile q-drawer--top-padding":` q-drawer--${k.value===!0?"mini":"standard"}`+(ye.value===!0||ee.value!==!0?" fixed":"")+(e.overlay===!0||e.miniToOverlay===!0?" q-drawer--on-top":"")+(Pe.value===!0?" q-drawer--top-padding":""))),ot=u(()=>{const t=d.lang.rtl===!0?e.side:ne.value;return[[Le,rt,void 0,{[t]:!0,mouse:!0}]]}),lt=u(()=>{const t=d.lang.rtl===!0?ne.value:e.side;return[[Le,Oe,void 0,{[t]:!0,mouse:!0}]]}),nt=u(()=>{const t=d.lang.rtl===!0?ne.value:e.side;return[[Le,Oe,void 0,{[t]:!0,mouse:!0,mouseAllDir:!0}]]});function be(){st(h,e.behavior==="mobile"||e.behavior!=="desktop"&&a.totalWidth.value<=e.breakpoint)}S(h,t=>{t===!0?(B=f.value,f.value===!0&&M(!1)):e.overlay===!1&&e.behavior!=="mobile"&&B!==!1&&(f.value===!0?(O(0),E(0),ke()):T(!1))}),S(()=>e.side,(t,m)=>{a.instances[m]===J&&(a.instances[m]=void 0,a[m].space=!1,a[m].offset=0),a.instances[t]=J,a[t].size=r.value,a[t].space=ee.value,a[t].offset=pe.value}),S(a.totalWidth,()=>{(a.isContainer.value===!0||document.qScrollPrevented!==!0)&&be()}),S(()=>e.behavior+e.breakpoint,be),S(a.isContainer,t=>{f.value===!0&&x(t!==!0),t===!0&&be()}),S(a.scrollbarWidth,()=>{O(f.value===!0?0:void 0)}),S(pe,t=>{U("offset",t)}),S(ee,t=>{i("onLayout",t),U("space",t)}),S(H,()=>{O()}),S(r,t=>{O(),Se(e.miniToOverlay,t)}),S(()=>e.miniToOverlay,t=>{Se(t,r.value)}),S(()=>d.lang.rtl,()=>{O()}),S(()=>e.mini,()=>{e.noMiniAnimation||e.modelValue===!0&&(it(),a.animate())}),S(k,t=>{i("miniState",t)});function O(t){t===void 0?De(()=>{t=f.value===!0?0:r.value,O(K.value*t)}):(a.isContainer.value===!0&&H.value===!0&&(h.value===!0||Math.abs(t)===r.value)&&(t+=K.value*a.scrollbarWidth.value),Ve.value=t)}function E(t){ze.value=t}function we(t){const m=t===!0?"remove":a.isContainer.value!==!0?"add":"";m!==""&&document.body.classList[m]("q-body--drawer-toggle")}function it(){q!==null&&clearTimeout(q),o.proxy&&o.proxy.$el&&o.proxy.$el.classList.add("q-drawer--mini-animate"),he.value=!0,q=setTimeout(()=>{q=null,he.value=!1,o?.proxy?.$el?.classList.remove("q-drawer--mini-animate")},150)}function rt(t){if(f.value!==!1)return;const m=r.value,z=se(t.distance.x,0,m);if(t.isFinal===!0){z>=Math.min(75,m)===!0?T():(a.animate(),E(0),O(K.value*m)),G.value=!1;return}O((d.lang.rtl===!0?H.value!==!0:H.value)?Math.max(m-z,0):Math.min(0,z-m)),E(se(z/m,0,1)),t.isFirst===!0&&(G.value=!0)}function Oe(t){if(f.value!==!0)return;const m=r.value,z=t.direction===e.side,ie=(d.lang.rtl===!0?z!==!0:z)?se(t.distance.x,0,m):0;if(t.isFinal===!0){Math.abs(ie)<Math.min(75,m)===!0?(a.animate(),E(1),O(0)):M(),G.value=!1;return}O(K.value*ie),E(se(1-ie/m,0,1)),t.isFirst===!0&&(G.value=!0)}function ke(){x(!1),we(!0)}function U(t,m){a.update(e.side,t,m)}function st(t,m){t.value!==m&&(t.value=m)}function Se(t,m){U("size",t===!0?e.miniWidth:m)}return a.instances[e.side]=J,Se(e.miniToOverlay,r.value),U("space",ee.value),U("offset",pe.value),e.showIfAbove===!0&&e.modelValue!==!0&&f.value===!0&&e["onUpdate:modelValue"]!==void 0&&i("update:modelValue",!0),Qe(()=>{i("onLayout",ee.value),i("miniState",k.value),B=e.showIfAbove===!0;const t=()=>{(f.value===!0?Q:b)(!1,!0)};if(a.totalWidth.value!==0){De(t);return}y=S(a.totalWidth,()=>{y(),y=void 0,f.value===!1&&e.showIfAbove===!0&&h.value===!1?T(!1):t()})}),ge(()=>{y?.(),q!==null&&(clearTimeout(q),q=null),f.value===!0&&ke(),a.instances[e.side]===J&&(a.instances[e.side]=void 0,U("size",0),U("offset",0),U("space",!1))}),()=>{const t=[];h.value===!0&&(e.noSwipeOpen===!1&&t.push(Te(L("div",{key:"open",class:`q-drawer__opener fixed-${e.side}`,"aria-hidden":"true"}),ot.value)),t.push(Ae("div",{ref:"backdrop",class:Ge.value,style:Xe.value,"aria-hidden":"true",onClick:M},void 0,"backdrop",e.noSwipeBackdrop!==!0&&f.value===!0,()=>nt.value)));const m=k.value===!0&&n.mini!==void 0,z=[L("div",{...s,key:""+m,class:[tt.value,s.class]},m===!0?n.mini():oe(n.default))];return e.elevated===!0&&f.value===!0&&z.push(L("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),t.push(Ae("aside",{ref:"content",class:at.value,style:et.value},z,"contentclose",e.noSwipeClose!==!0&&h.value===!0,()=>lt.value)),L("div",{class:"q-drawer-container"},t)}}}),Ut=j({name:"QPageContainer",setup(e,{slots:n}){const{proxy:{$q:i}}=le(),s=ve(me,F);if(s===F)return console.error("QPageContainer needs to be child of QLayout"),F;Ee(ct,!0);const o=u(()=>{const d={};return s.header.space===!0&&(d.paddingTop=`${s.header.size}px`),s.right.space===!0&&(d[`padding${i.lang.rtl===!0?"Left":"Right"}`]=`${s.right.size}px`),s.footer.space===!0&&(d.paddingBottom=`${s.footer.size}px`),s.left.space===!0&&(d[`padding${i.lang.rtl===!0?"Right":"Left"}`]=`${s.left.size}px`),d});return()=>L("div",{class:"q-page-container",style:o.value},oe(n.default))}}),{passive:Fe}=dt,jt=["both","horizontal","vertical"],Jt=j({name:"QScrollObserver",props:{axis:{type:String,validator:e=>jt.includes(e),default:"vertical"},debounce:[String,Number],scrollTarget:ft},emits:["scroll"],setup(e,{emit:n}){const i={position:{top:0,left:0},direction:"down",directionChanged:!1,delta:{top:0,left:0},inflectionPoint:{top:0,left:0}};let s=null,o,d;S(()=>e.scrollTarget,()=>{g(),x()});function p(){s?.();const B=Math.max(0,gt(o)),q=ht(o),y={top:B-i.position.top,left:q-i.position.left};if(e.axis==="vertical"&&y.top===0||e.axis==="horizontal"&&y.left===0)return;const h=Math.abs(y.top)>=Math.abs(y.left)?y.top<0?"up":"down":y.left<0?"left":"right";i.position={top:B,left:q},i.directionChanged=i.direction!==h,i.delta=y,i.directionChanged===!0&&(i.direction=h,i.inflectionPoint=i.position),n("scroll",{...i})}function x(){o=vt(d,e.scrollTarget),o.addEventListener("scroll",w,Fe),w(!0)}function g(){o!==void 0&&(o.removeEventListener("scroll",w,Fe),o=void 0)}function w(B){if(B===!0||e.debounce===0||e.debounce==="0")p();else if(s===null){const[q,y]=e.debounce?[setTimeout(p,e.debounce),clearTimeout]:[requestAnimationFrame(p),cancelAnimationFrame];s=()=>{y(q),s=null}}}const{proxy:a}=le();return S(()=>a.$q.lang.rtl,p),Qe(()=>{d=a.$el.parentNode,x()}),ge(()=>{s?.(),g()}),Object.assign(a,{trigger:w,getPosition:()=>i}),mt}}),Kt=j({name:"QLayout",props:{container:Boolean,view:{type:String,default:"hhh lpr fff",validator:e=>/^(h|l)h(h|r) lpr (f|l)f(f|r)$/.test(e.toLowerCase())},onScroll:Function,onScrollHeight:Function,onResize:Function},setup(e,{slots:n,emit:i}){const{proxy:{$q:s}}=le(),o=C(null),d=C(s.screen.height),p=C(e.container===!0?0:s.screen.width),x=C({position:0,direction:"down",inflectionPoint:0}),g=C(0),w=C(pt.value===!0?0:_e()),a=u(()=>"q-layout q-layout--"+(e.container===!0?"containerized":"standard")),B=u(()=>e.container===!1?{minHeight:s.screen.height+"px"}:null),q=u(()=>w.value!==0?{[s.lang.rtl===!0?"left":"right"]:`${w.value}px`}:null),y=u(()=>w.value!==0?{[s.lang.rtl===!0?"right":"left"]:0,[s.lang.rtl===!0?"left":"right"]:`-${w.value}px`,width:`calc(100% + ${w.value}px)`}:null);function h(b){if(e.container===!0||document.qScrollPrevented!==!0){const T={position:b.position.top,direction:b.direction,directionChanged:b.directionChanged,inflectionPoint:b.inflectionPoint.top,delta:b.delta.top};x.value=T,e.onScroll!==void 0&&i("scroll",T)}}function k(b){const{height:T,width:M}=b;let P=!1;d.value!==T&&(P=!0,d.value=T,e.onScrollHeight!==void 0&&i("scrollHeight",T),f()),p.value!==M&&(P=!0,p.value=M),P===!0&&e.onResize!==void 0&&i("resize",b)}function r({height:b}){g.value!==b&&(g.value=b,f())}function f(){if(e.container===!0){const b=d.value>g.value?_e():0;w.value!==b&&(w.value=b)}}let c=null;const Q={instances:{},view:u(()=>e.view),isContainer:u(()=>e.container),rootRef:o,height:d,containerHeight:g,scrollbarWidth:w,totalWidth:u(()=>p.value+w.value),rows:u(()=>{const b=e.view.toLowerCase().split(" ");return{top:b[0].split(""),middle:b[1].split(""),bottom:b[2].split("")}}),header:re({size:0,offset:0,space:!1}),right:re({size:300,offset:0,space:!1}),footer:re({size:0,offset:0,space:!1}),left:re({size:300,offset:0,space:!1}),scroll:x,animate(){c!==null?clearTimeout(c):document.body.classList.add("q-body--layout-animate"),c=setTimeout(()=>{c=null,document.body.classList.remove("q-body--layout-animate")},155)},update(b,T,M){Q[b][T]=M}};if(Ee(me,Q),_e()>0){let b=function(){P=null,Z.classList.remove("hide-scrollbar")},T=function(){if(P===null){if(Z.scrollHeight>s.screen.height)return;Z.classList.add("hide-scrollbar")}else clearTimeout(P);P=setTimeout(b,300)},M=function(J){P!==null&&J==="remove"&&(clearTimeout(P),b()),window[`${J}EventListener`]("resize",T)},P=null;const Z=document.body;S(()=>e.container!==!0?"add":"remove",M),e.container!==!0&&M("add"),yt(()=>{M("remove")})}return()=>{const b=bt(n.default,[L(Jt,{onScroll:h}),L($e,{onResize:k})]),T=L("div",{class:a.value,style:B.value,ref:e.container===!0?void 0:o,tabindex:-1},b);return e.container===!0?L("div",{class:"q-layout-container overflow-hidden",ref:o},[L($e,{onResize:r}),L("div",{class:"absolute-full",style:q.value},[L("div",{class:"scroll",style:y.value},[T])])]):T}}}),Gt="/assets/Praevista-SCADA-Logo-W-CZngRCS5.svg",Xt={__name:"PopoutNavItem",props:{title:{type:String,required:!0},caption:{type:String,default:""},link:{type:String,default:"#"},disable:{type:Boolean,default:!1},icon:{type:String,default:""},tooltip:{type:Boolean,default:!1}},setup(e){const n=e;return(i,s)=>e.link[0]=="/"&&!e.disable?(_(),D(fe,{key:0,clickable:"",to:n.link},{default:v(()=>[n.icon?(_(),D(te,{key:0,avatar:"",class:"my-q-item-section-1"},{default:v(()=>[l(X,{name:n.icon},null,8,["name"])]),_:1})):A("",!0),l(te,null,{default:v(()=>[l(ue,null,{default:v(()=>[$(R(n.title),1)]),_:1}),l(ue,{caption:""},{default:v(()=>[$(R(n.caption),1)]),_:1})]),_:1}),n.tooltip?(_(),D(ae,{key:1,anchor:"center right",self:"center left",class:"text-body2","transition-show":"jump-right","transition-hide":"jump-left"},{default:v(()=>[$(R(n.title),1)]),_:1})):A("",!0)]),_:1},8,["to"])):e.disable?A("",!0):(_(),D(fe,{key:1,clickable:"",tag:"a",target:"_blank",href:n.link},{default:v(()=>[n.icon?(_(),D(te,{key:0,avatar:"",class:"my-q-item-section-1"},{default:v(()=>[l(X,{name:n.icon},null,8,["name"])]),_:1})):A("",!0),l(te,null,{default:v(()=>[l(ue,null,{default:v(()=>[$(R(n.title)+" ",1),l(X,{name:"launch",color:"primary"})]),_:1}),l(ue,{caption:""},{default:v(()=>[$(R(n.caption),1)]),_:1})]),_:1}),n.tooltip?(_(),D(ae,{key:1,anchor:"center right",self:"center left",class:"text-body2","transition-show":"jump-right","transition-hide":"jump-left"},{default:v(()=>[$(R(n.title),1)]),_:1})):A("",!0)]),_:1},8,["href"]))}},Yt=Rt(Xt,[["__scopeId","data-v-5ae664c0"]]),Zt={__name:"DarkModeButton",props:{modelValue:{},modelModifiers:{}},emits:["update:modelValue"],setup(e){const n=wt(e,"modelValue"),i=je();function s(){n.value=!n.value,i.dark.set(n.value),localStorage.setItem("darkMode",JSON.stringify(n.value))}return Ue(()=>{n.value=JSON.parse(localStorage.getItem("darkMode")??"true"),i.dark.set(n.value)}),(o,d)=>(_(),D(N,Me({...o.$props,...o.$attrs},{icon:n.value?"dark_mode":"light_mode",onClick:s}),{default:v(()=>[l(ae,{class:"text-body2"},{default:v(()=>[$(" Click to turn on "+R(n.value?"light":"dark")+" mode ",1)]),_:1})]),_:1},16,["icon"]))}},ea=j({name:"QCardActions",props:{...kt,vertical:Boolean},setup(e,{slots:n}){const i=St(e),s=u(()=>`q-card__actions ${i.value} q-card__actions--${e.vertical===!0?"vert column":"horiz row"}`);return()=>L("div",{class:s.value},oe(n.default))}}),ta={key:0},aa={__name:"LoginButton",props:{debug:{type:Boolean,required:!1,default:!1}},setup(e){const n=e,i=je(),s=ve("$login"),o=u(()=>!!W.value),d=C(!1),p=C(!1),x=C(JSON.parse(localStorage.getItem("rememberMe")??"false")),g=u({get(){return console.log("<<< rememberMe.get",x.value),x.value},set(k){const r=!!k;localStorage.setItem("rememberMe",JSON.stringify(r)),console.log(">>> rememberMe.set",r),x.value=r}}),w=C(localStorage.getItem("username")||""),a=C("");function B(k,r){Ne.value=k,r&&localStorage.setItem("username",W.value.user)}function q(k){Ne.value=null,a.value="",k||(w.value="",localStorage.removeItem("username"))}function y(){d.value=!d.value,console.log(">>> LoginButton clickUser")}function h(){if(console.log(">>> LoginButton onSubmit"),o.value){q(g.value),d.value=!1;return}const k={auth:{username:w.value,password:a.value}};g.value&&(k.params={remember:1}),s("",k).then(r=>{B(r.data.token,g.value),console.log("=== LoginButton onSubmit response",r.data,W.value),d.value=!1}).catch(r=>{console.error("=== LoginButton onSubmit error",r),i.notify({type:"negative",progress:!0,message:"Login failed"})})}return Ue(()=>{Nt().then(k=>{console.log(">>> LoginButton onBeforeMount",k)}).catch(k=>{console.error(">>> LoginButton onBeforeMount error",k)})}),(k,r)=>(_(),D(N,Me(k.$props,{icon:o.value?V(de)?"manage_accounts":"person":"person_off",color:o.value?"":"grey-5",onClick:y}),{default:v(()=>[l(ae,{class:"text-body2"},{default:v(()=>[$(" Click to log "+R(o.value?"out as "+V(W).user:"in"),1)]),_:1}),l(Dt,{modelValue:d.value,"onUpdate:modelValue":r[4]||(r[4]=f=>d.value=f),"backdrop-filter":"blur(4px) saturate(150%)"},{default:v(()=>[l(qt,{style:{width:"100%","min-width":"360px","max-width":"500px"},class:"q-pa-sm"},{default:v(()=>[l(Je,null,{default:v(()=>[o.value?(_(),I(Y,{key:0},[l(Ie,{icon:"person_off",color:"negative",size:"xl","font-size":"70%"}),l(Be,null,{default:v(()=>[r[5]||(r[5]=$("Log Out as ")),l(At,{outline:"",square:"",color:"primary"},{default:v(()=>[$(R(V(W).user),1)]),_:1}),r[6]||(r[6]=$("?"))]),_:1,__:[5,6]})],64)):(_(),I(Y,{key:1},[l(Ie,{icon:"person",color:"primary",size:"xl","font-size":"70%"}),l(Be,null,{default:v(()=>r[7]||(r[7]=[$("GeoSCADA Login")])),_:1,__:[7]})],64)),Te(l(N,{icon:"close",flat:"",round:"",dense:""},null,512),[[He]])]),_:1}),l(Lt,{class:"q-gutter-md"},{default:v(()=>[o.value?(_(),I("div",ta,"Are you sure you want to log out?")):(_(),I(Y,{key:1},[l(Re,{outlined:"",modelValue:w.value,"onUpdate:modelValue":r[0]||(r[0]=f=>w.value=f),label:"Username"},null,8,["modelValue"]),l(Re,{class:"q-input-pr-none",outlined:"",modelValue:a.value,"onUpdate:modelValue":r[2]||(r[2]=f=>a.value=f),label:"Password",type:p.value?"text":"password"},{append:v(()=>[l(N,{stretch:"",flat:"",icon:p.value?"visibility":"visibility_off",onClick:r[1]||(r[1]=f=>p.value=!p.value)},null,8,["icon"])]),_:1},8,["modelValue","type"])],64)),l(Ht,{modelValue:g.value,"onUpdate:modelValue":r[3]||(r[3]=f=>g.value=f),label:"Remember Me"},null,8,["modelValue"])]),_:1}),l(ea,{class:"row q-gutter-sm"},{default:v(()=>[Te(l(N,{unelevated:"",class:"col-6",label:"Cancel",color:"grey-8"},null,512),[[He]]),l(N,{unelevated:"",class:"col",label:"Confirm",color:o.value?"negative":"primary",onClick:h},null,8,["color"]),n.debug?(_(),D(N,{key:0,unelevated:"",class:"col-4",label:"Test Token",color:"primary",onClick:k.testToken},null,8,["onClick"])):A("",!0)]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1},16,["icon","color"]))}},oa={class:"inline-block",style:{width:"140px"}},la={key:0,class:"inline-block"},na={key:0,class:"aq-vertical-center"},ia={key:1,class:"text-grey-8"},ra={key:0,class:"absolute-bottom text-caption text-center q-py-sm text-grey-6"},Ba={__name:"MainLayout",setup(e){const n=Wt(),i=[{title:"Dashboard",caption:"Main Home Page",icon:"svguse:icons.svg#dashboard",link:"/dash"},{title:"Trends",caption:"Plot Datalog History",icon:"show_chart",link:"/trends"},{title:"Alarms",caption:"View/Clear Alarms and Lockouts",icon:"warning",link:"/alarm"},{title:"Maintenance",caption:"Perform Maintenance Tasks",icon:"plumbing",link:"/maintenance",type:"debug"},{title:"Register Status",caption:"Local and Remote Io Registers",icon:"svguse:icons.svg#io",link:"/registers",type:"setup"},{title:"Data",caption:"Download Logged Data",icon:"file_download",link:"/download",type:"setup"},{title:"Project Documents",caption:"View Project Documentation",icon:"picture_as_pdf",link:"/documents",type:"setup"},{title:"IO Server Status",caption:"Status of Remote IO Servers",icon:"svguse:icons.svg#server",link:"/serverstatus",type:"setup"},{title:"Settings",caption:"Configure System Settings",icon:"settings",link:"/setup/settings",type:"setup"},{title:"Control Curves",caption:"View/Edit Control Curves",icon:"show_chart",link:"/setup/curves",type:"setup"},{title:"Schedules",caption:"Date/Time Based Events",icon:"schedule",link:"/setup/schedules",type:"setup"},{title:"Utilities",caption:"Special System Functions",icon:"build",link:"/util",type:"debug",permission:"admin"},{title:"Debug",caption:"Special Debugging Functions",icon:"svguse:icons.svg#bug",link:"/debug",type:"debug",permission:"admin"}],s=C(!1),o=C(!1);function d(){s.value=!s.value}const p=u(()=>n.info?n.info.projectSettings:{});return Qe(()=>{console.log("$$$> MainLayout onMounted infoStore",n.info)}),ge(()=>{console.log("MainLayout onBeforeUnmount")}),(x,g)=>{const w=_t("router-view");return _(),D(Kt,{view:"hHh Lpr lFf"},{default:v(()=>[l(Ft,null,{default:v(()=>[l(Je,{class:"q-pr-none"},{default:v(()=>[l(N,{flat:"",dense:"",round:"",icon:"menu","aria-label":"Menu",onClick:d}),l(Be,{class:"text-left"},{default:v(()=>[Ce("div",oa,[l(xt,{src:Gt,fit:"contain",style:{height:"28px"}})]),p.value.PROJECT_NAME?(_(),I("div",la,[l(xe,{class:"text-h4 q-ml-lg",outline:"",color:"white",label:p.value.PROJECT_NAME},null,8,["label"])])):A("",!0)]),_:1}),l(qe,{vertical:"",dark:""}),l(Zt,{flat:"",stretch:"",size:"sm"}),l(qe,{vertical:"",dark:""}),l(aa,{flat:"",stretch:"",class:"q-px-lg"})]),_:1})]),_:1}),l(Et,{modelValue:s.value,"onUpdate:modelValue":g[2]||(g[2]=a=>s.value=a),"show-if-above":"",bordered:"",mini:o.value,width:260},{default:v(()=>[l(Vt,null,{default:v(()=>[l(fe,{clickable:"",onClick:g[0]||(g[0]=a=>o.value=!o.value),class:"mobile-hide"},{default:v(()=>[l(te,{avatar:""},{default:v(()=>[l(X,{name:o.value?"chevron_right":"chevron_left"},null,8,["name"])]),_:1})]),_:1}),(_(),I(Y,null,Ct(i,a=>l(Yt,Me({key:a.title,tooltip:o.value,ref_for:!0},a,{disable:a.type==="debug"&&(!V(ce)||!V(de))}),null,16,["tooltip","disable"])),64)),l(qe),l(fe,null,{default:v(()=>[Ce("div",null,[V(W)?(_(),I("div",na,[l(X,{name:V(de)?"manage_accounts":"person"},null,8,["name"]),o.value?A("",!0):(_(),I(Y,{key:0},[g[3]||(g[3]=$(" Logged in as ")),l(xe,{outline:"",color:"primary",label:V(W).user},null,8,["label"])],64))])):(_(),I("div",ia,[l(X,{name:"person_off"}),o.value?A("",!0):(_(),I(Y,{key:0},[$(" Not logged in ")],64))]))]),l(Tt),V(de)?(_(),D(N,{key:0,flat:"",round:"",color:V(ce)?"primary":"grey-8",onClick:g[1]||(g[1]=a=>ce.value=!V(ce)),size:"sm",icon:"svguse:icons.svg#bug"},null,8,["color"])):A("",!0),o.value?(_(),D(ae,{key:1,anchor:"center right",self:"center left",class:"text-body2","transition-show":"jump-right","transition-hide":"jump-left"},{default:v(()=>[$(R(V(W)?"Logged in as "+V(W).user:"Not logged in"),1)]),_:1})):A("",!0)]),_:1})]),_:1}),p.value.MAC&&!o.value?(_(),I("div",ra,[Ce("div",null,[g[4]||(g[4]=$("MAC: ")),l(xe,{outline:"",label:p.value.MAC,color:"grey-6"},null,8,["label"])])])):A("",!0)]),_:1},8,["modelValue","mini"]),l(Ut,null,{default:v(()=>[l(w)]),_:1})]),_:1})}}};export{Ba as default};
