const nodemailer = require('nodemailer')
require('dotenv').config()


class EmailSender {

	/**
	 * @constructor
	 * @param {Object} options - Configuration options for the email.
	 * @param {string} options.to
	 * @param {string} options.subject
	 * @param {string} options.body
	 * @param {Array<{filename: string, data: Buffer}>} [options.attachments=[]] - Array of attachments, each with a filename and binary data as a Buffer.
	 * @param {string} [options.user] - SMTP username
	 * @param {string} [options.pass] - SMTP password
	 * @param {string} [options.host] - SMTP server host
	 * @param {number} [options.port] - SMTP server port.
	 * @throws {Error} If fields (to, subject, body) or email credentials (user, pass, host, port) are missing
	 */
	constructor(options) {
		this.debug = options.debug || false

		if (!options.to || !options.subject || !options.body) {
			throw new Error("to, subject, and body are required.");
		}

		this.from = options.user || process.env.PRAEVISTA_SMTP_USER;
		this.to = options.to;
		this.subject = options.subject;
		this.body = options.body;
		this.attachments = options.attachments || [];
		this.host = options.host || process.env.PRAEVISTA_SMTP_HOST;
		this.port = options.port || process.env.PRAEVISTA_SMTP_PORT;

		const auth = {
			user: options.user || process.env.PRAEVISTA_SMTP_USER,
			pass: options.pass || process.env.PRAEVISTA_SMTP_PASS
		};

		// credentials validtion
		if (!auth.user || !auth.pass) {
			throw new Error("Email credentials are required.");
		}
		if (!this.host) {
			throw new Error("SMTP host is required. ");
		}
		if (!this.port || isNaN(this.port)) {
			throw new Error("Valid SMTP port is required.");
		}

		// creating nodemailer transporter
		this.transporter = nodemailer.createTransport({
			host: this.host,
			port: this.port,
			secure: true,
			auth: auth,
			debug: this.debug,
			logger: this.debug,
			tls: {
				// disable certificate validation
				rejectUnauthorized: false,
			},
		});
	}

	/**
	 * Sends the configured email using the Nodemailer transporter.
	 * @returns {Promise<Object>} A promise that resolves with standardized response object
	 */
	async send() {
		const response = {
			message: "",
			error: false
		}
		try {
			if (this.debug) {
				response.steps = []
				try {
					await this.transporter.verify()
					response.steps.push('SMTP verification successful')
				} catch (verifyError) {
					response.steps.push('SMTP verification failed')
				}

			}

			// Prepare email options
			const eMail = {
				to: this.to,
				from: this.from,
				subject: this.subject,
				html: this.body,
				attachments: this.attachments.map(att => ({ filename: att.filename, content: att.data }))
			};

			// Send email 
			const result = await this.transporter.sendMail(eMail)

			response.message = "Email sent successfully"

			if (this.debug) {
				response.steps.push("Email sent successfully")
				response.result = {
					messageID: result.messageId,
					response: result.response
				}
			}

			return response

		} catch (error) {

			const response = {
				message: "",
				error: false
			}
			response.message = error.message
			response.error = true

			if (this.debug) {
				response.error = {
					code: error.code,
					command: error.command,
					// stack: error.stack
				}
			}
			return response
		}
	}
}

module.exports = EmailSender


// // @testcase
// async function testEmailSender() {
// 	try {
// 		// Create EmailSender instance
// 		const emailSender = new EmailSender({
// 			to: '<EMAIL>',
// 			subject: 'Test Email NNEEWW',
// 			body: '<h1>Hello World!</h1><p>This is a test email.</p>',
// 			debug: true
// 		});

// 		// Send email
// 		console.log('Sending email...');
// 		const result = await emailSender.send();

// 		console.log('Email sent successfully!');
// 		console.log('Response:', JSON.stringify(result, null, 2));

// 	} catch (error) {
// 		console.error('Error sending email:', error.message);
// 	}
// }

// // Run the test
// testEmailSender();