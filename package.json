{"name": "geoscada", "version": "1.0.0", "main": "src/NodeApp.js", "directories": {"doc": "docs"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "MA", "license": "ISC", "description": "", "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/static": "^8.1.1", "bcryptjs": "^3.0.2", "check-disk-space": "^3.4.0", "csv-parse": "^5.6.0", "dayjs": "^1.11.13", "dotenv": "^17.0.1", "fastify": "^5.2.2", "jsonwebtoken": "^9.0.2", "keytar": "^7.9.0", "ky": "^1.8.1", "nodemailer": "^7.0.5", "read-last-lines": "^1.8.0", "uid": "^2.0.2", "winston": "^3.17.0"}}