import{Q as et}from"./QPage-Disdm55v.js";import{a as tt,Q as nt}from"./focusout-C-pmmZED.js";import{Q as W}from"./QSelect-BNWcW_ch.js";import{a as rt,c as st,Q as se}from"./use-key-composition-CoMUTTxZ.js";import{r as L,w as N,a as V,Z as ot,_ as at,A as it,$ as lt,f as he,B as te,T as U,F as E,H as T,X as P,S as w,J as Q,D as z,E as ue,O as H,G as x,Q as Z,j as ut,U as G,a0 as ct,K as dt,I as B,a1 as ft,c as pt,g as ht,h as oe,b as ye,Y as ce,a2 as de,a3 as fe,a4 as mt,o as gt}from"./index-CzmOWWdj.js";import{Q as yt,a as vt}from"./QPopupProxy-D3bKa31G.js";import{Q as bt}from"./QInput-CCOJFSf5.js";import{C as wt}from"./ClosePopup-DhH_6eqd.js";import{d as q}from"./dayjs-mToOJNbe.js";import{u as _t}from"./info-store-rjwJi5x9.js";import{D as ee}from"./dygraph-CO2DREVQ.js";import{_ as ne}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{Q as Tt,a as xe,b as St}from"./QCard-Dm-gpeRW.js";import{b as xt}from"./QDialog-Cuvxr_1w.js";import{Q as Rt}from"./QToggle-D9UX9nQt.js";import{u as $e}from"./use-quasar-Li8tSQ3f.js";import{b as Ct}from"./Login-BVtZ5S7B.js";import"./QMenu-D3shCIOy.js";import"./use-timeout-DeCFbuIx.js";import"./_commonjsHelpers-Bx2EM-6T.js";import"./use-checkbox-DDSE4paG.js";var ae={exports:{}},Re;function kt(){if(Re)return ae.exports;Re=1;var t=typeof Reflect=="object"?Reflect:null,e=t&&typeof t.apply=="function"?t.apply:function(u,m,g){return Function.prototype.apply.call(u,m,g)},n;t&&typeof t.ownKeys=="function"?n=t.ownKeys:Object.getOwnPropertySymbols?n=function(u){return Object.getOwnPropertyNames(u).concat(Object.getOwnPropertySymbols(u))}:n=function(u){return Object.getOwnPropertyNames(u)};function r(d){console&&console.warn&&console.warn(d)}var s=Number.isNaN||function(u){return u!==u};function a(){a.init.call(this)}ae.exports=a,ae.exports.once=D,a.EventEmitter=a,a.prototype._events=void 0,a.prototype._eventsCount=0,a.prototype._maxListeners=void 0;var c=10;function f(d){if(typeof d!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof d)}Object.defineProperty(a,"defaultMaxListeners",{enumerable:!0,get:function(){return c},set:function(d){if(typeof d!="number"||d<0||s(d))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+d+".");c=d}}),a.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},a.prototype.setMaxListeners=function(u){if(typeof u!="number"||u<0||s(u))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+u+".");return this._maxListeners=u,this};function p(d){return d._maxListeners===void 0?a.defaultMaxListeners:d._maxListeners}a.prototype.getMaxListeners=function(){return p(this)},a.prototype.emit=function(u){for(var m=[],g=1;g<arguments.length;g++)m.push(arguments[g]);var o=u==="error",y=this._events;if(y!==void 0)o=o&&y.error===void 0;else if(!o)return!1;if(o){var b;if(m.length>0&&(b=m[0]),b instanceof Error)throw b;var S=new Error("Unhandled error."+(b?" ("+b.message+")":""));throw S.context=b,S}var k=y[u];if(k===void 0)return!1;if(typeof k=="function")e(k,this,m);else for(var M=k.length,Y=R(k,M),g=0;g<M;++g)e(Y[g],this,m);return!0};function i(d,u,m,g){var o,y,b;if(f(m),y=d._events,y===void 0?(y=d._events=Object.create(null),d._eventsCount=0):(y.newListener!==void 0&&(d.emit("newListener",u,m.listener?m.listener:m),y=d._events),b=y[u]),b===void 0)b=y[u]=m,++d._eventsCount;else if(typeof b=="function"?b=y[u]=g?[m,b]:[b,m]:g?b.unshift(m):b.push(m),o=p(d),o>0&&b.length>o&&!b.warned){b.warned=!0;var S=new Error("Possible EventEmitter memory leak detected. "+b.length+" "+String(u)+" listeners added. Use emitter.setMaxListeners() to increase limit");S.name="MaxListenersExceededWarning",S.emitter=d,S.type=u,S.count=b.length,r(S)}return d}a.prototype.addListener=function(u,m){return i(this,u,m,!1)},a.prototype.on=a.prototype.addListener,a.prototype.prependListener=function(u,m){return i(this,u,m,!0)};function l(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function h(d,u,m){var g={fired:!1,wrapFn:void 0,target:d,type:u,listener:m},o=l.bind(g);return o.listener=m,g.wrapFn=o,o}a.prototype.once=function(u,m){return f(m),this.on(u,h(this,u,m)),this},a.prototype.prependOnceListener=function(u,m){return f(m),this.prependListener(u,h(this,u,m)),this},a.prototype.removeListener=function(u,m){var g,o,y,b,S;if(f(m),o=this._events,o===void 0)return this;if(g=o[u],g===void 0)return this;if(g===m||g.listener===m)--this._eventsCount===0?this._events=Object.create(null):(delete o[u],o.removeListener&&this.emit("removeListener",u,g.listener||m));else if(typeof g!="function"){for(y=-1,b=g.length-1;b>=0;b--)if(g[b]===m||g[b].listener===m){S=g[b].listener,y=b;break}if(y<0)return this;y===0?g.shift():A(g,y),g.length===1&&(o[u]=g[0]),o.removeListener!==void 0&&this.emit("removeListener",u,S||m)}return this},a.prototype.off=a.prototype.removeListener,a.prototype.removeAllListeners=function(u){var m,g,o;if(g=this._events,g===void 0)return this;if(g.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):g[u]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete g[u]),this;if(arguments.length===0){var y=Object.keys(g),b;for(o=0;o<y.length;++o)b=y[o],b!=="removeListener"&&this.removeAllListeners(b);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(m=g[u],typeof m=="function")this.removeListener(u,m);else if(m!==void 0)for(o=m.length-1;o>=0;o--)this.removeListener(u,m[o]);return this};function v(d,u,m){var g=d._events;if(g===void 0)return[];var o=g[u];return o===void 0?[]:typeof o=="function"?m?[o.listener||o]:[o]:m?O(o):R(o,o.length)}a.prototype.listeners=function(u){return v(this,u,!0)},a.prototype.rawListeners=function(u){return v(this,u,!1)},a.listenerCount=function(d,u){return typeof d.listenerCount=="function"?d.listenerCount(u):_.call(d,u)},a.prototype.listenerCount=_;function _(d){var u=this._events;if(u!==void 0){var m=u[d];if(typeof m=="function")return 1;if(m!==void 0)return m.length}return 0}a.prototype.eventNames=function(){return this._eventsCount>0?n(this._events):[]};function R(d,u){for(var m=new Array(u),g=0;g<u;++g)m[g]=d[g];return m}function A(d,u){for(;u+1<d.length;u++)d[u]=d[u+1];d.pop()}function O(d){for(var u=new Array(d.length),m=0;m<u.length;++m)u[m]=d[m].listener||d[m];return u}function D(d,u){return new Promise(function(m,g){function o(b){d.removeListener(u,y),g(b)}function y(){typeof d.removeListener=="function"&&d.removeListener("error",o),m([].slice.call(arguments))}$(d,u,y,{once:!0}),u!=="error"&&C(d,o,{once:!0})})}function C(d,u,m){typeof d.on=="function"&&$(d,"error",u,m)}function $(d,u,m,g){if(typeof d.on=="function")g.once?d.once(u,m):d.on(u,m);else if(typeof d.addEventListener=="function")d.addEventListener(u,function o(y){g.once&&d.removeEventListener(u,o),m(y)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof d)}return ae.exports}var Lt=kt();const Ce={BTU:{comment:"BTU displayed as BTU/h",units:"BTU/h"},kWh:{comment:"kWh displayed as kW",units:"kW"}};function Et(){function t(n,r,s){if(!Array.isArray(n)||!Array.isArray(r))throw new Error("Invalid input: timestamps and data must be arrays.");const a=[];for(let c=1;c<r.length;c++){const f=n[c]-n[c-1];if(f<=0)continue;const i=(r[c]-r[c-1])/f*s;a.push(i)}return a}function e(n){return n.units=Ce[n.units]?.units||n.units,n}return{rateConversions:Ce,convertArrayData:t,convertArrayHeaders:e}}const Ue={autoRefresh:!1,showExtends:!1,showLabelHeader:!1,oneSelection:!1,legendBehaviour:"Hover",unitSystem:"Metric",resolution:"Fine"},_e=L({...Ue}),ke=localStorage.getItem("trending-settings");_e.value=ke?JSON.parse(ke):{...Ue};N(_e,t=>{localStorage.setItem("trending-settings",JSON.stringify(t))},{deep:!0,immediate:!0});function re(){return{trendingSettings:_e}}const ve={default:{},alternate:{"°C":{units:"°F",scale:1.8,offset:32},"°F":{units:"°C",scale:5/9,offset:-32*5/9},"US GPM":{units:"LPM",scale:3.78541,offset:0},LPM:{units:"US GPM",scale:1/3.78541,offset:0},PSIG:{units:"kPa",scale:6.89476,offset:0},kPa:{units:"PSIG",scale:1/6.89476,offset:0},"BTU/h":{units:"kW",scale:.00029307107,offset:0},kW:{units:"BTU/h",scale:1/.00029307107,offset:0},BTU:{units:"kWh",scale:.000293071083,offset:0},kWh:{units:"BTU",scale:1/.000293071083,offset:0},HP:{units:"kW",scale:1/1.34102209,offset:0}},imperial:{"°C":{units:"°F",scale:1.8,offset:32}},metric:{"°F":{units:"°C",scale:.5555555555555571,offset:-17.77777777777778},"US GPM":{units:"LPM",scale:3.7854,offset:0},PSIG:{units:"KPa",scale:6.894757293168,offset:0},BTU:{units:"kWh",scale:.00029307108333333,offset:0},"BTU/h":{units:"kW",scale:.00029307107,offset:0}}};function Ie(){const{trendingSettings:t}=re();function e(r){const s=t.value.unitSystem.toLowerCase();return(ve[s]??{})[r]?.units??r}function n(r,s){const a=t.value.unitSystem.toLowerCase(),f=(ve[a]??{})[r];return f?s*f.scale+f.offset:s}return{unitConversions:ve,convertUnitLabels:e,convertUnitValue:n}}const Le=_t(),Ot=V(()=>Le.info?Le.info.trends:[]),{convertArrayData:At,convertArrayHeaders:Ee}=Et(),{convertUnitLabels:Dt,convertUnitValue:Pt}=Ie();function Ve(){function t(c){return!Array.isArray(c)||c.length===0?[]:c[0].map((f,p)=>c.map(i=>i[p]))}function e(c){const{header:{value:f},values:{value:p}}=c,i=[...p].sort((O,D)=>O[0]-D[0]),l=t(i),[h,...v]=l,_=h,R=f.map(O=>{const{cumulative:D}=O;return D?Ee({...O}):{...O}}),A=v.map((O,D)=>{const C=R[D],{cumulative:$,units:d}=C,u=O;return($?At(_,u,$):u).map(o=>Pt(d,o))});return t([_.slice(1),...A])}function n(c){return e(c).map(i=>[new Date(i[0]*1e3),...i.slice(1)])}function r(c){const{header:{value:f}}=c;return f.map(l=>{const{cumulative:h}=l;return h?Ee({...l}):{...l}}).map(l=>({...l,units:Dt(l.units)}))}function s(c){return["Date",...r(c).map(({device:i,register:l,units:h})=>`${i??"unknown"} - ${l??"unknown"}${h?` (${h})`:""}`)]}function a(c){const{header:{value:f}}=c;return f}return{trendTypes:Ot,getTrendData:n,getTrendLabels:s,getTrendRegisters:a}}var qt=function(){if(arguments.length===0)throw"Invalid invocation of Dygraph.synchronize(). Need >= 1 argument.";var e=["selection","zoom","range"],n={selection:!0,zoom:!0,range:!0},r=[],s=[],a=function(l){if(l instanceof Object)for(var h=0;h<e.length;h++){var v=e[h];l.hasOwnProperty(v)&&(n[v]=l[v])}else throw"Last argument must be either Dygraph or Object."};if(arguments[0]instanceof ee){for(var c=0;c<arguments.length&&arguments[c]instanceof ee;c++)r.push(arguments[c]);if(c<arguments.length-1)throw"Invalid invocation of Dygraph.synchronize(). All but the last argument must be Dygraph objects.";c==arguments.length-1&&a(arguments[arguments.length-1])}else if(arguments[0].length){for(var c=0;c<arguments[0].length;c++)r.push(arguments[0][c]);if(arguments.length==2)a(arguments[1]);else if(arguments.length>2)throw"Invalid invocation of Dygraph.synchronize(). Expected two arguments: array and optional options argument."}else throw"Invalid invocation of Dygraph.synchronize(). First parameter must be either Dygraph or list of Dygraphs.";if(r.length<2)throw"Invalid invocation of Dygraph.synchronize(). Need two or more dygraphs to synchronize.";for(var f=r.length,c=0;c<r.length;c++){var p=r[c];p.ready(function(){if(--f==0){for(var h=["drawCallback","highlightCallback","unhighlightCallback"],v=0;v<r.length;v++){s[v]||(s[v]={});for(var _=h.length-1;_>=0;_--)s[v][h[_]]=r[v].getFunctionOption(h[_])}n.zoom&&Mt(r,n,s),n.selection&&$t(r,s)}})}return{detach:function(){for(var l=0;l<r.length;l++){var h=r[l];n.zoom&&h.updateOptions({drawCallback:s[l].drawCallback}),n.selection&&h.updateOptions({highlightCallback:s[l].highlightCallback,unhighlightCallback:s[l].unhighlightCallback})}r=null,n=null,s=null}}};function Oe(t,e){if(!Array.isArray(t)||!Array.isArray(e))return!1;var n=t.length;if(n!==e.length)return!1;for(;n--;)if(t[n]!==e[n])return!1;return!0}function Mt(t,e,n){for(var r=!1,s=0;s<t.length;s++){var a=t[s];a.updateOptions({drawCallback:function(f,p){if(r||p){for(var i=0;i<t.length;i++)if(t[i]==f){n[i]&&n[i].drawCallback&&n[i].drawCallback.apply(this,arguments);break}return}r=!0;var l={dateWindow:f.xAxisRange()};f.isZoomed("x")||(l.dateWindow=null),e.range&&(l.valueRange=f.yAxisRange());for(var h=0;h<t.length;h++){if(t[h]==f){n[h]&&n[h].drawCallback&&n[h].drawCallback.apply(this,arguments);continue}Oe(l.dateWindow,t[h].getOption("dateWindow"))&&(!e.range||Oe(l.valueRange,t[h].getOption("valueRange")))||t[h].updateOptions(l)}r=!1}},!0)}}function $t(t,e){for(var n=!1,r=0;r<t.length;r++){var s=t[r];s.updateOptions({highlightCallback:function(c,f,p,i,l){if(!n){n=!0;for(var h=this,v=0;v<t.length;v++){if(h==t[v]){e[v]&&e[v].highlightCallback&&e[v].highlightCallback.apply(this,arguments);continue}var _=t[v].getRowForX(f);_!==null&&t[v].setSelection(_,l,void 0,!0)}n=!1}},unhighlightCallback:function(c){if(!n){n=!0;for(var f=this,p=0;p<t.length;p++){if(f==t[p]){e[p]&&e[p].unhighlightCallback&&e[p].unhighlightCallback.apply(this,arguments);continue}t[p].clearSelection()}n=!1}}},!0)}}ee.synchronize=qt;let Ne;const He=new Lt.EventEmitter,Te=Ve().trendTypes,Ut=V(()=>Te.value?.map(t=>({label:t.title,value:t.title}))),Be=L({}),ze="trendProperties",J=it({settings:{maxSelected:4,minHeight:"300px"},enabledTrendTypes:[],disabledRegisters:[],trendPropertiesMap:{},rangeSelector:{timeRange:{t0:null,t1:null},isRelative:null,date:null,timemode:null,timespan:null,anchor:null}});N(J,t=>{localStorage.setItem(ze,JSON.stringify({enabledTrendTypes:t.enabledTrendTypes,disabledRegisters:t.disabledRegisters,trendPropertiesMap:t.trendPropertiesMap})),Ne?.push({query:{...J.rangeSelector,timeRange:void 0,isRelative:void 0}})},{deep:!0});N(Te,t=>{J.enabledTrendTypes.length===0&&J.enabledTrendTypes.push(t[0].title)});let Ae;He.on("updateGraphInstance",()=>{clearTimeout(Ae);const t=Object.values(Be.value).filter(Boolean);t.length<2||(Ae=setTimeout(()=>ee.synchronize(t,{zoom:!0,selection:!0,range:!1}),100))});const F=(t=!0)=>{if(t){const e=ot();Ne=at(),Object.assign(J,{...JSON.parse(localStorage.getItem(ze)||"{}"),rangeSelector:{...e.query}})}return{trendEvents:He,trendProperties:J,trendTypes:Te,trendTypesOptions:Ut,dygraphInstances:Be}},It={class:"range-interact"},Vt={class:"range-label"},Nt={class:"user-interact"},Ht={class:"row items-center justify-end"},Bt={__name:"RangeSelector",setup(t){const{trendProperties:e,trendEvents:n,trendTypes:r}=F(),s={relative:[{id:"24-hours",label:"24 Hours",unit:"day",math:1},{id:"2-days",label:"2 Days",unit:"day",math:2},{id:"7-days",label:"7 Days",unit:"day",math:7},{id:"14-days",label:"14 Days",unit:"day",math:14}],absolute:[{id:"calendar-day",label:"Calendar Day",unit:"day",math:1},{id:"week-sun-sat",label:"Week Sun–Sat",unit:"week",math:1},{id:"week-mon-sun",label:"Week Mon–Sun",unit:"isoWeek",math:1},{id:"calendar-month",label:"Calendar Month",unit:"month",math:1},{id:"calendar-quarter",label:"Calendar Quarter",unit:"quarter",math:1},{id:"calendar-year",label:"Calendar Year",unit:"year",math:1}]},a=Object.freeze([{label:"Relative",disable:!0,class:"text-bold text-primary"},...s.relative.map(o=>({...o,type:"relative",value:o.id})),{label:"Absolute",disable:!0,class:"text-bold text-secondary"},...s.absolute.map(o=>({...o,type:"absolute",value:o.id}))]),c=Object.freeze([{label:"End On",value:"end"},{label:"Start On",value:"start"}]),f=L(q(e.rangeSelector.date)),p=V({get:()=>f.value,set:o=>{f.value=o,e.rangeSelector.date=f.value.format("YYYY-MM-DD")}}),i=e.rangeSelector?.timespan??null,l=e.rangeSelector?.anchor??null,h=L(a.find(o=>o?.value===i)||a[1]),v=L(c.find(o=>o?.value===l)||c[0]),_=V({get:()=>p.value.format("YYYY-MM-DD"),set:()=>p.value=q(e.rangeSelector.date)});lt(()=>e.rangeSelector.isRelative=h.value?.type==="relative");const R=V(()=>({unit:h.value.unit,value:h.value.math})),A=V(()=>{if(!e.rangeSelector.timeRange)return"‎";const{t0:o,t1:y}=e.rangeSelector.timeRange,b=q.unix(o),S=q.unix(y);if(!e.rangeSelector.isRelative)return`${b.format("YYYY-MM-DD")} - ${S.format("YYYY-MM-DD")}`;const k=q().startOf("day"),M=p.value.startOf("day").diff(k,"day");if(M>0)return"‎";switch(M){case 0:return"Today";case-1:return"Yesterday";case-7:return"Last Week";default:return p.value.from(k)}});function O(){p.value=q()}function D(){if(!e.rangeSelector.isRelative)return;const o=q();p.value=p.value.hour(o.hour()).minute(o.minute()).second(0)}function C(){e.rangeSelector.isRelative||(p.value=p.value.startOf(R.value.unit))}const $=()=>[{from:"isoWeek",to:"week"}].reduce((o,y)=>o.replace(y.from,y.to),R.value.unit);function d(o){const y=R.value.value,b=o?y:-y;p.value=p.value.add(b,$())}function u(){const o=!e.rangeSelector.isRelative||v.value.value==="start",y=p.value.unix(),b=$();o?e.rangeSelector.timeRange={t0:y,t1:p.value.add(R.value.value,b).unix()}:e.rangeSelector.timeRange={t0:p.value.subtract(R.value.value,b).unix(),t1:y},Object.assign(e.rangeSelector,{timemode:h.value.type,timespan:h.value.id,anchor:v.value.value})}function m(){C(),D(),u(),n.emit("RangeSelectorUpdate")}N([_,v,h],m,{immediate:!0}),N(()=>e.rangeSelector.date,o=>_.value=o);function g(o){o.key==="+"?d(!0):o.key==="-"&&d(!1)}return he(()=>{window.addEventListener("keydown",g),e.rangeSelector.date||O(),N([()=>e.enabledTrendTypes,()=>r.value],()=>setTimeout(m,250),{immediate:!0,deep:!0})}),te(()=>window.removeEventListener("keydown",g)),(o,y)=>(E(),U(G,null,[T(W,{modelValue:h.value,"onUpdate:modelValue":y[0]||(y[0]=b=>h.value=b),options:w(a),label:"Time Span",style:{width:"170px"}},null,8,["modelValue","options"]),P("div",It,[P("div",Vt,Q(A.value),1),P("div",Nt,[T(H,{onClick:y[1]||(y[1]=b=>d(!1)),class:"user-interact-button",padding:"xs",color:"primary",icon:"chevron_left"}),w(e).rangeSelector.isRelative?(E(),z(W,{key:0,dense:"",borderless:"",modelValue:v.value,"onUpdate:modelValue":y[2]||(y[2]=b=>v.value=b),options:w(c),style:{width:"110px",padding:"0 0.75em"}},null,8,["modelValue","options"])):ue("",!0),T(bt,{modelValue:w(e).rangeSelector.date,"onUpdate:modelValue":y[4]||(y[4]=b=>w(e).rangeSelector.date=b),filled:"",dense:"",style:{width:"140px"}},{append:x(()=>[T(Z,{name:"event",class:"cursor-pointer"},{default:x(()=>[T(yt,{cover:"","transition-show":"scale","transition-hide":"scale"},{default:x(()=>[T(vt,{modelValue:w(e).rangeSelector.date,"onUpdate:modelValue":y[3]||(y[3]=b=>w(e).rangeSelector.date=b),mask:"YYYY-MM-DD"},{default:x(()=>[P("div",Ht,[ut(T(H,{label:"Close",color:"primary",flat:""},null,512),[[wt]])])]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),T(H,{flat:"",class:"user-interact-today-button",padding:"xs",outline:"",label:"Today","no-caps":"",onClick:O}),T(H,{onClick:y[5]||(y[5]=b=>d(!0)),class:"user-interact-button",padding:"xs",color:"primary",icon:"chevron_right"})])])],64))}},zt=ne(Bt,[["__scopeId","data-v-efd2b1e3"]]),jt={class:"row items-center",style:{gap:"10px"}},Ft={class:"right"},Yt={__name:"GlobalPopup",props:ct({title:String,iconName:String,enabled:Boolean},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(t){const e=dt(t,"modelValue");return(n,r)=>(E(),z(xt,{modelValue:e.value,"onUpdate:modelValue":r[1]||(r[1]=s=>e.value=s),"backdrop-filter":"blur(1px)"},{default:x(()=>[T(Tt,{style:{width:"500px","max-width":"80vw"},class:"no-box-shadow"},{default:x(()=>[T(xe,{class:"row items-center q-pb-none text-h6",style:{"justify-content":"space-between"}},{default:x(()=>[P("div",jt,[T(Z,{size:"32px",name:t.iconName},null,8,["name"]),B(" "+Q(t.title),1)]),P("div",Ft,[T(Z,{class:"cursor-pointer",size:"32px",name:"close",onClick:r[0]||(r[0]=s=>e.value=!1)})])]),_:1}),T(xe,null,{default:x(()=>[ft(n.$slots,"default")]),_:3})]),_:3})]),_:3},8,["modelValue"]))}},De=pt({name:"QBanner",props:{...rt,inlineActions:Boolean,dense:Boolean,rounded:Boolean},setup(t,{slots:e}){const{proxy:{$q:n}}=ht(),r=st(t,n),s=V(()=>"q-banner row items-center"+(t.dense===!0?" q-banner--dense":"")+(r.value===!0?" q-banner--dark q-dark":"")+(t.rounded===!0?" rounded-borders":"")),a=V(()=>`q-banner__actions row items-center justify-end col-${t.inlineActions===!0?"auto":"all"}`);return()=>{const c=[oe("div",{class:"q-banner__avatar col-auto row items-center self-start"},ye(e.avatar)),oe("div",{class:"q-banner__content col text-body2"},ye(e.default))],f=ye(e.action);return f!==void 0&&c.push(oe("div",{class:a.value},f)),oe("div",{class:s.value+(t.inlineActions===!1&&f!==void 0?" q-banner--top-padding":""),role:"alert"},c)}}}),Wt={class:"column"},Qt={class:"interactions",style:{display:"flex","flex-direction":"column",gap:"10px"}},Gt={class:"interactions",style:{display:"flex","flex-direction":"column",gap:"10px"}},Jt={__name:"SettingsPopup",setup(t){const{trendProperties:e}=F(!1),{unitConversions:n}=Ie(),r={fine:1e4,coarse:2e3},s=["Follow","Never","Always","Hover"],a=Object.keys(n).map(i=>i.charAt(0).toUpperCase()+i.slice(1)),c=Object.entries(r).map(([i])=>i.charAt(0).toUpperCase()+i.slice(1)),{trendingSettings:f}=re();function p(i){const l=i.replace(/([A-Z])/g," $1");return l.charAt(0).toUpperCase()+l.slice(1)}return N(()=>e.enabledTrendTypes,i=>{i.forEach(l=>{const h=e.trendPropertiesMap[l]??={};h.fillGraph??=!1,h.showZero??=!1})},{immediate:!0}),(i,l)=>(E(),U("div",Wt,[T(De,{dense:"",rounded:""},{avatar:x(()=>[T(Z,{name:"warning",color:"orange"})]),default:x(()=>[l[3]||(l[3]=B(" Some settings changes may require a refresh to take full effect. "))]),_:1,__:[3]}),P("div",Qt,[T(W,{filled:"",modelValue:w(f).legendBehaviour,"onUpdate:modelValue":l[0]||(l[0]=h=>w(f).legendBehaviour=h),options:s,label:"Legend Cursor Show Behavior",color:"primary"},null,8,["modelValue"]),T(W,{filled:"",modelValue:w(f).unitSystem,"onUpdate:modelValue":l[1]||(l[1]=h=>w(f).unitSystem=h),options:w(a),label:"Unit System",color:"primary"},null,8,["modelValue","options"]),T(W,{filled:"",modelValue:w(f).resolution,"onUpdate:modelValue":l[2]||(l[2]=h=>w(f).resolution=h),options:w(c),label:"Resolution",color:"primary"},null,8,["modelValue","options"])]),T(De,{dense:"",rounded:""},{avatar:x(()=>[T(Z,{name:"build",color:"primary"})]),default:x(()=>[l[4]||(l[4]=B(" Current Trend Types Properties "))]),_:1,__:[4]}),P("div",Gt,[(E(!0),U(G,null,ce(Object.fromEntries(Object.entries(w(e).trendPropertiesMap).filter(([h])=>w(e).enabledTrendTypes.includes(h))),(h,v)=>(E(),U("div",{key:v,class:"interaction-trend-settings"},[P("strong",null,Q(v),1),(E(!0),U(G,null,ce(Object.entries(h),([_],R)=>(E(),z(Rt,{key:R,modelValue:h[_],"onUpdate:modelValue":A=>h[_]=A,color:"primary",label:p(_)},null,8,["modelValue","onUpdate:modelValue","label"]))),128))]))),128))])]))}},Xt=ne(Jt,[["__scopeId","data-v-10499ddf"]]);function Kt(t=document.documentElement){const e=L(!1),n=s=>{(s!==void 0?s:!e.value)?document.fullscreenElement||t.requestFullscreen?.():document.fullscreenElement&&document.exitFullscreen?.()},r=()=>{e.value=!!document.fullscreenElement,e.value?document.body.classList.add("full-screen"):document.body.classList.remove("full-screen")};return he(()=>{document.addEventListener("fullscreenchange",r),e.value=!!document.fullscreenElement}),te(()=>{document.removeEventListener("fullscreenchange",r)}),{isFullScreen:e,setFullScreen:n}}const Zt={class:"selectors"},en={class:"left"},tn={class:"selector"},nn={class:"right"},rn={__name:"GraphSelectorsHeader",setup(t){const{trendProperties:e,trendTypesOptions:n}=F(),{trendingSettings:r}=re(),s=p=>{const{enabledTrendTypes:i,settings:l}=e;if(r.value.oneSelection){e.enabledTrendTypes=[p];return}const h=l?.maxSelected;i.includes(p)?e.enabledTrendTypes=i.filter(v=>v!==p):(!h||i.length<h)&&(e.enabledTrendTypes=[...i,p])},a=()=>{r.value.oneSelection=!r.value.oneSelection,r.value.oneSelection&&(e.enabledTrendTypes=e.enabledTrendTypes.slice(-1))},c=Kt(),f=L(!1);return(p,i)=>(E(),U(G,null,[P("div",Zt,[P("div",en,[P("div",tn,[T(W,{"model-value":w(e).enabledTrendTypes.join(", "),"display-value":w(e).enabledTrendTypes.join(", "),label:"Trend Type","transition-show":"jump-up","transition-hide":"jump-up",options:w(n),style:{width:"250px"},multiple:""},{option:x(({opt:l})=>[T(tt,{dense:"",clickable:"",class:de(["q-mx-xs q-my-xs rounded-borders",w(e).enabledTrendTypes.includes(l.value)?"bg-primary text-white":""]),onClick:h=>s(l.value)},{default:x(()=>[T(nt,null,{default:x(()=>[B(Q(l.label),1)]),_:2},1024)]),_:2},1032,["class","onClick"])]),_:1},8,["model-value","display-value","options"]),T(zt),T(H,{onClick:i[0]||(i[0]=()=>{w(r).autoRefresh=!w(r).autoRefresh}),style:{"margin-top":"15px"},class:de(["user-interact-button",w(r).autoRefresh&&"animated-border"]),padding:"xs",icon:"refresh",rounded:"",size:"14px"},{default:x(()=>[T(se,{class:"bg-primary"},{default:x(()=>i[6]||(i[6]=[B("Auto Refresh")])),_:1,__:[6]})]),_:1},8,["class"]),T(H,{color:w(r).oneSelection?"primary":void 0,onClick:a,style:{"margin-top":"15px"},class:"user-interact-button",icon:"looks_one",rounded:"",size:"14px"},{default:x(()=>[T(se,{class:"bg-primary"},{default:x(()=>i[7]||(i[7]=[B("One Selection")])),_:1,__:[7]})]),_:1},8,["color"]),T(H,{color:w(r).showExtends?"primary":void 0,onClick:i[1]||(i[1]=l=>w(r).showExtends=!w(r).showExtends),style:{"margin-top":"15px"},class:"user-interact-button",icon:"straighten",rounded:"",size:"14px"},{default:x(()=>[T(se,{class:"bg-primary"},{default:x(()=>i[8]||(i[8]=[B("Show Extends")])),_:1,__:[8]})]),_:1},8,["color"]),T(H,{color:w(r).showLabelHeader?"primary":void 0,onClick:i[2]||(i[2]=l=>w(r).showLabelHeader=!w(r).showLabelHeader),style:{"margin-top":"15px"},class:"user-interact-button",icon:"filter_alt",rounded:"",size:"14px"},{default:x(()=>[T(se,{class:"bg-primary"},{default:x(()=>i[9]||(i[9]=[B("Show Registers Filter")])),_:1,__:[9]})]),_:1},8,["color"])])]),P("div",nn,[T(H,{onClick:i[3]||(i[3]=l=>f.value=!f.value),style:{"margin-top":"15px"},class:"user-interact-button",padding:"xs",color:"primary",icon:"settings"}),T(H,{onClick:i[4]||(i[4]=l=>w(c).setFullScreen()),style:{"margin-top":"15px"},class:"user-interact-button",padding:"xs",color:"primary",icon:"fullscreen"})])]),T(Yt,{modelValue:f.value,"onUpdate:modelValue":i[5]||(i[5]=l=>f.value=l),title:"Settings",iconName:"settings"},{default:x(()=>[T(Xt)]),_:1},8,["modelValue"])],64))}},sn=ne(rn,[["__scopeId","data-v-0299213d"]]),on={class:"header-labels"},an={__name:"GraphHeaderLabels",props:{colorsMap:{type:Object},registers:{type:Array},rawData:{type:Array},visibleXRange:{type:Object}},setup(t){const{trendProperties:e}=F(),n=$e(),r=p=>Array.isArray(e.disabledRegisters)&&e.disabledRegisters.includes(p),s=p=>p.replace(/\s*\(.*?\)\s*$/,"").trim(),a=t,c=V(()=>{function p(l){return!Array.isArray(l)||l.length===0?[]:l[0].map((h,v)=>l.map(_=>_[v]))}function i(l,h){let v=0,_=1/0;for(let R=0;R<l.length;R++){const A=Math.abs(l[R]-h);A<_&&(_=A,v=R)}return v}return l=>{if(a.registers[l].cumulative&&a.visibleXRange){const[h,v]=a.visibleXRange,_=p(a.rawData),R=_[0].map(u=>u.valueOf()*1e3),A=_[l+1],O=i(R,h),D=i(R,v),C=A[O];return`${(A[D]-C).toFixed(a.registers[l].display)} (${a.registers[l].units})`}return null}});function f(p){e.disabledRegisters.includes(p)?e.disabledRegisters=e.disabledRegisters.filter(i=>i!==p):e.disabledRegisters=[...e.disabledRegisters,p]}return(p,i)=>(E(),U("div",on,[(E(!0),U(G,null,ce(t.colorsMap,(l,h,v)=>(E(),z(St,{key:h,onClick:()=>f(h),class:de(`legend-item ${r(h)?"disabled-item":""} non-selectable`),style:fe({borderColor:l,color:r(h)?l:w(n).dark.isActive?"black":"white",backgroundColor:l}),outline:"",clickable:""},{default:x(()=>[P("span",{class:"legend-color-box",style:fe({borderColor:l})},null,4),B(" "+Q(s(h))+" "+Q(c.value(v)),1)]),_:2},1032,["onClick","class","style"]))),128))]))}},ln=ne(an,[["__scopeId","data-v-b4842133"]]);function un(){const t=L(0),e=L(0),n=r=>{t.value=r.clientX,e.value=r.clientY};return he(()=>window.addEventListener("mousemove",n)),te(()=>window.removeEventListener("mousemove",n)),{x:t,y:e}}class Pe extends Error{response;request;options;constructor(e,n,r){const s=e.status||e.status===0?e.status:"",a=e.statusText||"",c=`${s} ${a}`.trim(),f=c?`status code ${c}`:"an unknown error";super(`Request failed with ${f}: ${n.method} ${n.url}`),this.name="HTTPError",this.response=e,this.request=n,this.options=r}}class je extends Error{request;constructor(e){super(`Request timed out: ${e.method} ${e.url}`),this.name="TimeoutError",this.request=e}}const qe=(()=>{let t=!1,e=!1;const n=typeof globalThis.ReadableStream=="function",r=typeof globalThis.Request=="function";if(n&&r)try{e=new globalThis.Request("https://empty.invalid",{body:new globalThis.ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type")}catch(s){if(s instanceof Error&&s.message==="unsupported BodyInit type")return!1;throw s}return t&&!e})(),cn=typeof globalThis.AbortController=="function",dn=typeof globalThis.ReadableStream=="function",fn=typeof globalThis.FormData=="function",Fe=["get","post","put","patch","head","delete"],pn={json:"application/json",text:"text/*",formData:"multipart/form-data",arrayBuffer:"*/*",blob:"*/*"},be=2147483647,hn=new TextEncoder().encode("------WebKitFormBoundaryaxpyiPgbbPti10Rw").length,Ye=Symbol("stop"),mn={json:!0,parseJson:!0,stringifyJson:!0,searchParams:!0,prefixUrl:!0,retry:!0,timeout:!0,hooks:!0,throwHttpErrors:!0,onDownloadProgress:!0,onUploadProgress:!0,fetch:!0},gn={method:!0,headers:!0,body:!0,mode:!0,credentials:!0,cache:!0,redirect:!0,referrer:!0,referrerPolicy:!0,integrity:!0,keepalive:!0,signal:!0,window:!0,dispatcher:!0,duplex:!0,priority:!0},yn=t=>{if(!t)return 0;if(t instanceof FormData){let e=0;for(const[n,r]of t)e+=hn,e+=new TextEncoder().encode(`Content-Disposition: form-data; name="${n}"`).length,e+=typeof r=="string"?new TextEncoder().encode(r).length:r.size;return e}if(t instanceof Blob)return t.size;if(t instanceof ArrayBuffer)return t.byteLength;if(typeof t=="string")return new TextEncoder().encode(t).length;if(t instanceof URLSearchParams)return new TextEncoder().encode(t.toString()).length;if("byteLength"in t)return t.byteLength;if(typeof t=="object"&&t!==null)try{const e=JSON.stringify(t);return new TextEncoder().encode(e).length}catch{return 0}return 0},vn=(t,e)=>{const n=Number(t.headers.get("content-length"))||0;let r=0;return t.status===204?(e&&e({percent:1,totalBytes:n,transferredBytes:r},new Uint8Array),new Response(null,{status:t.status,statusText:t.statusText,headers:t.headers})):new Response(new ReadableStream({async start(s){const a=t.body.getReader();e&&e({percent:0,transferredBytes:0,totalBytes:n},new Uint8Array);async function c(){const{done:f,value:p}=await a.read();if(f){s.close();return}if(e){r+=p.byteLength;const i=n===0?0:r/n;e({percent:i,transferredBytes:r,totalBytes:n},p)}s.enqueue(p),await c()}await c()}}),{status:t.status,statusText:t.statusText,headers:t.headers})},bn=(t,e)=>{const n=yn(t.body);let r=0;return new Request(t,{duplex:"half",body:new ReadableStream({async start(s){const a=t.body instanceof ReadableStream?t.body.getReader():new Response("").body.getReader();async function c(){const{done:f,value:p}=await a.read();if(f){e&&e({percent:1,transferredBytes:r,totalBytes:Math.max(n,r)},new Uint8Array),s.close();return}r+=p.byteLength;let i=n===0?0:r/n;(n<r||i===1)&&(i=.99),e&&e({percent:Number(i.toFixed(2)),transferredBytes:r,totalBytes:n},p),s.enqueue(p),await c()}await c()}})})},K=t=>t!==null&&typeof t=="object",ie=(...t)=>{for(const e of t)if((!K(e)||Array.isArray(e))&&e!==void 0)throw new TypeError("The `options` argument must be an object");return Se({},...t)},We=(t={},e={})=>{const n=new globalThis.Headers(t),r=e instanceof globalThis.Headers,s=new globalThis.Headers(e);for(const[a,c]of s.entries())r&&c==="undefined"||c===void 0?n.delete(a):n.set(a,c);return n};function le(t,e,n){return Object.hasOwn(e,n)&&e[n]===void 0?[]:Se(t[n]??[],e[n]??[])}const Qe=(t={},e={})=>({beforeRequest:le(t,e,"beforeRequest"),beforeRetry:le(t,e,"beforeRetry"),afterResponse:le(t,e,"afterResponse"),beforeError:le(t,e,"beforeError")}),Se=(...t)=>{let e={},n={},r={};for(const s of t)if(Array.isArray(s))Array.isArray(e)||(e=[]),e=[...e,...s];else if(K(s)){for(let[a,c]of Object.entries(s))K(c)&&a in e&&(c=Se(e[a],c)),e={...e,[a]:c};K(s.hooks)&&(r=Qe(r,s.hooks),e.hooks=r),K(s.headers)&&(n=We(n,s.headers),e.headers=n)}return e},wn=t=>Fe.includes(t)?t.toUpperCase():t,_n=["get","put","head","delete","options","trace"],Tn=[408,413,429,500,502,503,504],Sn=[413,429,503],Me={limit:2,methods:_n,statusCodes:Tn,afterStatusCodes:Sn,maxRetryAfter:Number.POSITIVE_INFINITY,backoffLimit:Number.POSITIVE_INFINITY,delay:t=>.3*2**(t-1)*1e3},xn=(t={})=>{if(typeof t=="number")return{...Me,limit:t};if(t.methods&&!Array.isArray(t.methods))throw new Error("retry.methods must be an array");if(t.statusCodes&&!Array.isArray(t.statusCodes))throw new Error("retry.statusCodes must be an array");return{...Me,...t}};async function Rn(t,e,n,r){return new Promise((s,a)=>{const c=setTimeout(()=>{n&&n.abort(),a(new je(t))},r.timeout);r.fetch(t,e).then(s).catch(a).then(()=>{clearTimeout(c)})})}async function Cn(t,{signal:e}){return new Promise((n,r)=>{e&&(e.throwIfAborted(),e.addEventListener("abort",s,{once:!0}));function s(){clearTimeout(a),r(e.reason)}const a=setTimeout(()=>{e?.removeEventListener("abort",s),n()},t)})}const kn=(t,e)=>{const n={};for(const r in e)!(r in gn)&&!(r in mn)&&!(r in t)&&(n[r]=e[r]);return n};class pe{static create(e,n){const r=new pe(e,n),s=async()=>{if(typeof r._options.timeout=="number"&&r._options.timeout>be)throw new RangeError(`The \`timeout\` option cannot be greater than ${be}`);await Promise.resolve();let f=await r._fetch();for(const p of r._options.hooks.afterResponse){const i=await p(r.request,r._options,r._decorateResponse(f.clone()));i instanceof globalThis.Response&&(f=i)}if(r._decorateResponse(f),!f.ok&&r._options.throwHttpErrors){let p=new Pe(f,r.request,r._options);for(const i of r._options.hooks.beforeError)p=await i(p);throw p}if(r._options.onDownloadProgress){if(typeof r._options.onDownloadProgress!="function")throw new TypeError("The `onDownloadProgress` option must be a function");if(!dn)throw new Error("Streams are not supported in your environment. `ReadableStream` is missing.");return vn(f.clone(),r._options.onDownloadProgress)}return f},c=(r._options.retry.methods.includes(r.request.method.toLowerCase())?r._retry(s):s()).finally(async()=>{r.request.bodyUsed||await r.request.body?.cancel()});for(const[f,p]of Object.entries(pn))c[f]=async()=>{r.request.headers.set("accept",r.request.headers.get("accept")||p);const i=await c;if(f==="json"){if(i.status===204||(await i.clone().arrayBuffer()).byteLength===0)return"";if(n.parseJson)return n.parseJson(await i.text())}return i[f]()};return c}request;abortController;_retryCount=0;_input;_options;constructor(e,n={}){if(this._input=e,this._options={...n,headers:We(this._input.headers,n.headers),hooks:Qe({beforeRequest:[],beforeRetry:[],beforeError:[],afterResponse:[]},n.hooks),method:wn(n.method??this._input.method??"GET"),prefixUrl:String(n.prefixUrl||""),retry:xn(n.retry),throwHttpErrors:n.throwHttpErrors!==!1,timeout:n.timeout??1e4,fetch:n.fetch??globalThis.fetch.bind(globalThis)},typeof this._input!="string"&&!(this._input instanceof URL||this._input instanceof globalThis.Request))throw new TypeError("`input` must be a string, URL, or Request");if(this._options.prefixUrl&&typeof this._input=="string"){if(this._input.startsWith("/"))throw new Error("`input` must not begin with a slash when using `prefixUrl`");this._options.prefixUrl.endsWith("/")||(this._options.prefixUrl+="/"),this._input=this._options.prefixUrl+this._input}if(cn){const r=this._options.signal??this._input.signal;this.abortController=new globalThis.AbortController,this._options.signal=r?AbortSignal.any([r,this.abortController.signal]):this.abortController.signal}if(qe&&(this._options.duplex="half"),this._options.json!==void 0&&(this._options.body=this._options.stringifyJson?.(this._options.json)??JSON.stringify(this._options.json),this._options.headers.set("content-type",this._options.headers.get("content-type")??"application/json")),this.request=new globalThis.Request(this._input,this._options),this._options.searchParams){const s="?"+(typeof this._options.searchParams=="string"?this._options.searchParams.replace(/^\?/,""):new URLSearchParams(this._options.searchParams).toString()),a=this.request.url.replace(/(?:\?.*?)?(?=#|$)/,s);(fn&&this._options.body instanceof globalThis.FormData||this._options.body instanceof URLSearchParams)&&!(this._options.headers&&this._options.headers["content-type"])&&this.request.headers.delete("content-type"),this.request=new globalThis.Request(new globalThis.Request(a,{...this.request}),this._options)}if(this._options.onUploadProgress){if(typeof this._options.onUploadProgress!="function")throw new TypeError("The `onUploadProgress` option must be a function");if(!qe)throw new Error("Request streams are not supported in your environment. The `duplex` option for `Request` is not available.");this.request.body&&(this.request=bn(this.request,this._options.onUploadProgress))}}_calculateRetryDelay(e){if(this._retryCount++,this._retryCount>this._options.retry.limit||e instanceof je)throw e;if(e instanceof Pe){if(!this._options.retry.statusCodes.includes(e.response.status))throw e;const r=e.response.headers.get("Retry-After")??e.response.headers.get("RateLimit-Reset")??e.response.headers.get("X-RateLimit-Reset")??e.response.headers.get("X-Rate-Limit-Reset");if(r&&this._options.retry.afterStatusCodes.includes(e.response.status)){let s=Number(r)*1e3;Number.isNaN(s)?s=Date.parse(r)-Date.now():s>=Date.parse("2024-01-01")&&(s-=Date.now());const a=this._options.retry.maxRetryAfter??s;return s<a?s:a}if(e.response.status===413)throw e}const n=this._options.retry.delay(this._retryCount);return Math.min(this._options.retry.backoffLimit,n)}_decorateResponse(e){return this._options.parseJson&&(e.json=async()=>this._options.parseJson(await e.text())),e}async _retry(e){try{return await e()}catch(n){const r=Math.min(this._calculateRetryDelay(n),be);if(this._retryCount<1)throw n;await Cn(r,{signal:this._options.signal});for(const s of this._options.hooks.beforeRetry)if(await s({request:this.request,options:this._options,error:n,retryCount:this._retryCount})===Ye)return;return this._retry(e)}}async _fetch(){for(const r of this._options.hooks.beforeRequest){const s=await r(this.request,this._options);if(s instanceof Request){this.request=s;break}if(s instanceof Response)return s}const e=kn(this.request,this._options),n=this.request;return this.request=n.clone(),this._options.timeout===!1?this._options.fetch(n,e):Rn(n,e,this.abortController,this._options)}}/*! MIT License © Sindre Sorhus */const we=t=>{const e=(n,r)=>pe.create(n,ie(t,r));for(const n of Fe)e[n]=(r,s)=>pe.create(r,ie(t,s,{method:n}));return e.create=n=>we(ie(n)),e.extend=n=>(typeof n=="function"&&(n=n(t??{})),we(ie(t,n))),e.stop=Ye,e},Ln=we(),En=`${Ct}/trends`;function Ge(){const t=L([]),e=L([]),n=L(!1),r=L(null);let s=null;var a=!0;function c(i,l){const h=new TextDecoder;let v="";async function _(){for(;;){const{done:R,value:A}=await i.read();if(R){n.value=!1;break}v+=h.decode(A,{stream:!0});const O=v.split(`
`);v=O.pop();for(const D of O)if(D.trim())try{const C=JSON.parse(D);l(C)}catch(C){console.error("JSON parse error:",C,D)}}}return _()}const f=async i=>{if(i){n.value=!0,a=!0,t.value=[],e.value=[],s=new AbortController;try{const h=(await Ln.get(En,{searchParams:i,signal:s.signal})).body.getReader();c(h,v=>{a?(t.value=v,a=!1):e.value.push(v)})}catch(l){l.name==="AbortError"?console.warn("Stream was aborted."):console.error("Streaming failed:",l)}}},p=()=>{s&&(s.abort(),n.value=!1)};return te(()=>{p()}),{header:t,values:e,error:r,isStreaming:n,startStreaming:f,stopStreaming:p}}const On={class:"parent"},An={key:0,class:"error-message"},Dn={__name:"TrendingDyGraph",props:{trendType:{type:String,required:!0},data:{type:Array,required:!0},rawData:{type:Array,required:!0},labels:{type:Array,required:!0},registers:{type:Array,required:!0},update:{type:Number},timeRange:{type:Object,required:!0}},setup(t){const e=$e(),{trendProperties:n,dygraphInstances:r,trendEvents:s}=F(),a=V(()=>n.trendPropertiesMap[v.trendType]),c=L(null),f=L(null),p=L([]),{trendingSettings:i}=re(),{isStreaming:l}=Ge(),h=L([0,0]),v=t;function _(g){const y=n.disabledRegisters.map(S=>v.labels.findIndex(k=>k===S));return g.map(S=>S.map((k,M)=>y.includes(M)?null:k))}function R(g){let o={...v.timeRange},y=[...g];if(q(y[0][0]).unix()!==o.start){const S=[q.unix(o.t0).toDate(),...new Array(y[0].length-1).fill(null)];y.unshift(S)}const b=y.length-1;if(q(y[b][0]).unix()!==o.end){const S=[q.unix(o.t1).toDate(),...new Array(y[0].length-1).fill(null)];y.push(S)}return y}function A(g){return`
    <table style="width: 100%; border-collapse: collapse; font-family: sans-serif; font-size: 14px;">
        <thead>
            <tr>
                <th colspan="3" style="text-align: center; padding: 8px; font-weight: bold;">
                    ${q(g.x).format("YYYY-MM-DD (ddd) HH:mm:ss")}
                </th>
            </tr>
        </thead>
        <tbody>
            ${g.series.map(o=>{const y=o.yHTML?Number(o.yHTML).toLocaleString("fullwide",{useGrouping:!1}):"",b=o.label.split("(");let S=o.label;if(n.disabledRegisters.includes(S))return;let k="";return b.length>1&&(S=b[0].trim(),k=b.slice(1).join("(").replace(/\)$/,"")),`
                <tr>
                    <td style="padding: 6px 8px; display: flex; align-items: center;">
                        <span style="display: inline-block; width: 12px; height: 12px; background-color: ${o.color}; margin-right: 8px;"></span>
                        ${S}
                    </td>
                    <td style="padding: 6px 8px; text-align: right;">${y}</td>
                    <td style="padding: 6px 8px;">${k}</td>
                </tr>
                `}).join("")}
        </tbody>
    </table>
    `}function O(g,o,y){if(g.fillStyle="rgba(42,129,219, 0.1)",!!(y.rawData_&&y.rawData_.length>0))for(var b=y.getValue(0,0),S=y.getValue(y.numRows()-1,0),k=q(b).startOf("day").valueOf();k<S;){var M=q(k),Y=M.add(1,"days"),X=M.day();if(X==6||X==0){var I=y.toDomXCoord(M.valueOf()),j=y.toDomXCoord(Y.valueOf()),me=j-I;g.fillRect(I,o.y,me,o.h)}k=Y.valueOf()}}function D(g,o){return Array.from({length:g},(y,b)=>{const S=360/g*b,k=65+b%3*5,M=o?65+b%2*5:40+b%2*5,Y=k/100,X=M/100,I=(1-Math.abs(2*X-1))*Y,j=I*(1-Math.abs(S/60%2-1)),me=X-I/2;let[Je,Xe,Ke]=S<60?[I,j,0]:S<120?[j,I,0]:S<180?[0,I,j]:S<240?[0,j,I]:S<300?[j,0,I]:[I,0,j];const ge=Ze=>Math.round((Ze+me)*255).toString(16).padStart(2,"0");return`#${ge(Je)}${ge(Xe)}${ge(Ke)}`})}let C=null;function $(){if(!c.value||v.data.length<=0){C&&(C.destroy(),C=null,p.value=[]);return}C&&(C.destroy(),C=null);let g=_(v.data);if(i.value.showExtends&&(g=R(g)),g.length<=0)return;c.value.innerHTML="",f.value.innerHTML="";const o=i.value.legendBehaviour.toLowerCase();C=new ee(c.value,g,{labels:v.labels,labelsSeparateLines:!0,labelsDiv:o!=="follow"?f.value:0,legendFormatter:A,legend:o,underlayCallback:O,colors:D(v.labels.length,e.dark.isActive),resizable:"both",fillGraph:!!a.value?.fillGraph,includeZero:!!a.value?.showZero,drawCallback:y=>{h.value=y.xAxisRange()}}),p.value=C.colorsMap_,r.value[v.trendType]=C,s.emit("updateGraphInstance")}N(()=>[v.update,n.disabledRegisters,e.dark.isActive],()=>{$()}),N(()=>[i.value,n.trendPropertiesMap],()=>{$()},{deep:!0});const d=L(!1),{x:u}=un(),m=L(!1);return N(()=>u.value,()=>{const g=window.innerWidth;m.value=u.value>=g/2}),te(()=>{C&&(C.destroy(),C=null,r.value[v.trendType]=null)}),(g,o)=>(E(),U("div",On,[w(C)==null&&w(l)===!1?(E(),U("div",An," No data Available ")):ue("",!0),w(l)===!0?(E(),z(mt,{key:1,color:"primary",size:"150px",class:"spinner"})):ue("",!0),T(ln,{style:fe({display:w(i).showLabelHeader?"flex":"none"}),colorsMap:p.value,registers:v.registers,visibleXRange:h.value,rawData:t.rawData},null,8,["style","colorsMap","registers","visibleXRange","rawData"]),P("div",{onMouseenter:o[0]||(o[0]=y=>d.value=!0),onMouseleave:o[1]||(o[1]=y=>d.value=!1),class:"parent-chart relative-position"},[P("div",{id:"div_g",ref_key:"chartRef",ref:c,class:"chart-container"},null,512),P("div",{ref_key:"chartLegendRef",ref:f,class:de(["chart-legend",{left:m.value}])},null,2)],32)]))}},Pn={__name:"TrendGraph",props:{trendType:{type:String,required:!0}},setup(t){const{trendProperties:e,trendEvents:n,trendTypes:r}=F(),{trendingSettings:s}=re(),a=t,c=V(()=>r.value.findIndex(_=>_.title===a.trendType)),f=Ge(),p=Ve(),i=L(0),l=L({data:[],labels:[],registers:[]}),h=()=>{const _=f.values.value.length>0;l.value={data:_?p.getTrendData(f):[],labels:_?p.getTrendLabels(f):[],registers:_?p.getTrendRegisters(f):[]},i.value++};function v(){if(e.rangeSelector.timeRange===void 0)return;const _={t0:e.rangeSelector.timeRange.t0,t1:e.rangeSelector.timeRange.t1,type:c.value,resolution:s.value.resolution.toLowerCase()??"fine"};f.stopStreaming(),f.startStreaming(_)}return he(()=>{n.on("RangeSelectorUpdate",v)}),gt(()=>{n.off("RangeSelectorUpdate",v)}),N(()=>f.values.value.length,h),(_,R)=>(E(),z(Dn,{"trend-type":t.trendType,data:l.value.data,labels:l.value.labels,registers:l.value.registers,"time-range":w(e).rangeSelector?.timeRange||{},update:i.value,"raw-data":w(f).values.value},null,8,["trend-type","data","labels","registers","time-range","update","raw-data"]))}},qn={__name:"TrendingComponent",setup(t){const{trendProperties:e,trendTypes:n}=F();return(r,s)=>(E(),z(et,{style:{display:"flex","flex-direction":"column"}},{default:x(()=>[T(sn),w(n).length>0?(E(),U("div",{key:0,class:"trending-parent",style:fe({"--min-height":w(e).settings.minHeight})},[(E(!0),U(G,null,ce(w(e).enabledTrendTypes,a=>(E(),z(Pn,{key:a+"-"+JSON.stringify(w(e).enabledTrendTypes),"trend-type":a},null,8,["trend-type"]))),128))],4)):ue("",!0)]),_:1}))}},Mn=ne(qn,[["__scopeId","data-v-16d85c96"]]),rr={__name:"TrendsPage",setup(t){return(e,n)=>(E(),z(Mn))}};export{rr as default};
