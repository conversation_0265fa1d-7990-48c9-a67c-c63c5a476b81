import{i as E,r as u,A as H,f as L,B as T,T as k,F as b,X as t,I as _,J as o,D as R,G as M,H as P,O as S}from"./index-CzmOWWdj.js";import{Q as D}from"./QPage-Disdm55v.js";import{_ as V}from"./PageHeading-CorBRYVa.js";import{a as A}from"./Login-BVtZ5S7B.js";import"./QSpace-i2TdLNVM.js";const J={id:"app",class:"svg-info"},Q={class:"container-outer"},W=["innerHTML"],j={class:"info"},z={__name:"SvgInfo",setup(B){const d=E("$api"),c=u(null),r=u("None"),l=u(null),m=u(`
	<div class="box">
		Box 1
		<div class="nested">Nested A</div>
		<div class="nested">Nested B</div>
	</div>
	<div class="box">Box 2</div>
	<button class="nested">A Button</button>
`),e=H({centerX:"",centerY:"",centerXPercent:"",centerYPercent:"",mouseX:"",mouseY:"",mouseXPercent:"",mouseYPercent:"",width:"",height:"",widthPercent:"",heightPercent:"",aspectRatio:""});function y(s){const n=c.value.getBoundingClientRect(),a=n.width,v=n.height,x=s.clientX-n.left,w=s.clientY-n.top,$=(x/a*100).toFixed(2)+"%",C=(w/v*100).toFixed(2)+"%",i=document.elementFromPoint(s.clientX,s.clientY);if(c.value.contains(i)&&i!==c.value){l.value&&l.value!==i&&l.value.classList.remove("highlighted"),l.value=i,i.classList.add("highlighted"),r.value=`<${i.tagName.toLowerCase()}> ${i.className.baseVal||i.className||""}`;const h=i.getBoundingClientRect(),p=h.width,g=h.height,X=h.left+p/2-n.left,Y=h.top+g/2-n.top,F=(X/a*100).toFixed(2)+"%",N=(Y/v*100).toFixed(2)+"%";e.width=Math.round(p)+"px",e.height=Math.round(g)+"px",e.widthPercent=(p/a*100).toFixed(2)+"%",e.heightPercent=(g/v*100).toFixed(2)+"%",e.centerX=Math.round(X)+"px",e.centerY=Math.round(Y)+"px",e.centerXPercent=F,e.centerYPercent=N}else l.value&&(l.value.classList.remove("highlighted"),l.value=null),r.value="None",e.centerX="",e.centerY="",e.centerXPercent="",e.centerYPercent="",e.width="",e.height="",e.widthPercent="",e.heightPercent="",e.aspectRatio=(n.height/n.width*100).toFixed(2)+"%";e.mouseX=Math.round(x)+"px",e.mouseY=Math.round(w)+"px",e.mouseXPercent=$,e.mouseYPercent=C}function f(s){if(s.code==="Space"&&e.centerXPercent&&e.centerYPercent){const n=`${e.centerXPercent}, ${e.centerYPercent}`.replace(/%/g,"");navigator.clipboard.writeText(n).then(()=>{console.log(`Copied to clipboard: ${n}`)}).catch(a=>{console.error("Failed to copy:",a)}),s.preventDefault()}}return L(()=>{window.addEventListener("keydown",f),d.get("/dashboard/bg.svg").then(s=>{m.value=s.data})}),T(()=>{window.removeEventListener("keydown",f)}),(s,n)=>(b(),k("div",J,[t("div",Q,[t("div",{ref_key:"container",ref:c,innerHTML:m.value,class:"container",onMousemove:y},null,40,W)]),t("div",j,[t("p",null,[n[0]||(n[0]=t("strong",null,"Hovered:",-1)),_(" "+o(r.value),1)]),t("table",null,[n[3]||(n[3]=t("thead",null,[t("tr",null,[t("th"),t("th",null,"Element Center (px)"),t("th",null,"Element Center (%)"),t("th",null,"Mouse Position (px)"),t("th",null,"Mouse Position (%)"),t("th",null,"Element Size (px)"),t("th",null,"Element Size (%)")])],-1)),t("tbody",null,[t("tr",null,[n[1]||(n[1]=t("th",null,"X",-1)),t("td",null,o(e.centerX),1),t("td",null,o(e.centerXPercent),1),t("td",null,o(e.mouseX),1),t("td",null,o(e.mouseXPercent),1),t("td",null,o(e.width),1),t("td",null,o(e.widthPercent),1)]),t("tr",null,[n[2]||(n[2]=t("th",null,"Y",-1)),t("td",null,o(e.centerY),1),t("td",null,o(e.centerYPercent),1),t("td",null,o(e.mouseY),1),t("td",null,o(e.mouseYPercent),1),t("td",null,o(e.height),1),t("td",null,o(e.heightPercent),1)])])]),t("p",null,[n[4]||(n[4]=t("strong",null,"Container Aspect Ratio (H/W):",-1)),_(" "+o(e.aspectRatio),1)])])]))}},G={class:"a-container-lg"},Z={__name:"DebugPage",setup(B){const d=u(null);function c(){A.get("/login/test").then(r=>{d.value=r.data,console.log(r.data)})}return(r,l)=>(b(),R(D,null,{default:M(()=>[t("div",G,[P(V,{title:"SVG Helper",icon:"image"},{default:M(()=>l[1]||(l[1]=[t("div",{class:"q-gutter-sm"},null,-1)])),_:1,__:[1]}),P(z),P(S,{color:"primary",label:"Test JWT",onClick:l[0]||(l[0]=m=>c())}),t("pre",null,o(d.value),1),l[2]||(l[2]=t("div",{style:{height:"25vh"}},null,-1))])]),_:1}))}};export{Z as default};
