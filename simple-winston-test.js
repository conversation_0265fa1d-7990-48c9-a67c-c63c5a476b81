const winston = require('winston');
const fs = require('fs');

console.log('Testing Winston file transport...');

const logger = winston.createLogger({
    transports: [
        new winston.transports.File({
            filename: './logs/simple-test.log',
            maxsize: 500,
            maxFiles: 2,
            tailable: true,
            format: winston.format.combine(
                winston.format.timestamp(),
                winston.format.json()
            )
        }),
        new winston.transports.Console()
    ]
});

// Test logging
for(let i = 0; i < 10; i++) {
    logger.info(`Test message ${i} - this is a longer message to trigger rotation faster and see how the files are created`);
}

// Wait for async operations
setTimeout(() => {
    console.log('Files created:', fs.readdirSync('./logs').filter(f => f.includes('simple-test')));
    
    // Check file contents
    const files = fs.readdirSync('./logs').filter(f => f.includes('simple-test'));
    files.forEach(file => {
        const content = fs.readFileSync(`./logs/${file}`, 'utf8');
        console.log(`\n${file} content (${content.length} bytes):`);
        console.log(content.substring(0, 200) + (content.length > 200 ? '...' : ''));
    });
}, 2000);
