//*********************************************************************************************
//* COPYRIGHT © 2025-, Michael <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
// SystemSettings.js
// Handle the System Settings
// /**
//  * @module SystemSettings
//  */
"use strict"

console.log('========== SystemSettings.js =======')

const fs = require("fs")
const { systemSettings } = require('./Global.js')
const { ProjectDefinitions } = require('./Project.js')
const { clearObject } = require('./Util.js')

const systemSettingsDefault = {
	projectName: 'CT332-',
	projectLocation: 'Praevista Lab-',
	projectDescription: '',
	email: '<EMAIL>',
	alertEmailEnable: false,
	cloudUrl: 'https://cloud.praevista.com/api2',
	cloudEnable: false,
}


//==========================================================================================
// SystemSettings.init()
//==========================================================================================
function init()
{
	clearObject(systemSettings)
	//--------------------------------------------------------------------------------------
	// Load the system settings file
	//--------------------------------------------------------------------------------------
	try {
		const s = JSON.parse(fs.readFileSync(ProjectDefinitions.systemSettingsFile, 'utf8'))
		if (typeof s !== 'object') {
			throw new Error('Invalid system settings file format')
		}
		Object.assign(systemSettings, s)
		console.log('+++++>>> System settings file loaded:', ProjectDefinitions.systemSettingsFile)
	} catch (err) {
		if (err.code === 'ENOENT')
		{
			fs.writeFileSync(ProjectDefinitions.systemSettingsFile, JSON.stringify(systemSettingsDefault, null, '\t'))
			console.log('+++++>>> System settings file was created:', ProjectDefinitions.systemSettingsFile)
		}
		else
		{
			console.error('Error loading system settings file:', err)
		}
		Object.assign(systemSettings, systemSettingsDefault)
	}


}









module.exports = {
	init,
}
