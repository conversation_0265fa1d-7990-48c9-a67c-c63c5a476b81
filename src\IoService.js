//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// IoService.js
//
// Implement processing of the IO Servers requests
//=============================================================================================

const util = require('util');
const { timestamp } = require("./Global.js")
const { logger } = require('./AppLogger.js')

// const { cloneObject } = require('./Util.js')
// const ModbusOperations = require('./ModbusOperations.js')

// A single IO Server
async function svc1(ioServer, ioRegisters, timestamp)
{
	logger.switchToSystemLogger()
	let r = null
	const t0 = Date.now()
	// const { host, port } = ioServer
	ioServer.requestIpr = true


	//------------------------------------------------------------------------------------------------
	// Write Request queue
	//------------------------------------------------------------------------------------------------
	while(ioServer.writeQueue.length > 0) {
		let item = ioServer.writeQueue.shift()
		try {
			await ioServer.modbusClient.writeRegisters(item.unitId, item.startAddress, item.dataBuffer)
			ioServer.error.write = false
		} catch(e) {
			logger.error('####>>>> svc1 writeRegisters error', e)
			// console.error('####>>>> svc1 writeRegisters error', e)
			r = e
			ioServer.error.write = true
			ioServer.error.count++
			ioServer.error.timestamp = Date.now()
			ioServer.error.message = e.message
			ioServer.writeQueue.length = 0 // clear the queue when there is an error
		}
	}

	//------------------------------------------------------------------------------------------------
	// Read Request queue
	//------------------------------------------------------------------------------------------------
	const nRead = ioServer.requestQueue.length
	for(let i = 0; i < nRead; i++) {
		const registerGroup = ioServer.requestQueue[i]

		// we may not have to read this register right now
		const upd = registerGroup.update
		if(upd && registerGroup.updated && (timestamp % upd[0]) !== upd[1])
		{
			continue
		}

		const { server, params } = registerGroup
		const unitid = params.unitid || 1
		const fc  = params.fc  || 3
		const address = params.address || 0
		const quantity = params.quantity || 2
		const bytes = quantity * 2
		try {
			const resp = await ioServer.modbusClient.readRegisters(unitid, fc, address, quantity)
			if(!resp)
			{
				throw new Error('Modbus read error')
			}
			const tu = Date.now() // This is the timestamp to use for the update
			registerGroup.updated = tu
			ioServer.xferCount++
			ioServer.error.state = false
			const buf = resp.data
			// console.log('svc1', server, 'fc:', fc, 'address', address, resp.data)
			// if(resp.header.reqTransactionId !== resp.header.transactionId)
			// {
			// 	throw new Error('Bad transactionId:' + resp.header.transactionId + ', request:' + resp.header.reqTransactionId)
			// }
			// else
			if(buf.length !== bytes)
			{
				throw new Error('Bad Modbus buffer length, requested:' + bytes + ', received:' + buf.length)
				// console.error('svc1', server, 'requested:', quantity * 2, 'received:', buf.length)
			}
			else
			{
				// Step through the registers to be written
				registerGroup.registers.forEach((register, i) => {
					const myRegObj = ioRegisters[register.device][register.name]
					// const { type, index } = myRegObj
					if(!myRegObj.write) // Don't update the register if it is writable
					{
						const raw = myRegObj.parse(buf, register.index, register)
						myRegObj.raw = raw
						myRegObj._value = raw * myRegObj.scale + myRegObj.offset
						myRegObj.updated = tu
					}
				})
			}


		} catch(e) {
			r = e
			ioServer.error.state = true
			ioServer.error.count++
			ioServer.error.timestamp = Date.now()
			ioServer.error.message = e.message
			logger.error(`Modbus communication error - Server: ${server}, Device: ${registerGroup.device}`, e)
			// console.error('svc1', server, registerGroup.device, '|', e)
			break; // Stop servicing requests
		}

	}
	ioServer.requestIpr = false
	const now = Date.now()
	ioServer.updated = now
	ioServer.requestLatency = now - t0
	return r
}



const promiseTimout = 30000
function poll(ioServers, ioRegisters, timestamp)
{
	console.log('-------------- ioPoll ---------------')
	logger.switchToSystemLogger()
	// Quick Write Test
	// if(timestamp % 2 === 1)
	// {
		// mbWriteTest(ioServers)
	// }


	Object.keys(ioServers).forEach(serverName => {
		const ioServer = ioServers[serverName]
		if(ioServer.requestQueue.length > 0)
		{
			if(util.types.isPromise(ioServer.promise))
			{
				if((Date.now() - ioServer.updated) > promiseTimout)
				{
					// ioServer.promise.cancel()
					// ioServer.promise = null
					logger.error('XXXX> IoService.poll promise timeout exceeded', serverName)
				}
				else
				{
					logger.log('--> Slow response from:', serverName)
				}
			}
			else
			{
				ioServer.promise = svc1(ioServer, ioRegisters, timestamp).then(result => {
					ioServer.promise = null
				}).catch(e => {
					logger.error('IoService.poll svc1 error', e)
					ioServer.promise = null
				})
			}

			try {
				ioServer.readyState = ioServer.modbusClient.client.readyState
			} catch(e) {
				ioServer.readyState = null
			}

		}

	})
}

// let wrBusy = false
let mbWriteTestData = 0x01
function mbWriteTest(ioServers)
{
	// if(wrBusy) return
	// wrBusy = true
	const buffer = Buffer.alloc(2)
//	buffer.writeUInt16BE(Math.floor(Math.random() * 256), 0)
	buffer.writeUInt16BE(mbWriteTestData, 0)
	mbWriteTestData *= 2
	if(mbWriteTestData > 0xFF) mbWriteTestData = 0x01
	mbWriteQueue(ioServers['Digital-IO-1'], 1, 2, buffer)
}

function mbWriteQueue(ioServer, unitId, startAddress, dataBuffer)
{
	if(ioServer.writeQueue.length < 10 && !ioServer.error.write)
	{
		ioServer.writeQueue.push({ unitId, startAddress, dataBuffer })
		return true
	}
	return false
}


// Close all the open connections
function close(ioServers)
{
	logger.switchToSystemLogger()
	const prArray = [] // An array of promises, one per server
	Object.keys(ioServers).forEach(serverName => {
		if(ioServers[serverName].modbusClient)
		{
			prArray.push(ioServers[serverName].modbusClient.close().then(() => {
				logger.log('IoService.close', serverName)
			}).catch(e => {
				logger.error('IoService.close error', serverName, e)
			}))
		}
	})
	return Promise.all(prArray)
}

/**
 * Services the IO operations for all registered servers
 * @function poll
 * @description Processes IO operations for all servers if registers are initialized
 * @returns {Promise<Object>|undefined} A promise that resolves with the result of the IO service operation,
 *                                     or undefined if no registers are initialized
 */
// let pollTimer = null
// function poll(ioServers, ioRegisters, timestamp)
// {

// 	if(pollTimer)
// 	{
// 		clearTimeout(pollTimer)
// 		pollTimer = null
// 	}

// 	pollTimer = setTimeout(() => {
// 		pollTimer = null
// 		// pollResults(0)
// 	}, 800)

// 	console.log('-------------- ioPoll ---------------')
// 	return svc(ioServers, ioRegisters, timestamp).then((r) => {
// 		if(pollTimer)
// 		{
// 			clearTimeout(pollTimer)
// 			pollTimer = null
// 		}
// 		// pollResults(1)
// 		return r
// 	})

// }



module.exports = {
	// svc,
	poll,
	close,
}

