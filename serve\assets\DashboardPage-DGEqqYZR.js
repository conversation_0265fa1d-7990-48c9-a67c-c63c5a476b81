import{Q as E}from"./QSelect-BNWcW_ch.js";import{Q as z}from"./QPage-Disdm55v.js";import{_ as O}from"./AqPersistToggleButton-BcqzKwP0.js";import{S as Q}from"./SseSpinner-HLkgS90o.js";import{a as V,r as n,w as H,f as F,T as u,F as c,X as g,J as D,a3 as L,a2 as x,E as _,U as k,Y as U,H as A,Q as N,i as W,n as X,D as M,G as Y,ah as J,S as j}from"./index-CzmOWWdj.js";import"./use-key-composition-CoMUTTxZ.js";import"./use-timeout-DeCFbuIx.js";import"./QDialog-Cuvxr_1w.js";import"./focusout-C-pmmZED.js";import"./QMenu-D3shCIOy.js";import"./Login-BVtZ5S7B.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";//!============================================================================================
//! COPYRIGHT © 2024-, Michael Alford and Pravista Inc. - All Rights Reserved
//! NOTICE: All information contained herein is, and remains the property of Michael Alford,
//! Pravista Inc., and their suppliers, if any. The intellectual and technical concepts
//! contained herein are proprietary to Michael Alford, Pravista Inc., and their suppliers
//! and are protected by trade secret or copyright law. Dissemination of this information or
//! reproduction of this material is strictly forbidden unless prior written permission is
//! obtained from Michael Alford and Pravista Inc.
//!============================================================================================
function R(i){const e=(i.origin||"LT").toUpperCase(),t=i.location[0]||i.x||0,r=i.location[1]||i.y||0,s=e[0]==="R"?"right:"+(100-t):"left:"+t,l=e[1]==="B"?"bottom:"+(100-r):"top:"+r;return s+"%;"+l+"%;"}const K={__name:"DbUnknown",props:{registers:{required:!1},options:{type:Object,required:!0},update:{type:Number,required:!0}},setup(i){const e=V(()=>"unknown-widget db-widget-"+(t.options.origin||"LT").toLowerCase()),t=i,r=n(null);H(()=>t.update,()=>{s()});function s(){if(t.registers===null)return;const l=t.options;r.value=R(l)}return F(()=>{s()}),(l,a)=>(c(),u("span",{class:x(e.value),style:L(r.value)},[a[0]||(a[0]=g("div",null,"Unknown Widget",-1)),g("div",null,'"'+D(t.options.component)+'"',1)],6))}},Z={key:0},ee={key:0},te={colspan:"100"},oe={key:1},re={__name:"DbFlexTable",props:{registers:{type:Object,required:!0},options:{type:Object,required:!0},update:{type:Number,required:!0},altUnits:{type:[Boolean,Number],required:!0,default:!1}},setup(i){const e=i,t=n(null),r=n(null),s=n(null),l=n([]);H(()=>e.update,()=>{a()}),H(()=>e.altUnits,()=>{a()});function a(){if(e.registers===null)return;const o=e.options,d=Array.isArray(o.mode);let p;try{p=o.mode&&!d?e.registers[o.mode.device][o.mode.register].text[0].toLowerCase():"idle"}catch{p="idle"}const m=d&&!o.columns;if(d&&o.columns){const b=[];for(let T=0;T<o.mode.length;T++){let P="idle";try{const f=o.mode[T];P=e.registers[f.device][f.register].text[0].toLowerCase()}catch{}b.push("db-widget-mode-cell-"+P)}l.value=b}s.value="db-widget-mode-"+p+" db-widget-"+(o.origin||"LT").toLowerCase();const y=o.columns||[{device:o.device||"?"}];r.value=R(o);const S=[];o.registers.forEach((b,T)=>{const P={label:b.label===null?null:b.label||b.register};if(m){try{p=e.registers[o.mode[T].device][o.mode[T].register].text[0].toLowerCase()}catch{p="idle"}P.thClass="db-widget-mode-cell-"+p}const f=[P];y.forEach(w=>{const I=b.device||w.device||"?",G=b.register||w.register||"?";try{const q=e.altUnits?1:0,B=e.registers[I][G],v=B.text[q]||B.text[0];f.push({value:v,error:B.error||!1})}catch{f.push({value:"-?-",error:!1})}}),S.push(f)}),t.value=S}return F(()=>{a()}),(o,d)=>e.registers?(c(),u("table",{key:0,class:x(["db-widget-table",s.value]),style:L(r.value)},[i.options.columns||i.options.title?(c(),u("thead",Z,[i.options.title?(c(),u("tr",ee,[g("th",te,D(i.options.title),1)])):_("",!0),i.options.columns?(c(),u("tr",oe,[d[0]||(d[0]=g("th",null,null,-1)),(c(!0),u(k,null,U(i.options.columns,(p,m)=>(c(),u("th",{key:m,class:x(l.value[m]||null)},D(p.title),3))),128))])):_("",!0)])):_("",!0),g("tbody",null,[(c(!0),u(k,null,U(t.value,(p,m)=>(c(),u("tr",{key:m},[(c(!0),u(k,null,U(p,(y,S)=>(c(),u(k,{key:S},[y.label?(c(),u("th",{key:0,class:x(y.thClass||null)},D(y.label),3)):(c(),u("td",{key:1,class:x({"sensor-error":y.error})},D(y.value),3))],64))),128))]))),128))])],6)):_("",!0)}},le={__name:"DbPump",props:{registers:{type:Object,required:!0},options:{type:Object,required:!0},update:{type:Number,required:!0}},setup(i){const e=i,t=n(null),r=n(null);H(()=>e.update,()=>{s()});function s(){if(e.registers===null)return;const l=e.options;r.value=R(l);try{t.value=!!parseInt(e.registers[l.device][l.register].text[0])}catch{t.value=!1}}return F(()=>{s()}),(l,a)=>t.value?(c(),u("span",{key:0,class:"motor-widget",style:L(r.value)},null,4)):_("",!0)}},ie={__name:"DbBinaryIcon",props:{registers:{required:!1},options:{type:Object,required:!0},update:{type:Number,required:!0}},setup(i){const e=i,t=n(null),r=n("question_mark"),s=n(null);H(()=>e.update,()=>{l()});function l(){if(e.registers===null)return;const a=e.options;s.value=R(a);try{const o=parseInt(e.registers[e.options.device][e.options.register].text[0]),d=e.options.invert?!o:o;r.value=d?e.options.iconTrue||"check_circle_outline":e.options.iconFalse||"highlight_off",t.value="binary-icon-widget binary-icon-widget-"+(d?"true":"false")+" db-widget-"+(e.options.origin||"LT").toLowerCase()}catch{r.value="question_mark"}}return F(()=>{l()}),(a,o)=>(c(),u("div",{class:x(t.value),style:L(s.value)},[g("div",null,D(e.options.label),1),g("div",null,[A(N,{name:r.value,size:"2.5em"},null,8,["name"])])],6))}},se={__name:"DbHeatCool",props:{registers:{type:Object,required:!0},options:{type:Object,required:!0},update:{type:Number,required:!0}},setup(i){const e=i,t=n(null),r=n(null),s=n(null),l=n(null);H(()=>e.update,()=>{a()});function a(){if(e.registers===null)return;const o=e.options;r.value=R(o);let d="off";try{d=e.registers[o.device][o.register].text[0].toLowerCase()}catch{}switch(t.value=d,d){case"heat":l.value="whatshot";break;case"cool":l.value="ac_unit";break;default:l.value="block";break}s.value="heat-cool-widget heat-cool-widget-"+d+" db-widget-"+(e.options.origin||"LT").toLowerCase()}return F(()=>{a()}),(o,d)=>(c(),u("div",{class:x(s.value),style:L(r.value)},[A(N,{name:l.value,size:"3em",color:"white"},null,8,["name"])],6))}};//!============================================================================================
//! COPYRIGHT © 2025-, Michael Alford and Pravista Inc. - All Rights Reserved
//! NOTICE: All information contained herein is, and remains the property of Michael Alford,
//! Pravista Inc., and their suppliers, if any. The intellectual and technical concepts
//! contained herein are proprietary to Michael Alford, Pravista Inc., and their suppliers
//! and are protected by trade secret or copyright law. Dissemination of this information or
//! reproduction of this material is strictly forbidden unless prior written permission is
//! obtained from Michael Alford and Pravista Inc.
//!============================================================================================
const ne={DbFlexTable:re,DbPump:le,DbBinaryIcon:ie,DbHeatCool:se};function ae(i){return ne[i]||K}const ce={style:{"min-width":"1600px"}},ue={class:"dashboard-schematic"},de=["innerHTML"],ge={class:"dashboard-schematic-widgets"},pe={key:0,class:"dashboard-selector q-ma-sm"},me={class:"fixed z-top overflow-hidden",style:{top:"50px",right:"0"}},be={class:"inline-block q-mr-sm q-px-xs text-caption"},_e={__name:"DashboardPage",setup(i){const e=W("$api"),t={device:"geothermal",register:"HeatingCoolingMode"},r={device:"building1",register:"HeatingCoolingMode"},s={device:"building2",register:"HeatingCoolingMode"},l={device:"auxiliary1",register:"HeatingCoolingMode"},a={device:"auxiliary2",register:"HeatingCoolingMode"},o=[{label:"Speed",register:"ActualSpeedRPM"},{label:"Voltage",register:"MotorInputVoltage"},{label:"Current",register:"MotorInputCurrent"},{label:"Power",register:"MotorPower"}],m=[{label:"Overview",widgets:[...[{component:"DbFlexTable",origin:"CB",location:[25.8,76.5],columns:[{title:"GP1",device:"GP1"},{title:"GP2",device:"GP2"}],registers:[{label:"Setpoint",register:"ControlSetpoint"},{label:"Flow",register:"SensorlessFlow"},...o],mode:t},{component:"DbFlexTable",origin:"RT",location:[19.7,86],columns:[{title:"GP1",device:"GP1"},{title:"GP2",device:"GP2"}],registers:[{label:"HOA",register:"HOAState"},{label:"Trip Hrs",register:"TripPumpRunningHours"}],mode:t},{component:"DbFlexTable",origin:"CB",location:[59,82],device:"AnalogIn",registers:[{label:"GPS1",register:"GPS1"},{label:"GTS1",register:"GTS1"},{label:"GEO ΔT",register:"DeltaT",device:"geothermal"},{label:"GTS0",register:"GTS0"}],mode:t},{component:"DbFlexTable",origin:"CB",location:[59,70],device:"geothermal",registers:[{label:"Geo Energy Transfer",register:"HeatTransferRate"}],mode:t},{component:"DbFlexTable",origin:"RC",location:[100-17.4,47.13],device:"AnalogIn",registers:[{label:"B2TS1",register:"B2TS1"},{label:"B2 ΔT",register:"DeltaT",device:"building2"},{label:"B2TS0",register:"B2TS0"}],mode:s},{component:"DbFlexTable",origin:"LC",location:[62,14.73],device:"AnalogIn",registers:[{label:"A2TS1",register:"A2TS1"},{label:"A2 ΔT",register:"DeltaT",device:"auxiliary2"},{label:"A2TS0",register:"A2TS0"}],mode:a},{component:"DbFlexTable",origin:"RT",location:[75,28.25],columns:[{title:"A2P1",device:"A2P1"},{title:"B2P2",device:"B2P2"},{title:"B2P1",device:"B2P1"}],registers:[{label:"Flow",register:"SensorlessFlow"},{label:"Head",register:"SensorlessHead"},...o],mode:[a,s,s]},{component:"DbFlexTable",origin:"CC",location:[65,47.13],device:"building2",registers:[{label:"A2 Aux Energy Transfer",register:"HeatTransferRate",device:"auxiliary2"},{label:"B2 Geo Energy Transfer",register:"HeatTransferRate"}],mode:[a,s]},{component:"DbFlexTable",origin:"LC",location:[17.4,47.13],device:"AnalogIn",registers:[{label:"B1TS1",register:"B1TS1"},{label:"B1 ΔT",register:"DeltaT",device:"building1"},{label:"B1TS0",register:"B1TS0"}],mode:r},{component:"DbFlexTable",origin:"RC",location:[38,14.73],device:"AnalogIn",registers:[{label:"A1TS1",register:"A1TS1"},{label:"A1 ΔT",register:"DeltaT",device:"auxiliary1"},{label:"A1TS0",register:"A1TS0"}],mode:l},{component:"DbFlexTable",origin:"LT",location:[25,28.25],columns:[{title:"B1P1",device:"B1P1"},{title:"B1P2",device:"B1P2"},{title:"A1P1",device:"A1P1"}],registers:[{label:"Flow",register:"SensorlessFlow"},{label:"Head",register:"SensorlessHead"},...o],mode:[r,r,l]},{component:"DbFlexTable",origin:"CC",location:[35,47.13],device:"building1",registers:[{label:"A1 Aux Energy Transfer",register:"HeatTransferRate",device:"auxiliary1"},{label:"B1 Geo Energy Transfer",register:"HeatTransferRate"}],mode:[l,r]},{component:"DbFlexTable",origin:"CT",location:[90.36,61],title:"Head",registers:[{label:"GP1",device:"GP1",register:"SensorlessHead"},{label:"GP2",device:"GP2",register:"SensorlessHead"},{label:"B1P1",device:"B1P1",register:"SensorlessHead"},{label:"B1P2",device:"B1P2",register:"SensorlessHead"},{label:"A1P1",device:"A1P1",register:"SensorlessHead"},{label:"B2P1",device:"B2P1",register:"SensorlessHead"},{label:"B2P2",device:"B2P2",register:"SensorlessHead"},{label:"A2P1",device:"A2P1",register:"SensorlessHead"}],mode:[t,t,r,r,l,s,s,a]},{component:"DbFlexTable",origin:"LC",location:[37.5,24.5],title:"A1 Setpoint",registers:[{label:null,device:"A1P1",register:"ControlSetpoint"}],mode:t},{component:"DbFlexTable",origin:"RC",location:[100-37.5,24.5],title:"A2 Setpoint",registers:[{label:null,device:"A1P1",register:"ControlSetpoint"}],mode:t},{component:"DbFlexTable",origin:"CT",location:[28.21,24.5],device:"auxiliary1",registers:[{label:"State",register:"PumpingState"}],mode:t},{component:"DbFlexTable",origin:"CT",location:[100-28.21,24.5],device:"auxiliary2",registers:[{label:"State",register:"PumpingState"}],mode:t},{component:"DbFlexTable",origin:"LB",location:[32,83.5],device:"geothermal",registers:[{label:"State",register:"PumpingState"}],mode:t}],{component:"DbPump",device:"GP1",register:"Status",location:[25.3,80.51]},{component:"DbPump",device:"GP2",register:"Status",location:[25.3,88.36]},{component:"DbPump",device:"A1P1",register:"Status",location:[28.21,18.65]},{component:"DbPump",device:"B1P1",register:"Status",location:[13.25,28.43]},{component:"DbPump",device:"B1P2",register:"Status",location:[18.07,28.43]},{component:"DbPump",device:"A2P1",register:"Status",location:[100-28.21,18.65]},{component:"DbPump",device:"B2P1",register:"Status",location:[100-13.25,28.43]},{component:"DbPump",device:"B2P2",register:"Status",location:[100-18.07,28.43]},{component:"DbBinaryIcon",label:"GGF",device:"DigitalIO",register:"GGF",origin:"CB",location:[6.02,74.62-3],invert:!0},{component:"DbBinaryIcon",label:"B1GF",device:"DigitalIO",register:"B1GF",origin:"CB",location:[8.43,20.62-3],invert:!0},{component:"DbBinaryIcon",label:"B2GF",device:"DigitalIO",register:"B2GF",origin:"CB",location:[91.57,20.62-3],invert:!0},Object.assign({component:"DbHeatCool",origin:"LC",location:[10.26+1,5.93]},r),Object.assign({component:"DbHeatCool",origin:"RC",location:[100-10.26-1.25,5.93]},s),Object.assign({component:"DbHeatCool",origin:"LC",location:[28.2+1,5.93]},l),Object.assign({component:"DbHeatCool",origin:"RC",location:[100-28.2-1.25,5.93]},a),Object.assign({component:"DbHeatCool",origin:"CC",location:[43,67]},t)]}],y=n(!0),S=n(!1),b=n("-NA-"),T=n(m[0].label),P=n(null),f=n(0);let w={};const I=V(()=>m.find(h=>h.label==T.value)||m[0]),G=()=>e.get("/dashboard/data").then(v=>{w=v.data,f.value++,b.value=new Date().toLocaleTimeString()}),q=v=>{P.value=v,y.value&&G()},B=n("");return F(()=>{console.log("IndexPage onMounted"),e.get("/dashboard/bg.svg").then(v=>(B.value=v.data,X())).then(()=>{const v=document.querySelector(".dashboard-schematic-svg"),h=v.offsetHeight/v.offsetWidth;document.querySelector(".dashboard-schematic").style.paddingTop=100*h+"%",G()})}),(v,h)=>(c(),M(z,null,{default:Y(()=>[g("div",ce,[g("div",ue,[g("div",{class:"dashboard-schematic-svg",innerHTML:B.value},null,8,de),g("div",ge,[(c(!0),u(k,null,U(I.value.widgets,(C,$)=>(c(),M(J(j(ae)(C.component)),{key:$,options:C,update:f.value,registers:j(w),"alt-units":S.value},null,8,["options","update","registers","alt-units"]))),128))]),m.length>1?(c(),u("div",pe,[A(E,{dense:"",outlined:"","emit-value":"","option-value":"label",modelValue:T.value,"onUpdate:modelValue":h[0]||(h[0]=C=>T.value=C),options:m,"option-label":"label"},null,8,["modelValue"])])):_("",!0)])]),g("div",me,[g("div",be,"Last Updated: "+D(b.value),1),A(O,{size:"sm",modelValue:S.value,"onUpdate:modelValue":h[1]||(h[1]=C=>S.value=C),"local-store-key":"dashboardAltUnits","default-value":!1,"color-true":"primary","color-false":"primary","label-true":"Alternate Units","label-false":"Default Units",onClick:G},null,8,["modelValue"]),A(O,{round:"",flat:"",modelValue:y.value,"onUpdate:modelValue":h[2]||(h[2]=C=>y.value=C),"local-store-key":"dashboardAutoUpdate","default-value":!0,"color-true":"primary","color-false":"grey-5","icon-true":"play_circle","icon-false":"pause_circle","tooltip-true":"Click to disable auto updates","tooltip-false":"Click to enable auto updates"},null,8,["modelValue"]),A(Q,{onMessage:q,color:"primary"})])]),_:1}))}};export{_e as default};
