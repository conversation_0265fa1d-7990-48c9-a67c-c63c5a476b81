
//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//*
//* routes/data.js
//*
//* Server Sent Events
//*============================================================================================
/**
 * @module routes/setup
 */

const { API_ROOT, mac } = require('../System.js')
const { timestamp, registers, devicesList, projectSettings } = require('../Global.js')
const { ProjectDefinitions } = require('../Project.js')
const ProjectConstants = require('../ProjectConstants.js')
const CurvesInit       = require('../CurvesInit.js')
const Schedules        = require('../Schedules.js')
const DocsInit         = require('../DocsInit.js')
const SystemSettings = require('../SystemSettings.js')
const { postFileHandler } = require('./handlers.js')

const fs   = require('fs')
const path = require('path')

// function postHandler(request, reply, fileName, initFunction)
// {
// 		const data = request.body.data
// 		if(!Array.isArray(data))
// 		{
// 			return reply.code(400).send({ error: 'Invalid data, a JSON array is required' })
// 		}
// 		fs.writeFileSync(fileName, JSON.stringify(data, null, '\t'))

// 		if (typeof initFunction === 'function') {
// 			initFunction() // Apply the updated settings
// 		}

// 		const r = fs.readFileSync(fileName, 'utf8')
// 		return reply.type('application/json').send(r)

// }

//----------------------------------------------------------------------------------------------------
/**
 * Routes for the setup pages.
 * - Call using `fastify.register()`
 * @param {object} fastify - The Fastify instance
 * @param {object} options
 * @param {Function} done - The callback function to call when the plugin is registered
 */
//----------------------------------------------------------------------------------------------------
function setupRoutes(fastify, options, done)
{
	//----------------------------------------------------------------------------------------------------
	// Setup Curves
	//----------------------------------------------------------------------------------------------------
	fastify.get(API_ROOT + '/setup/curves', async (request, reply) => {
		const r = fs.readFileSync(ProjectDefinitions.controlCurvesFile, 'utf8')
		return reply.type('application/json').send(r)
	})

	fastify.post(API_ROOT + '/setup/curves', async (request, reply) => {
		return postFileHandler(request, reply, ProjectDefinitions.controlCurvesFile, CurvesInit)
	})

	//----------------------------------------------------------------------------------------------------
	// Setup Project Settings (project-constants.json)
	//----------------------------------------------------------------------------------------------------
	fastify.get(API_ROOT + '/setup/settings', async (request, reply) => {
		const r = fs.readFileSync(ProjectDefinitions.projectConstantsFile, 'utf8')
		return reply.type('application/json').send(r)
	})

	fastify.post(API_ROOT + '/setup/settings', async (request, reply) => {
		return postFileHandler(request, reply, ProjectDefinitions.projectConstantsFile, ProjectConstants.init)
	})

	//----------------------------------------------------------------------------------------------------
	// Setup Schedules
	//----------------------------------------------------------------------------------------------------
	fastify.get(API_ROOT + '/setup/schedules', async (request, reply) => {
		const r = fs.readFileSync(ProjectDefinitions.schedulesFile, 'utf8')
		return reply.type('application/json').send(r)
	})

	fastify.post(API_ROOT + '/setup/schedules', async (request, reply) => {
		return postFileHandler(request, reply, ProjectDefinitions.schedulesFile, Schedules.init)
	})

	//----------------------------------------------------------------------------------------------------
	// Setup System
	//----------------------------------------------------------------------------------------------------
	fastify.get(API_ROOT + '/setup/system', async (request, reply) => {
		try {
			const r = fs.readFileSync(ProjectDefinitions.systemSettingsFile, 'utf8')
			return reply.type('application/json').send(r)
		} catch (err) {}

		return reply.send({
			email: '<EMAIL>',
			alertEmailEnable: false,
			cloudUrl: 'https://cloud.praevista.com/api2',
			cloudEnable: false,
		});
	})

	fastify.post(API_ROOT + '/setup/system', async (request, reply) => {
		return postFileHandler(request, reply, ProjectDefinitions.systemSettingsFile, SystemSettings.init) // TODO: init function
	})

	//----------------------------------------------------------------------------------------------------
	// Setup Documents
	//----------------------------------------------------------------------------------------------------
	fastify.get(API_ROOT + '/setup/documents', async (request, reply) => {
		const r = fs.readFileSync(ProjectDefinitions.docsIndexFile, 'utf8')
		return reply.type('application/json').send(r)
	})

	fastify.post(API_ROOT + '/setup/documents', async (request, reply) => {
		return postFileHandler(request, reply, ProjectDefinitions.docsIndexFile, DocsInit.init)
	})

	fastify.get(API_ROOT + '/setup/documents/ls', async (request, reply) => {
		const files = await fs.promises.readdir(ProjectDefinitions.docsDirectory)
		const r = []
		for (const file of files) {
			const filePath = path.join(ProjectDefinitions.docsDirectory, file)
			const stats = await fs.promises.stat(filePath)
			if (stats.isFile()) {
				r.push({ file, size: stats.size })
			}
		}
		return reply.send(r)
	})


	done()
}


module.exports = setupRoutes
