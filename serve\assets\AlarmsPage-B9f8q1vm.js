import{Q as P}from"./use-key-composition-CoMUTTxZ.js";import{c as G,a as S,h as T,b as Q,g as J,i as K,r as k,f as X,o as Y,D as g,F as r,G as o,X as m,H as n,E as y,T as w,O as C,I as V,ag as Z,S as B,a2 as ee,a3 as te,J as M,U as ae,Q as q}from"./index-CzmOWWdj.js";import{Q as le}from"./QTable-CsCiCbH_.js";import{Q as oe}from"./QPage-Disdm55v.js";import{_ as se,s as D,a as re,e as ne}from"./export-file-CL4zo4TZ.js";import{S as ie}from"./SseSpinner-HLkgS90o.js";import{_ as U}from"./AqPersistToggleButton-BcqzKwP0.js";import{u as L}from"./PersistObject-BVaCPhKL.js";import{d as ue}from"./Debug-gcimLaHD.js";import{u as ce}from"./use-quasar-Li8tSQ3f.js";import"./use-timeout-DeCFbuIx.js";import"./QSeparator-D-fNGoQY.js";import"./QList-yWBbetAh.js";import"./QSelect-BNWcW_ch.js";import"./QDialog-Cuvxr_1w.js";import"./focusout-C-pmmZED.js";import"./QMenu-D3shCIOy.js";import"./QCheckbox-BLQMX8bg.js";import"./use-checkbox-DDSE4paG.js";import"./Login-BVtZ5S7B.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const N=G({name:"QTd",props:{props:Object,autoWidth:Boolean,noHover:Boolean},setup(i,{slots:_}){const d=J(),f=S(()=>"q-td"+(i.autoWidth===!0?" q-table--col-auto-width":"")+(i.noHover===!0?" q-td--no-hover":"")+" ");return()=>{if(i.props===void 0)return T("td",{class:f.value},Q(_.default));const v=d.vnode.key,c=(i.props.colsMap!==void 0?i.props.colsMap[v]:null)||i.props.col;if(c===void 0)return;const{row:l}=i.props;return T("td",{class:f.value+c.__tdClass(l),style:c.__tdStyle(l)},Q(_.default))}}}),me={class:"a-container-lg q-mb-xl"},de={class:"col-sm col-12"},pe={class:"row q-col-gutter-sm"},ge={class:"col-auto"},fe={key:1},ve={key:1},Ee={__name:"AlarmsPage",setup(i){const _=ce(),d=K("$api"),f=[{name:"i",label:"#",align:"center",field:"i",sortable:!0},{name:"name",label:"Alarm Pnumonic",align:"left",field:"name",sortable:!0,classes:e=>e.lockout?"table-cell-lockout-text":"table-cell-alarm-text"},{name:"start",label:"Start Time",align:"center",field:"start",sortable:!0,format:e=>e?D(e):"-"},{name:"status",label:"Status",align:"right",field:"timestamp",sortable:!0,format:e=>e?D(e):"-"},{name:"state",label:"State",align:"center",field:"alarm",sortable:!0},{name:"progress",label:"Progress",field:"progress",classes:"ellipsis table-col-progress",align:"center"},{name:"value",label:"Value",align:"right",field:"value",sortable:!0,classes:e=>e.error?"table-cell-error-text":""},{name:"limit",label:"Limit",align:"center",field:"limit",sortable:!0},{name:"units",label:"Units",align:"left",field:"units",sortable:!0},{name:"duration",label:"Duration",align:"center",field:"duration",sortable:!0,format:e=>e?re(e):"-"},{name:"comment",label:"Description",align:"left",field:"comment",sortable:!0,classes:"ellipsis table-col-comment"}],v=k(!0),c=k(!0),l=L("registerStatusTablePagination",{sortBy:"i",descending:!1,page:1,rowsPerPage:0}),E=L("regStatusDeviceSelect","AnalogIn"),$=k([]),j=k(null),u=k([]),z=S(()=>u.value.filter(e=>e.lockout&&e.alarm).length>0),I=S(()=>u.value.filter(e=>!e.lockout&&e.alarm).length>0),O=e=>{j.value=e,c.value&&h()};X(()=>{d.get("/info").then(e=>{console.log("RegistersPage onMounted: ",e),$.value=e.data.devices}).then(()=>{h()}),window.addEventListener("keydown",A)}),Y(()=>{window.removeEventListener("keydown",A)});function A(e){switch(e.code){case"NumpadAdd":l.value.page=Math.min(R.value,l.value.page+1);break;case"NumpadSubtract":l.value.page=Math.max(1,l.value.page-1);break}}const R=S(()=>Math.ceil(u.value.length/l.value.rowsPerPage)),h=()=>{d.get("/status/alarms").then(e=>{const a=e.data;a.forEach((t,s)=>{t.i=s,t.progress=t.start?Math.min(100,100*(t.timestamp-t.start)/t.duration):null,t.limit=t.compare+" "+t.setpoint,t.deltat=t.timestamp-t.start}),u.value=a})},H=()=>{d.delete("/status/alarms").then(e=>{console.log("AlarmsPage clearAlarms: ",e),h()})},F=()=>{d.delete("/status/lockouts").then(e=>{console.log("AlarmsPage clearAlarms: ",e),h()})};function x(e,a,t){let s=a!==void 0?a(e,t):e;return s=s==null?"":String(s),s=s.split('"').join('""'),`"${s}"`}const W=()=>{const e=f,a=u.value,t=[e.map(b=>x(b.label))].concat(a.map(b=>e.map(p=>x(typeof p.field=="function"?p.field(b):b[p.field===void 0?p.name:p.field],p.format,b)).join(","))).join(`\r
`);ne("registers-"+E.value+".csv",t,"text/csv")!==!0&&_.notify({message:"Browser denied file download...",color:"negative",icon:"warning"})};return(e,a)=>(r(),g(oe,null,{default:o(()=>[m("div",me,[n(se,{title:"Alarm Status",icon:"warning"},{default:o(()=>[m("div",de,[m("div",pe,[a[5]||(a[5]=m("div",{class:"col"},null,-1)),m("div",ge,[z.value?(r(),g(C,{key:0,color:"negative",dense:"",icon:"lock",label:"Clear Lockouts",onClick:F},{default:o(()=>[n(P,{class:"text-body2"},{default:o(()=>a[3]||(a[3]=[V("Click to clear all Alarms")])),_:1,__:[3]})]),_:1})):y("",!0),I.value?(r(),g(C,{key:1,class:"q-ml-sm",color:"warning",dense:"",icon:"warning",label:"Clear Alarms",onClick:H},{default:o(()=>[n(P,{class:"text-body2"},{default:o(()=>a[4]||(a[4]=[V("Click to clear all Alarms")])),_:1,__:[4]})]),_:1})):y("",!0),n(U,{round:"",flat:"",modelValue:c.value,"onUpdate:modelValue":a[0]||(a[0]=t=>c.value=t),"local-store-key":"alarmStatusAutoUpdate","default-value":!0,"color-true":"primary","color-false":"grey-5","icon-true":"play_circle","icon-false":"pause_circle","tooltip-true":"Click to disable auto updates","tooltip-false":"Click to enable auto updates"},null,8,["modelValue"]),n(ie,{onMessage:O})])])])]),_:1}),u.value.length>0?(r(),g(le,{key:0,"rows-per-page-label":"Alarms per page","rows-per-page-options":[10,20,25,30,35,50,100,0],class:"table-row-highlight q-mt-md",dense:v.value,flat:"",bordered:"",title:"Alarms and Lockouts",rows:u.value,columns:f,"row-key":"name",pagination:B(l),"onUpdate:pagination":a[2]||(a[2]=t=>Z(l)?l.value=t:null)},{"top-right":o(()=>[n(U,{class:"q-mr-md",round:"",flat:"",modelValue:v.value,"onUpdate:modelValue":a[1]||(a[1]=t=>v.value=t),"local-store-key":"alarmStatusTableDense","default-value":!0,"color-true":"grey-5","color-false":"grey-5","icon-true":"compress","icon-false":"expand","tooltip-true":"Click to expand the table spacing","tooltip-false":"Click to make the table compact"},null,8,["modelValue"]),n(C,{color:"primary","icon-right":"archive",label:"Export to csv","no-caps":"",onClick:W})]),"body-cell-state":o(t=>[n(N,{props:t},{default:o(()=>[t.value?(r(),w(ae,{key:0},[t.row.lockout?(r(),g(q,{key:0,name:"lock",color:"negative",size:"1.5em",class:"alarm-icon-animate"})):(r(),g(q,{key:1,name:"warning",color:"warning",size:"1.5em",class:"alarm-icon-animate"}))],64)):y("",!0)]),_:2},1032,["props"])]),"body-cell-progress":o(t=>[n(N,{props:t},{default:o(()=>[t.value>0?(r(),w("div",{key:0,class:ee("progress-bar-"+(t.row.lockout?"lockout":"alarm"))},[m("div",{style:te("width: "+t.value+"%")},null,4),m("div",null,M(t.row.deltat),1)],2)):(r(),w("span",fe,"Ok"))]),_:2},1032,["props"])]),_:1},8,["dense","rows","pagination"])):y("",!0),B(ue)?(r(),w("pre",ve,M(u.value),1)):y("",!0)])]),_:1}))}};export{Ee as default};
