const winston = require('winston');

console.log('=== Testing Winston Format ===');

const logger = winston.createLogger({
    transports: [
        new winston.transports.Console(),
        new winston.transports.File({
            filename: './logs/winston-format-test.log',
            format: winston.format.combine(
                winston.format.timestamp({ format: 'HH:mm:ss' }),
                winston.format.printf((info) => {
                    console.log('Winston info object:', info);
                    const logObj = {
                        timestamp: info.timestamp,
                        level: info.level,
                        message: info.message
                    };
                    if (info.data) { logObj.data = info.data; }
                    if (info.stack) { logObj.stack = info.stack; }
                    return JSON.stringify(logObj);
                })
            )
        })
    ]
});

// Test different log formats
console.log('\n1. Simple string message:');
logger.info('Simple message');

console.log('\n2. Object with timestamp:');
logger.log({
    level: 'info',
    message: 'Message with custom timestamp',
    timestamp: Date.now()
});

console.log('\n3. Object with data:');
logger.log({
    level: 'info', 
    message: 'Message with data',
    data: { key: 'value' }
});

setTimeout(() => {
    const fs = require('fs');
    const content = fs.readFileSync('./logs/winston-format-test.log', 'utf8');
    console.log('\nFile content:');
    console.log(content);
}, 1000);
