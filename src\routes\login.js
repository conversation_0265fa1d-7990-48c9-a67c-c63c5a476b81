//*********************************************************************************************
//* COPYRIGHT © 2025-, Michael <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of Michael <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
// routes/login.js
//
// This provides a plugin for the login routes
//
//******************************************************************************************

const { getUsersFile, API_ROOT, secrets } = require('../System.js')
const { jwtSign } = require('../HttpServerAuth.js')
const { objectArrayExclude, objectExclude } = require('../Util.js')
const { appAuthBasic, appAuthJWT, AUTH_ANY } = require('../AppLoginAuth.js')


const apiPathLogin = API_ROOT + '/login'
const apiPathUsers = API_ROOT + '/users'

function loginRoutes(fastify, options, done)
{
	fastify.get(apiPathUsers, (req, reply) => {
		reply.send(objectArrayExclude(getUsersFile(), 'password', null))
		// reply.send(getUsersFile())
	})

	fastify.get(apiPathLogin, { preHandler: appAuthBasic }, (req, reply) => {
		// The password user/password is valid
		// Create the JWT

		// console.log('LOGIN req', req)

		// if the `remember` query parameter is true, then set the expiration to 8w, 1w otherwise
		const expiresIn = req.query.remember ? '8w' : '1w'

		const token = jwtSign(req.user, secrets.jwtSecret, expiresIn)

		reply.send({
			// authorization: req.headers.authorization,
			// message: 'Hello from the login route',
			// user: req.user, // This is the user object from the appVerifyUser function
			token,
		})
	})

	// fastify.get(apiPathLogin + '/test', { preHandler: appAuthJWT }, (req, reply) => {
	fastify.get(apiPathLogin + '/test', AUTH_ANY, (req, reply) => {
		// The JWT is valid
		reply.send({
			message: 'The JWT is valid',
			user: req.user,
			query: req.query,
			// typeofJwt: typeof jwt,
			// jwtSecret: secrets.jwtSecret,
			// authorization: req.headers.authorization,
		})
	})

	done()
}

// export default loginRoutes

module.exports = loginRoutes
