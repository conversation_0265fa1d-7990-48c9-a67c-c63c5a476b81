//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// Logs.js
//
// <PERSON> system logging
//
//=============================================================================================
"use strict"
console.log('========== Logs.js =================')

// const startupLogArray = [] // Array of log messages for initialization
// const systemLogArray = [] // Array of log messages for system operation
// const alertLogArray  = [] // Array of log messages for system alerts

function logFormat(msg, data, level='error')
{
	const r = {
		timestamp: Date.now(),
		level,
	}

	if(data !== undefined)
	{
		try {
			r.data = JSON.stringify(data)
		} catch (error) {}
	}

	if(typeof msg === 'string')
	{
		r.message = msg
		return r
	}
	else if(msg instanceof Error)
	{
		r.message = msg.message
		r.stack = msg.stack
		return r
	}
}


const startupLogger = new loggerConstructor({
	size: 100, // Number of log entries to keep in memory!!
	verboseStack: false, // Only show the first to lines of stack trace
	verboseStack: true || undefined, // Show complete stack trace
	filename,
	persist: true || false, // true - load most recenet log info into array at startup
	filesize: 1024 * 1024 * 10, // 10MB for file size (not memory)
	storeOnly: ['warn', 'error'], // Only store warn and error messages in the file
	...
})

// Persist option:
// When persist===true upon construction, the logger will load the most recent log entries from the file (if any) into the array.
// In otherwords, this feature will make the log persist across restarts.

//const startupLog = startupLogger.log.log

startupLogger.init()
startupLogger.log(string || Error, data)
startupLogger.warn()
startupLogger.error()
startupLogger.array // naked array of all log entries
startupLogger.array = 0 // Reset the array
startupLogger.array.length // Number of total entries
startupLogger.query(indexed, offset)

// Inside the constructor
#_log(level, string || Error, data)

log(string || Error, data) {
	return this._log('info', string || Error, data)
}

module.exports = {
	startupLogger,
	systemLog,
	alertLog,
	logFormat,
}

