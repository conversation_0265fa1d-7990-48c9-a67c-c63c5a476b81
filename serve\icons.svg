<svg xmlns="http://www.w3.org/2000/svg">

<!-- Example Use: <q-icon name="svguse:icons.svg#io" /> -->

<!-- Praevista favicon Logo -->
	<symbol id="logo" viewBox="0 0 640 425">
		<path class="cls-1" d="M331.87,117.32c3.78-5.03,1.54-11.45-5.02-14.35-6.55-2.9-14.94-1.18-18.72,3.85L68.56,425H0S320,0,320,0l84.79,112.62-235.21,312.38h-69.38S331.87,117.32,331.87,117.32Z"/>
		<path class="cls-1" d="M534.85,370.51c3.78-5.03,1.54-11.45-5.02-14.35-6.56-2.9-14.94-1.18-18.72,3.85l-48.94,65h-68.11s122.97-163.31,122.97-163.31l122.97,163.31h-146.17s41.03-54.49,41.03-54.49Z"/>
		<path class="cls-2" d="M420.62,133.63l31.95,42.44-156.91,208.4c-3.78,5.03-1.54,11.45,5.02,14.35,2.16.95,4.51,1.41,6.84,1.41,4.74,0,9.34-1.88,11.88-5.25l149-197.89,32.82,43.59-138.79,184.33h-161.19s219.38-291.37,219.38-291.37Z"/>
	</symbol>

<!-- Aquiot favicon Logo -->
	<symbol id="aq-logo" viewBox="0 0 128 128">
		<path d="M127.32,36.35c-.45-1.08-1.08-2.03-1.9-2.84s-1.76-1.44-2.84-1.89c-1.08-.45-2.23-.68-3.45-.68h-39.67c-26.8,0-48.53,21.73-48.53,48.53v48.53h17.6v-17.6h61.87v17.6h17.6V39.8c0-1.22-.23-2.37-.68-3.45ZM48.53,79.47c0-17.08,13.85-30.93,30.93-30.93h30.93v44.27h-61.87v-13.33M79.47,48.53"/>
		<path d="M0,79.47C0,35.58,35.58,0,79.47,0v17.6c-34.17,0-61.87,27.7-61.87,61.87H0Z"/>
		<path d="M61.87,79.47c0-9.72,7.88-17.6,17.6-17.6v17.6h-17.6Z"/>
	</symbol>

<!-- From: https://pictogrammers.com/library/mdi/icon/server-network-outline/ -->
	<symbol id="server" viewBox="0 0 24 24">
		<!-- <path d="M13 13V15H14C14.6 15 15 15.4 15 16H22V18H15C15 18.6 14.6 19 14 19H10C9.4 19 9 18.6 9 18H2V16H9C9 15.4 9.4 15 10 15H11V13H3.2C2.5 13 2 12.3 2 11.4V6.6C2 5.7 2.5 5 3.2 5H20.8C21.5 5 22 5.7 22 6.6V11.4C22 12.3 21.5 13 20.8 13H13M9 10V8H10V10H9M5 8H7V10H5V8M20 7H4V11H20V7Z" /> -->
		<path d="M13,16h1c.55,0,1,.45,1,1h7v2h-7c0,.55-.45,1-1,1h-4c-.55,0-1-.45-1-1H2v-2h7c0-.55.45-1,1-1h1v-4h-7c-.55,0-1-.45-1-1v-6c0-.55.45-1,1-1h16c.55,0,1,.45,1,1v6c0,.55-.45,1-1,1h-7v4M9,9h2v-2h-2v2M5,7v2h2v-2h-2ZM17,9h2v-2h-2v2M13,7v2h2v-2h-2Z"/>
	</symbol>

	<symbol id="register-type" viewBox="0 0 24 24">
		<rect x="4" y="5" width="6" height="6"/>
		<circle cx="16" cy="8" r="3"/>
		<polygon points="7 13 4 19 10 19 7 13"/>
		<polygon points="17.73 13 14.27 13 12.54 16 14.27 19 17.73 19 19.46 16 17.73 13"/>
	</symbol>

	<symbol id="firmware" viewBox="0 0 512 512">
		<path d="M512,106v12a6,6,0,0,1-6,6H488v6a6,6,0,0,1-6,6H440V88h42a6,6,0,0,1,6,6v6h18A6,6,0,0,1,512,106Z"/>
		<path d="M512,202v12a6,6,0,0,1-6,6H488v6a6,6,0,0,1-6,6H440V184h42a6,6,0,0,1,6,6v6h18A6,6,0,0,1,512,202Z"/>
		<path d="M512,298v12a6,6,0,0,1-6,6H488v6a6,6,0,0,1-6,6H440V280h42a6,6,0,0,1,6,6v6h18A6,6,0,0,1,512,298Z"/>
		<path d="M512,394v12a6,6,0,0,1-6,6H488v6a6,6,0,0,1-6,6H440V376h42a6,6,0,0,1,6,6v6h18A6,6,0,0,1,512,394Z"/>
		<path d="M30,376H72v48H30a6,6,0,0,1-6-6v-6H6a6,6,0,0,1-6-6V394a6,6,0,0,1,6-6H24v-6A6,6,0,0,1,30,376Z"/>
		<path d="M30,280H72v48H30a6,6,0,0,1-6-6v-6H6a6,6,0,0,1-6-6V298a6,6,0,0,1,6-6H24v-6A6,6,0,0,1,30,280Z"/>
		<path d="M30,184H72v48H30a6,6,0,0,1-6-6v-6H6a6,6,0,0,1-6-6V202a6,6,0,0,1,6-6H24v-6A6,6,0,0,1,30,184Z"/>
		<path d="M30,88H72v48H30a6,6,0,0,1-6-6v-6H6a6,6,0,0,1-6-6V106a6,6,0,0,1,6-6H24V94A6,6,0,0,1,30,88Z"/>
		<path d="M368,0H144A48,48,0,0,0,96,48V464a48,48,0,0,0,48,48H368a48,48,0,0,0,48-48V48A48,48,0,0,0,368,0Zm-5.78,277.84-97.46,97.52a12.41,12.41,0,0,1-17.49,0l-97.58-97.52a12.79,12.79,0,0,1,9-21.85H215V148.34A15.34,15.34,0,0,1,230.35,133h51.26A15.34,15.34,0,0,1,297,148.34V256h56.19A12.79,12.79,0,0,1,362.22,277.84Z"/>
	</symbol>


<!-- Fontawesome 5: microchip -->
	<symbol id="microchip" viewBox="0 0 512 512">
		<path d="M416 48v416c0 26.51-21.49 48-48 48H144c-26.51 0-48-21.49-48-48V48c0-26.51 21.49-48 48-48h224c26.51 0 48 21.49 48 48zm96 58v12a6 6 0 0 1-6 6h-18v6a6 6 0 0 1-6 6h-42V88h42a6 6 0 0 1 6 6v6h18a6 6 0 0 1 6 6zm0 96v12a6 6 0 0 1-6 6h-18v6a6 6 0 0 1-6 6h-42v-48h42a6 6 0 0 1 6 6v6h18a6 6 0 0 1 6 6zm0 96v12a6 6 0 0 1-6 6h-18v6a6 6 0 0 1-6 6h-42v-48h42a6 6 0 0 1 6 6v6h18a6 6 0 0 1 6 6zm0 96v12a6 6 0 0 1-6 6h-18v6a6 6 0 0 1-6 6h-42v-48h42a6 6 0 0 1 6 6v6h18a6 6 0 0 1 6 6zM30 376h42v48H30a6 6 0 0 1-6-6v-6H6a6 6 0 0 1-6-6v-12a6 6 0 0 1 6-6h18v-6a6 6 0 0 1 6-6zm0-96h42v48H30a6 6 0 0 1-6-6v-6H6a6 6 0 0 1-6-6v-12a6 6 0 0 1 6-6h18v-6a6 6 0 0 1 6-6zm0-96h42v48H30a6 6 0 0 1-6-6v-6H6a6 6 0 0 1-6-6v-12a6 6 0 0 1 6-6h18v-6a6 6 0 0 1 6-6zm0-96h42v48H30a6 6 0 0 1-6-6v-6H6a6 6 0 0 1-6-6v-12a6 6 0 0 1 6-6h18v-6a6 6 0 0 1 6-6z" class=""></path>
	</symbol>
<!-- Material Icons: insert_chart -->
	<symbol id="dashboard" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0V0z" fill="none" />
		<path d="M9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4zm2 2H5V5h14v14zm0-16H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z" />
	</symbol>

<!-- Material Icons: menu -->
	<symbol id="menu" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0V0z" fill="none" />
		<path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z" />
	</symbol>

<!-- Material Icons: signal_cellular_alt -->
	<symbol id="status" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0V0z" fill="none"/>
		<path d="M17 4h3v16h-3V4zM5 14h3v6H5v-6zm6-5h3v11h-3V9z"/>
	</symbol>

<!-- Material Icons: wifi -->
	<symbol id="network" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0V0zm0 0h24v24H0V0z" fill="none"/>
		<path d="M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.08 2.93 1 9zm8 8l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zm-4-4l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z"/>
	</symbol>

<!-- Material Icons: cloud -->
	<symbol id="cloud" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0V0z" fill="none"/>
		<path d="M12 6c2.62 0 4.88 1.86 5.39 4.43l.3 1.5 1.53.11c1.56.1 2.78 1.41 2.78 2.96 0 1.65-1.35 3-3 3H6c-2.21 0-4-1.79-4-4 0-2.05 1.53-3.76 3.56-3.97l1.07-.11.5-.95C8.08 7.14 9.94 6 12 6m0-2C9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96C18.67 6.59 15.64 4 12 4z"/>
	</symbol>

<!-- Material Icons: notifications -->
	<symbol id="alert" viewBox="0 0 24 24">
		<path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
	</symbol>

<!-- Material Icons: sell -->
	<symbol id="tag" viewBox="0 0 24 24">
		<path d="M0,0h24v24H0V0z" fill="none"/>
		<path d="M21.41,11.41l-8.83-8.83C12.21,2.21,11.7,2,11.17,2H4C2.9,2,2,2.9,2,4v7.17c0,0.53,0.21,1.04,0.59,1.41l8.83,8.83 c0.78,0.78,2.05,0.78,2.83,0l7.17-7.17C22.2,13.46,22.2,12.2,21.41,11.41z M6.5,8C5.67,8,5,7.33,5,6.5S5.67,5,6.5,5S8,5.67,8,6.5 S7.33,8,6.5,8z"/>
	</symbol>

<!-- Material Icons: clear -->
	<symbol id="clear" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0z" fill="none"/>
		<path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
	</symbol>

<!-- Material Icons: visibility -->
	<symbol id="show" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0z" fill="none"/>
		<path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
	</symbol>

<!-- Material Icons: visibility_off -->
	<symbol id="hide" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0zm0 0h24v24H0zm0 0h24v24H0zm0 0h24v24H0z" fill="none"/>
		<path d="M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z"/>
	</symbol>

<!-- Material Icons: lan -->
	<symbol id="lan" viewBox="0 0 24 24">
		<polygon points="13,22 21,22 21,15 18,15 18,11 13,11 13,9 16,9 16,2 8,2 8,9 11,9 11,11 6,11 6,15 3,15 3,22 11,22 11,15 8,15 8,13 16,13 16,15 13,15"/>
	</symbol>

<!-- Material Icons: save_alt -->
	<symbol id="save" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0z" fill="none"/>
		<path d="M19 12v7H5v-7H3v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7h-2zm-6 .67l2.59-2.58L17 11.5l-5 5-5-5 1.41-1.41L11 12.67V3h2z"/>
	</symbol>

<!-- Material Icons: settings -->
	<symbol id="settings" viewBox="0 0 24 24">
		<path d="M0,0h24v24H0V0z" fill="none"/>
		<path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
	</symbol>

<!-- Material Icons: arrow_drop_down -->
	<symbol id="arrow_drop_down" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0z" fill="none"/><path d="M7 10l5 5 5-5z"/>
	</symbol>


<!-- Material Icons: manage_accounts -->
	<symbol id="user_admin" viewBox="0 0 24 24">
		<path d="M0,0h24v24H0V0z" fill="none"/>
		<circle cx="10" cy="8" r="4"/>
		<path d="M10.67,13.02C10.45,13.01,10.23,13,10,13c-2.42,0-4.68,0.67-6.61,1.82C2.51,15.34,2,16.32,2,17.35V20h9.26 C10.47,18.87,10,17.49,10,16C10,14.93,10.25,13.93,10.67,13.02z"/><path d="M20.75,16c0-0.22-0.03-0.42-0.06-0.63l1.14-1.01l-1-1.73l-1.45,0.49c-0.32-0.27-0.68-0.48-1.08-0.63L18,11h-2l-0.3,1.49 c-0.4,0.15-0.76,0.36-1.08,0.63l-1.45-0.49l-1,1.73l1.14,1.01c-0.03,0.21-0.06,0.41-0.06,0.63s0.03,0.42,0.06,0.63l-1.14,1.01 l1,1.73l1.45-0.49c0.32,0.27,0.68,0.48,1.08,0.63L16,21h2l0.3-1.49c0.4-0.15,0.76-0.36,1.08-0.63l1.45,0.49l1-1.73l-1.14-1.01 C20.72,16.42,20.75,16.22,20.75,16z M17,18c-1.1,0-2-0.9-2-2s0.9-2,2-2s2,0.9,2,2S18.1,18,17,18z"/>
	</symbol>

<!-- Material Icons: schedule -->
	<symbol id="clock" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0z" fill="none"/>
		<path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/>
		<path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"/>
	</symbol>

<!-- Material Icons: task -->
	<symbol id="file_ok" viewBox="0 0 24 24">
		<path d="M0,0h24v24H0V0z" fill="none"/>
		<path d="M14,2H6C4.9,2,4.01,2.9,4.01,4L4,20c0,1.1,0.89,2,1.99,2H18c1.1,0,2-0.9,2-2V8L14,2z M10.94,18L7.4,14.46l1.41-1.41 l2.12,2.12l4.24-4.24l1.41,1.41L10.94,18z M13,9V3.5L18.5,9H13z"/>
	</symbol>

<!-- Material Icons: error -->
	<symbol id="error" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0z" fill="none"/>
		<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
	</symbol>

<!-- Material Icons: insert_drive_file -->
	<symbol id="file" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0z" fill="none"/>
		<path d="M6 2c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6H6zm7 7V3.5L18.5 9H13z"/>
	</symbol>

<!-- Material Icons: restart_alt -->
	<symbol id="reset" viewBox="0 0 24 24">
		<path d="M0,0h24v24H0V0z" fill="none"/>
		<path d="M12,5V2L8,6l4,4V7c3.31,0,6,2.69,6,6c0,2.97-2.17,5.43-5,5.91v2.02c3.95-0.49,7-3.85,7-7.93C20,8.58,16.42,5,12,5z"/>
		<path d="M6,13c0-1.65,0.67-3.15,1.76-4.24L6.34,7.34C4.9,8.79,4,10.79,4,13c0,4.08,3.05,7.44,7,7.93v-2.02 C8.17,18.43,6,15.97,6,13z"/>
	</symbol>

<!-- Material Icons: done -->
	<symbol id="done" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0z" fill="none"/>
		<path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
	</symbol>

<!-- Material Icons: warning -->
	<symbol id="warning" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0z" fill="none"/>
		<path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
	</symbol>

<!-- Material Icons: help -->
	<symbol id="help" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0z" fill="none"/>
		<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
	</symbol>

<!-- Material Icons: check_box -->
	<symbol id="checkbox" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0z" fill="none"/>
		<path d="M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
	</symbol>

<!-- Material Icons: check_box_outline_blank -->
	<symbol id="uncheckbox" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0z" fill="none"/>
		<path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"/>
	</symbol>

<!-- Material Icons: bug_report -->
	<symbol id="bug" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0z" fill="none"/>
		<path d="M20 8h-2.81c-.45-.78-1.07-1.45-1.82-1.96L17 4.41 15.59 3l-2.17 2.17C12.96 5.06 12.49 5 12 5c-.49 0-.96.06-1.41.17L8.41 3 7 4.41l1.62 1.63C7.88 6.55 7.26 7.22 6.81 8H4v2h2.09c-.05.33-.09.66-.09 1v1H4v2h2v1c0 .34.04.67.09 1H4v2h2.81c1.04 1.79 2.97 3 5.19 3s4.15-1.21 5.19-3H20v-2h-2.09c.05-.33.09-.66.09-1v-1h2v-2h-2v-1c0-.34-.04-.67-.09-1H20V8zm-6 8h-4v-2h4v2zm0-4h-4v-2h4v2z"/>
	</symbol>

<!-- Material Icons: cloud_upload -->
	<symbol id="cloud_upload" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0z" fill="none"/>
		<path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM14 13v4h-4v-4H7l5-5 5 5h-3z"/>
	</symbol>

<!-- Material Icons: compare_arrows (edited) -->
	<symbol id="io" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0z" fill="none"/>
		<path d="M14,15H6v2h8v3L18,16l-4-4ZM10,12V9H18V7H10V4L6,8Z"/>
	</symbol>

<!-- Material Icons: folder -->
	<symbol id="folder" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0z" fill="none"/>
		<path d="M10 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8l-2-2z"/>
	</symbol>

<!-- Material Icons: delete -->
	<symbol id="delete" viewBox="0 0 24 24">
		<path d="M0 0h24v24H0z" fill="none"/>
		<path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
	</symbol>

<!-- Material Icons: first_page -->
	<symbol id="first_page" viewBox="0 0 24 24">
		<path d="M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z"/>
	</symbol>

<!-- Material Icons: last_page -->
	<symbol id="last_page" viewBox="0 0 24 24">
		<path d="M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z"/>
	</symbol>

<!-- Material Icons: chevron_left -->
	<symbol id="chevron_left" viewBox="0 0 24 24">
		<path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
	</symbol>

<!-- Material Icons: chevron_right -->
	<symbol id="chevron_right" viewBox="0 0 24 24">
		<path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
	</symbol>

<!-- Material Icons: sync -->
	<symbol id="sync" viewBox="0 0 24 24">
		<path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
	</symbol>

<!-- Material Icons: content_copy -->
	<symbol id="copy" viewBox="0 0 24 24">
		<path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
	</symbol>

<!-- Material Icons: person -->
	<symbol id="person" viewBox="0 0 24 24">
		<path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
	</symbol>

<!-- Material Icons: person_off -->
	<symbol id="person_off" viewBox="0 0 24 24">
		<path d="M8.65,5.82C9.36,4.72,10.6,4,12,4c2.21,0,4,1.79,4,4c0,1.4-0.72,2.64-1.82,3.35L8.65,5.82z M20,17.17 c-0.02-1.1-0.63-2.11-1.61-2.62c-0.54-0.28-1.13-0.54-1.77-0.76L20,17.17z M21.19,21.19L2.81,2.81L1.39,4.22l8.89,8.89 c-1.81,0.23-3.39,0.79-4.67,1.45C4.61,15.07,4,16.1,4,17.22V20h13.17l2.61,2.61L21.19,21.19z"/>
	</symbol>

<!-- custom -->
	<symbol id="output" viewBox="0 0 24 24">
		<path d="M22,12,17,7v4H9.86a4,4,0,1,0,0,2H17v4ZM6,14a2,2,0,1,1,2-2A2,2,0,0,1,6,14Z"/>
	</symbol>

<!-- custom -->
	<symbol id="input" viewBox="0 0 24 24">
		<path d="M18,8a4,4,0,0,0-3.89,3.11L10,7v4H2v2h8v4l4.11-4.11A4,4,0,1,0,18,8Zm0,6a2,2,0,1,1,2-2A2,2,0,0,1,18,14Z"/>
	</symbol>

<!-- ilertify icon -->
	<!-- <symbol id="logo" viewBox="0 0 180 180">
		<path d="M125.26,147.8a10.4,10.4,0,0,1-10.54-10.35V33.2a10.45,10.45,0,0,1,20.89,0v93.72H172.1A90.16,90.16,0,1,0,159,147.8Zm-24.82-10.35A10.51,10.51,0,0,1,89.91,147.8a10.36,10.36,0,0,1-10.35-10.35V75A10.51,10.51,0,0,1,89.91,64.44,10.67,10.67,0,0,1,100.44,75ZM89.91,53.08a10.44,10.44,0,0,1,0-20.88,10.44,10.44,0,1,1,0,20.88Z"/>
	</symbol> -->

<!-- ilertify icon filled -->
	<!-- <symbol id="logo-fill" viewBox="0 0 180 180">
		<circle cx="90" cy="90" r="89" style="fill:#fff"/>
		<path d="M125.3,147.8a10.4,10.4,0,0,1-10.6-10.4V33.2a10.5,10.5,0,0,1,10.6-10.4,10.4,10.4,0,0,1,10.3,10.4v93.7h36.5A88.8,88.8,0,0,0,180,90a90,90,0,1,0-21,57.8Zm-24.9-10.4a10.5,10.5,0,0,1-10.5,10.4,10.3,10.3,0,0,1-10.3-10.4V75A10.5,10.5,0,0,1,89.9,64.4,10.7,10.7,0,0,1,100.4,75ZM89.9,53.1a10.5,10.5,0,1,1,10.5-10.5A10.6,10.6,0,0,1,89.9,53.1Z" transform="translate(0 0)" style="fill:#c10015"/>
	</symbol> -->

</svg>
