//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// Global.js
//
// Global Data Structures
//=============================================================================================
console.log('========== Global.js ===============')
const { type } = require('os')
// const projectConstantsDefault = require('./ProjectConstants.js')
// const { Trends } = require('./Project.js')
// const functions = require('./ControlFunctions.js')

// const projConstFilename = '/public/project-constants.json' // DEPRECATED


// This contains the realtime register information
// Here the data is stored as registers['device']['register']
const registers   = {}
const devicesList = []
const ioServers   = {}
const curves      = {}
const schedules   = {}
const alarms      = []
const datalogSettings = {}
const projectSettings = {}
const systemSettings = {} // This is the new system settings object that replaces some of projectSettings
const timestamp   = {s: 0, ms: 0}
const trends      = []
const documents   = []

/**
 * Initializes the timestamp,
 *
 * - Sets the current timestamp in both milliseconds and seconds.
 *
 * @function init
 * @global
 * @throws Will log an error if reading or parsing the settings file fails.
 */
function init()
{
	const ms = Date.now()
	timestamp.ms = ms
	timestamp.s = Math.floor(ms / 1000)

	// Load the `Trends`
	// trends.length = 0
	// trends.push(...Trends)

}


module.exports = {
	init,
	registers,
	devicesList,
	ioServers,
	curves,
	schedules,
	alarms,
	trends,
	documents,
	datalogSettings,
	projectSettings,
	systemSettings,
	timestamp,
	// functions,
}
