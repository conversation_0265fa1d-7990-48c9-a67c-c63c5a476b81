import{c as ae,s as ne,g as se,r as w,a as u,w as D,a7 as ue,t as ie,aN as le,h as A,b as re,aa as ce,o as fe,a8 as de}from"./index-CzmOWWdj.js";import{u as ve,z as ge,A as M,w as me,a as he,b as ye,k as Pe,c as Te,j as ke,x as Ce,B as xe,l as Se,d as Ee,y as Oe,C as R,D as be,E as H,F as qe,G as Be,n as Fe}from"./use-key-composition-CoMUTTxZ.js";import{u as we}from"./use-timeout-DeCFbuIx.js";import{d as De,e as K,c as Ae,r as Me}from"./focusout-C-pmmZED.js";const We=ae({name:"QMenu",inheritAttrs:!1,props:{...Pe,...ye,...he,...me,persistent:<PERSON><PERSON><PERSON>,autoClose:<PERSON><PERSON><PERSON>,separateClosePopup:<PERSON><PERSON><PERSON>,noEscDismiss:<PERSON><PERSON><PERSON>,noRouteDismiss:<PERSON><PERSON><PERSON>,noRefocus:<PERSON><PERSON><PERSON>,noFocus:<PERSON><PERSON><PERSON>,fit:<PERSON><PERSON><PERSON>,cover:<PERSON><PERSON>an,square:Boolean,anchor:{type:String,validator:M},self:{type:String,validator:M},offset:{type:Array,validator:ge},scrollTarget:ne,touchPosition:Boolean,maxHeight:{type:String,default:null},maxWidth:{type:String,default:null}},emits:[...ve,"click","escapeKey"],setup(t,{slots:Q,emit:c,attrs:d}){let n=null,f,i,v;const T=se(),{proxy:g}=T,{$q:o}=g,a=w(null),s=w(!1),W=u(()=>t.persistent!==!0&&t.noRouteDismiss!==!0),j=Te(t,o),{registerTick:_,removeTick:z}=ke(),{registerTimeout:k}=we(),{transitionProps:G,transitionStyle:I}=Ce(t),{localScrollTarget:C,changeScrollEvent:L,unconfigureScrollTarget:N}=xe(t,B),{anchorEl:l,canShow:U}=Se({showing:s}),{hide:x}=Ee({showing:s,canShow:U,handleShow:Y,handleHide:Z,hideOnRouteChange:W,processOnMount:!0}),{showPortal:S,hidePortal:E,renderPortal:$}=Oe(T,a,ee,"menu"),m={anchorEl:l,innerRef:a,onClickOutside(e){if(t.persistent!==!0&&s.value===!0)return x(e),(e.type==="touchstart"||e.target.classList.contains("q-dialog__backdrop"))&&de(e),!0}},O=u(()=>R(t.anchor||(t.cover===!0?"center middle":"bottom start"),o.lang.rtl)),J=u(()=>t.cover===!0?O.value:R(t.self||"top start",o.lang.rtl)),V=u(()=>(t.square===!0?" q-menu--square":"")+(j.value===!0?" q-menu--dark q-dark":"")),X=u(()=>t.autoClose===!0?{onClick:p}:{}),b=u(()=>s.value===!0&&t.persistent!==!0);D(b,e=>{e===!0?(De(y),be(m)):(K(y),H(m))});function h(){Fe(()=>{let e=a.value;e&&e.contains(document.activeElement)!==!0&&(e=e.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||e.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||e.querySelector("[autofocus], [data-autofocus]")||e,e.focus({preventScroll:!0}))})}function Y(e){if(n=t.noRefocus===!1?document.activeElement:null,Ae(F),S(),B(),f=void 0,e!==void 0&&(t.touchPosition||t.contextMenu)){const P=ue(e);if(P.left!==void 0){const{top:te,left:oe}=l.value.getBoundingClientRect();f={left:P.left-oe,top:P.top-te}}}i===void 0&&(i=D(()=>o.screen.width+"|"+o.screen.height+"|"+t.self+"|"+t.anchor+"|"+o.lang.rtl,r)),t.noFocus!==!0&&document.activeElement.blur(),_(()=>{r(),t.noFocus!==!0&&h()}),k(()=>{o.platform.is.ios===!0&&(v=t.autoClose,a.value.click()),r(),S(!0),c("show",e)},t.transitionDuration)}function Z(e){z(),E(),q(!0),n!==null&&(e===void 0||e.qClickOutside!==!0)&&(((e?.type.indexOf("key")===0?n.closest('[tabindex]:not([tabindex^="-"])'):void 0)||n).focus(),n=null),k(()=>{E(!0),c("hide",e)},t.transitionDuration)}function q(e){f=void 0,i!==void 0&&(i(),i=void 0),(e===!0||s.value===!0)&&(Me(F),N(),H(m),K(y)),e!==!0&&(n=null)}function B(){(l.value!==null||t.scrollTarget!==void 0)&&(C.value=ie(l.value,t.scrollTarget),L(C.value,r))}function p(e){v!==!0?(qe(g,e),c("click",e)):v=!1}function F(e){b.value===!0&&t.noFocus!==!0&&le(a.value,e.target)!==!0&&h()}function y(e){t.noEscDismiss!==!0&&(c("escapeKey"),x(e))}function r(){Be({targetEl:a.value,offset:t.offset,anchorEl:l.value,anchorOrigin:O.value,selfOrigin:J.value,absoluteOffset:f,fit:t.fit,cover:t.cover,maxHeight:t.maxHeight,maxWidth:t.maxWidth})}function ee(){return A(ce,G.value,()=>s.value===!0?A("div",{role:"menu",...d,ref:a,tabindex:-1,class:["q-menu q-position-engine scroll"+V.value,d.class],style:[d.style,I.value],...X.value},re(Q.default)):null)}return fe(q),Object.assign(g,{focus:h,updatePosition:r}),$}});export{We as Q};
