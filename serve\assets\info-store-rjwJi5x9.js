import{ad as c,r as n}from"./index-CzmOWWdj.js";import{a as s}from"./Login-BVtZ5S7B.js";const d=c("info",()=>{const o=n(null),l=n(null),r=n(null),t=n(!1),e=n(null);function u(){return t.value=!0,e.value=null,s.get("/info").then(a=>{console.log("API Response: ",a),l.value=a.data.projectSettings,r.value=a.data.mac,o.value=a.data}).catch(a=>{e.value=a.message||"Unknown error"}).finally(()=>{t.value=!1})}return u(),{info:o,projectSettings:l,mac:r,loading:t,error:e,load:u}});export{d as u};
