import{a as g,aQ as Oe,g as R,r as q,ay as W,ax as oe,n as U,ac as je,w as k,f as K,o as F,au as ve,q as me,aD as Qe,aR as te,c as He,B as Ue,as as Ve,h as b,aS as Ne,aT as Ke,aU as Ge,al as Re,a9 as Ze,av as Fe,y as Je,s as Xe,t as Ye,aa as Me,b as Q,a8 as We,z as et,aJ as tt,i as ot,aB as lt,aH as nt,am as it,Q as ye,a4 as at}from"./index-CzmOWWdj.js";import{u as rt}from"./use-timeout-DeCFbuIx.js";const ut={dark:{type:Boolean,default:null}};function st(e,t){return g(()=>e.dark===null?t.dark.isActive:e.dark)}function he(){if(window.getSelection!==void 0){const e=window.getSelection();e.empty!==void 0?e.empty():e.removeAllRanges!==void 0&&(e.removeAllRanges(),Oe.is.mobile!==!0&&e.addRange(document.createRange()))}else document.selection!==void 0&&document.selection.empty()}const Le={target:{type:[Boolean,String,Element],default:!0},noParentEvent:Boolean},Qt={...Le,contextMenu:Boolean};function dt({showing:e,avoidEmit:t,configureAnchorEl:o}){const{props:l,proxy:i,emit:u}=R(),n=q(null);let a=null;function d(s){return n.value===null?!1:s===void 0||s.touches===void 0||s.touches.length<=1}const c={};o===void 0&&(Object.assign(c,{hide(s){i.hide(s)},toggle(s){i.toggle(s),s.qAnchorHandled=!0},toggleKey(s){je(s,13)===!0&&c.toggle(s)},contextClick(s){i.hide(s),W(s),U(()=>{i.show(s),s.qAnchorHandled=!0})},prevent:W,mobileTouch(s){if(c.mobileCleanup(s),d(s)!==!0)return;i.hide(s),n.value.classList.add("non-selectable");const v=s.target;oe(c,"anchor",[[v,"touchmove","mobileCleanup","passive"],[v,"touchend","mobileCleanup","passive"],[v,"touchcancel","mobileCleanup","passive"],[n.value,"contextmenu","prevent","notPassive"]]),a=setTimeout(()=>{a=null,i.show(s),s.qAnchorHandled=!0},300)},mobileCleanup(s){n.value.classList.remove("non-selectable"),a!==null&&(clearTimeout(a),a=null),e.value===!0&&s!==void 0&&he()}}),o=function(s=l.contextMenu){if(l.noParentEvent===!0||n.value===null)return;let v;s===!0?i.$q.platform.is.mobile===!0?v=[[n.value,"touchstart","mobileTouch","passive"]]:v=[[n.value,"mousedown","hide","passive"],[n.value,"contextmenu","contextClick","notPassive"]]:v=[[n.value,"click","toggle","passive"],[n.value,"keyup","toggleKey","passive"]],oe(c,"anchor",v)});function f(){ve(c,"anchor")}function S(s){for(n.value=s;n.value.classList.contains("q-anchor--skip");)n.value=n.value.parentNode;o()}function E(){if(l.target===!1||l.target===""||i.$el.parentNode===null)n.value=null;else if(l.target===!0)S(i.$el.parentNode);else{let s=l.target;if(typeof l.target=="string")try{s=document.querySelector(l.target)}catch{s=void 0}s!=null?(n.value=s.$el||s,o()):(n.value=null,console.error(`Anchor: target "${l.target}" not found`))}}return k(()=>l.contextMenu,s=>{n.value!==null&&(f(),o(s))}),k(()=>l.target,()=>{n.value!==null&&f(),E()}),k(()=>l.noParentEvent,s=>{n.value!==null&&(s===!0?f():o())}),K(()=>{E(),t!==!0&&l.modelValue===!0&&n.value===null&&u("update:modelValue",!1)}),F(()=>{a!==null&&clearTimeout(a),f()}),{anchorEl:n,canShow:d,anchorEvents:c}}function ct(e,t){const o=q(null);let l;function i(a,d){const c=`${d!==void 0?"add":"remove"}EventListener`,f=d!==void 0?d:l;a!==window&&a[c]("scroll",f,me.passive),window[c]("scroll",f,me.passive),l=d}function u(){o.value!==null&&(i(o.value),o.value=null)}const n=k(()=>e.noParentEvent,()=>{o.value!==null&&(u(),t())});return F(n),{localScrollTarget:o,unconfigureScrollTarget:u,changeScrollEvent:i}}const ft={modelValue:{type:Boolean,default:null},"onUpdate:modelValue":[Function,Array]},vt=["beforeShow","show","beforeHide","hide"];function mt({showing:e,canShow:t,hideOnRouteChange:o,handleShow:l,handleHide:i,processOnMount:u}){const n=R(),{props:a,emit:d,proxy:c}=n;let f;function S(m){e.value===!0?v(m):E(m)}function E(m){if(a.disable===!0||m?.qAnchorHandled===!0||t!==void 0&&t(m)!==!0)return;const x=a["onUpdate:modelValue"]!==void 0;x===!0&&(d("update:modelValue",!0),f=m,U(()=>{f===m&&(f=void 0)})),(a.modelValue===null||x===!1)&&s(m)}function s(m){e.value!==!0&&(e.value=!0,d("beforeShow",m),l!==void 0?l(m):d("show",m))}function v(m){if(a.disable===!0)return;const x=a["onUpdate:modelValue"]!==void 0;x===!0&&(d("update:modelValue",!1),f=m,U(()=>{f===m&&(f=void 0)})),(a.modelValue===null||x===!1)&&T(m)}function T(m){e.value!==!1&&(e.value=!1,d("beforeHide",m),i!==void 0?i(m):d("hide",m))}function A(m){a.disable===!0&&m===!0?a["onUpdate:modelValue"]!==void 0&&d("update:modelValue",!1):m===!0!==e.value&&(m===!0?s:T)(f)}k(()=>a.modelValue,A),o!==void 0&&Qe(n)===!0&&k(()=>c.$route.fullPath,()=>{o.value===!0&&e.value===!0&&v()}),u===!0&&K(()=>{A(a.modelValue)});const P={show:E,hide:v,toggle:S};return Object.assign(c,P),P}let L=[],N=[];function De(e){N=N.filter(t=>t!==e)}function ht(e){De(e),N.push(e)}function xe(e){De(e),N.length===0&&L.length!==0&&(L[L.length-1](),L=[])}function gt(e){N.length===0?e():L.push(e)}function pt(e){L=L.filter(t=>t!==e)}const I=[];function Ut(e){return I.find(t=>t.contentEl!==null&&t.contentEl.contains(e))}function bt(e,t){do{if(e.$options.name==="QMenu"){if(e.hide(t),e.$props.separateClosePopup===!0)return te(e)}else if(e.__qPortal===!0){const o=te(e);return o?.$options.name==="QPopupProxy"?(e.hide(t),o):e}e=te(e)}while(e!=null)}function Nt(e,t,o){for(;o!==0&&e!==void 0&&e!==null;){if(e.__qPortal===!0){if(o--,e.$options.name==="QMenu"){e=bt(e,t);continue}e.hide(t)}e=te(e)}}const yt=He({name:"QPortal",setup(e,{slots:t}){return()=>t.default()}});function xt(e){for(e=e.parent;e!=null;){if(e.type.name==="QGlobalDialog")return!0;if(e.type.name==="QDialog"||e.type.name==="QMenu")return!1;e=e.parent}return!1}function Ct(e,t,o,l){const i=q(!1),u=q(!1);let n=null;const a={},d=l==="dialog"&&xt(e);function c(S){if(S===!0){xe(a),u.value=!0;return}u.value=!1,i.value===!1&&(d===!1&&n===null&&(n=Ke(!1,l)),i.value=!0,I.push(e.proxy),ht(a))}function f(S){if(u.value=!1,S!==!0)return;xe(a),i.value=!1;const E=I.indexOf(e.proxy);E!==-1&&I.splice(E,1),n!==null&&(Ge(n),n=null)}return Ue(()=>{f(!0)}),e.proxy.__qPortal=!0,Ve(e.proxy,"contentEl",()=>t.value),{showPortal:c,hidePortal:f,portalIsActive:i,portalIsAccessible:u,renderPortal:()=>d===!0?o():i.value===!0?[b(Ne,{to:n},b(yt,o))]:void 0}}const se={transitionShow:{type:String,default:"fade"},transitionHide:{type:String,default:"fade"},transitionDuration:{type:[String,Number],default:300}};function wt(e,t=()=>{},o=()=>{}){return{transitionProps:g(()=>{const l=`q-transition--${e.transitionShow||t()}`,i=`q-transition--${e.transitionHide||o()}`;return{appear:!0,enterFromClass:`${l}-enter-from`,enterActiveClass:`${l}-enter-active`,enterToClass:`${l}-enter-to`,leaveFromClass:`${i}-leave-from`,leaveActiveClass:`${i}-leave-active`,leaveToClass:`${i}-leave-to`}}),transitionStyle:g(()=>`--q-transition-duration: ${e.transitionDuration}ms`)}}function St(){let e;const t=R();function o(){e=void 0}return Re(o),F(o),{removeTick:o,registerTick(l){e=l,U(()=>{e===l&&(Ze(t)===!1&&e(),e=void 0)})}}}const{notPassiveCapture:le}=me,D=[];function ne(e){const t=e.target;if(t===void 0||t.nodeType===8||t.classList.contains("no-pointer-events")===!0)return;let o=I.length-1;for(;o>=0;){const l=I[o].$;if(l.type.name==="QTooltip"){o--;continue}if(l.type.name!=="QDialog")break;if(l.props.seamless!==!0)return;o--}for(let l=D.length-1;l>=0;l--){const i=D[l];if((i.anchorEl.value===null||i.anchorEl.value.contains(t)===!1)&&(t===document.body||i.innerRef.value!==null&&i.innerRef.value.contains(t)===!1))e.qClickOutside=!0,i.onClickOutside(e);else return}}function qt(e){D.push(e),D.length===1&&(document.addEventListener("mousedown",ne,le),document.addEventListener("touchstart",ne,le))}function Ce(e){const t=D.findIndex(o=>o===e);t!==-1&&(D.splice(t,1),D.length===0&&(document.removeEventListener("mousedown",ne,le),document.removeEventListener("touchstart",ne,le)))}let we,Se;function qe(e){const t=e.split(" ");return t.length!==2?!1:["top","center","bottom"].includes(t[0])!==!0?(console.error("Anchor/Self position must start with one of top/center/bottom"),!1):["left","middle","right","start","end"].includes(t[1])!==!0?(console.error("Anchor/Self position must end with one of left/middle/right/start/end"),!1):!0}function Et(e){return e?!(e.length!==2||typeof e[0]!="number"||typeof e[1]!="number"):!0}const ge={"start#ltr":"left","start#rtl":"right","end#ltr":"right","end#rtl":"left"};["left","middle","right"].forEach(e=>{ge[`${e}#ltr`]=e,ge[`${e}#rtl`]=e});function Ee(e,t){const o=e.split(" ");return{vertical:o[0],horizontal:ge[`${o[1]}#${t===!0?"rtl":"ltr"}`]}}function Tt(e,t){let{top:o,left:l,right:i,bottom:u,width:n,height:a}=e.getBoundingClientRect();return t!==void 0&&(o-=t[1],l-=t[0],u+=t[1],i+=t[0],n+=t[0],a+=t[1]),{top:o,bottom:u,height:a,left:l,right:i,width:n,middle:l+(i-l)/2,center:o+(u-o)/2}}function _t(e,t,o){let{top:l,left:i}=e.getBoundingClientRect();return l+=t.top,i+=t.left,o!==void 0&&(l+=o[1],i+=o[0]),{top:l,bottom:l+1,height:1,left:i,right:i+1,width:1,middle:i,center:l}}function At(e,t){return{top:0,center:t/2,bottom:t,left:0,middle:e/2,right:e}}function Te(e,t,o,l){return{top:e[o.vertical]-t[l.vertical],left:e[o.horizontal]-t[l.horizontal]}}function ze(e,t=0){if(e.targetEl===null||e.anchorEl===null||t>5)return;if(e.targetEl.offsetHeight===0||e.targetEl.offsetWidth===0){setTimeout(()=>{ze(e,t+1)},10);return}const{targetEl:o,offset:l,anchorEl:i,anchorOrigin:u,selfOrigin:n,absoluteOffset:a,fit:d,cover:c,maxHeight:f,maxWidth:S}=e;if(Fe.is.ios===!0&&window.visualViewport!==void 0){const C=document.body.style,{offsetLeft:y,offsetTop:w}=window.visualViewport;y!==we&&(C.setProperty("--q-pe-left",y+"px"),we=y),w!==Se&&(C.setProperty("--q-pe-top",w+"px"),Se=w)}const{scrollLeft:E,scrollTop:s}=o,v=a===void 0?Tt(i,c===!0?[0,0]:l):_t(i,a,l);Object.assign(o.style,{top:0,left:0,minWidth:null,minHeight:null,maxWidth:S,maxHeight:f,visibility:"visible"});const{offsetWidth:T,offsetHeight:A}=o,{elWidth:P,elHeight:m}=d===!0||c===!0?{elWidth:Math.max(v.width,T),elHeight:c===!0?Math.max(v.height,A):A}:{elWidth:T,elHeight:A};let x={maxWidth:S,maxHeight:f};(d===!0||c===!0)&&(x.minWidth=v.width+"px",c===!0&&(x.minHeight=v.height+"px")),Object.assign(o.style,x);const $=At(P,m);let h=Te(v,$,u,n);if(a===void 0||l===void 0)de(h,v,$,u,n);else{const{top:C,left:y}=h;de(h,v,$,u,n);let w=!1;if(h.top!==C){w=!0;const H=2*l[1];v.center=v.top-=H,v.bottom-=H+2}if(h.left!==y){w=!0;const H=2*l[0];v.middle=v.left-=H,v.right-=H+2}w===!0&&(h=Te(v,$,u,n),de(h,v,$,u,n))}x={top:h.top+"px",left:h.left+"px"},h.maxHeight!==void 0&&(x.maxHeight=h.maxHeight+"px",v.height>h.maxHeight&&(x.minHeight=x.maxHeight)),h.maxWidth!==void 0&&(x.maxWidth=h.maxWidth+"px",v.width>h.maxWidth&&(x.minWidth=x.maxWidth)),Object.assign(o.style,x),o.scrollTop!==s&&(o.scrollTop=s),o.scrollLeft!==E&&(o.scrollLeft=E)}function de(e,t,o,l,i){const u=o.bottom,n=o.right,a=Je(),d=window.innerHeight-a,c=document.body.clientWidth;if(e.top<0||e.top+u>d)if(i.vertical==="center")e.top=t[l.vertical]>d/2?Math.max(0,d-u):0,e.maxHeight=Math.min(u,d);else if(t[l.vertical]>d/2){const f=Math.min(d,l.vertical==="center"?t.center:l.vertical===i.vertical?t.bottom:t.top);e.maxHeight=Math.min(u,f),e.top=Math.max(0,f-u)}else e.top=Math.max(0,l.vertical==="center"?t.center:l.vertical===i.vertical?t.top:t.bottom),e.maxHeight=Math.min(u,d-e.top);if(e.left<0||e.left+n>c)if(e.maxWidth=Math.min(n,c),i.horizontal==="middle")e.left=t[l.horizontal]>c/2?Math.max(0,c-n):0;else if(t[l.horizontal]>c/2){const f=Math.min(c,l.horizontal==="middle"?t.middle:l.horizontal===i.horizontal?t.right:t.left);e.maxWidth=Math.min(n,f),e.left=Math.max(0,f-e.maxWidth)}else e.left=Math.max(0,l.horizontal==="middle"?t.middle:l.horizontal===i.horizontal?t.left:t.right),e.maxWidth=Math.min(n,c-e.left)}const Kt=He({name:"QTooltip",inheritAttrs:!1,props:{...Le,...ft,...se,maxHeight:{type:String,default:null},maxWidth:{type:String,default:null},transitionShow:{...se.transitionShow,default:"jump-down"},transitionHide:{...se.transitionHide,default:"jump-up"},anchor:{type:String,default:"bottom middle",validator:qe},self:{type:String,default:"top middle",validator:qe},offset:{type:Array,default:()=>[14,14],validator:Et},scrollTarget:Xe,delay:{type:Number,default:0},hideDelay:{type:Number,default:0},persistent:Boolean},emits:[...vt],setup(e,{slots:t,emit:o,attrs:l}){let i,u;const n=R(),{proxy:{$q:a}}=n,d=q(null),c=q(!1),f=g(()=>Ee(e.anchor,a.lang.rtl)),S=g(()=>Ee(e.self,a.lang.rtl)),E=g(()=>e.persistent!==!0),{registerTick:s,removeTick:v}=St(),{registerTimeout:T}=rt(),{transitionProps:A,transitionStyle:P}=wt(e),{localScrollTarget:m,changeScrollEvent:x,unconfigureScrollTarget:$}=ct(e,j),{anchorEl:h,canShow:C,anchorEvents:y}=dt({showing:c,configureAnchorEl:p}),{show:w,hide:H}=mt({showing:c,canShow:C,handleShow:ae,handleHide:re,hideOnRouteChange:E,processOnMount:!0});Object.assign(y,{delayShow:Z,delayHide:r});const{showPortal:O,hidePortal:z,renderPortal:ie}=Ct(n,d,Ie,"tooltip");if(a.platform.is.mobile===!0){const B={anchorEl:h,innerRef:d,onClickOutside(M){return H(M),M.target.classList.contains("q-dialog__backdrop")&&We(M),!0}},ue=g(()=>e.modelValue===null&&e.persistent!==!0&&c.value===!0);k(ue,M=>{(M===!0?qt:Ce)(B)}),F(()=>{Ce(B)})}function ae(B){O(),s(()=>{u=new MutationObserver(()=>V()),u.observe(d.value,{attributes:!1,childList:!0,characterData:!0,subtree:!0}),V(),j()}),i===void 0&&(i=k(()=>a.screen.width+"|"+a.screen.height+"|"+e.self+"|"+e.anchor+"|"+a.lang.rtl,V)),T(()=>{O(!0),o("show",B)},e.transitionDuration)}function re(B){v(),z(),G(),T(()=>{z(!0),o("hide",B)},e.transitionDuration)}function G(){u!==void 0&&(u.disconnect(),u=void 0),i!==void 0&&(i(),i=void 0),$(),ve(y,"tooltipTemp")}function V(){ze({targetEl:d.value,offset:e.offset,anchorEl:h.value,anchorOrigin:f.value,selfOrigin:S.value,maxHeight:e.maxHeight,maxWidth:e.maxWidth})}function Z(B){if(a.platform.is.mobile===!0){he(),document.body.classList.add("non-selectable");const ue=h.value,M=["touchmove","touchcancel","touchend","click"].map(be=>[ue,be,"delayHide","passiveCapture"]);oe(y,"tooltipTemp",M)}T(()=>{w(B)},e.delay)}function r(B){a.platform.is.mobile===!0&&(ve(y,"tooltipTemp"),he(),setTimeout(()=>{document.body.classList.remove("non-selectable")},10)),T(()=>{H(B)},e.hideDelay)}function p(){if(e.noParentEvent===!0||h.value===null)return;const B=a.platform.is.mobile===!0?[[h.value,"touchstart","delayShow","passive"]]:[[h.value,"mouseenter","delayShow","passive"],[h.value,"mouseleave","delayHide","passive"]];oe(y,"anchor",B)}function j(){if(h.value!==null||e.scrollTarget!==void 0){m.value=Ye(h.value,e.scrollTarget);const B=e.noParentEvent===!0?V:H;x(m.value,B)}}function J(){return c.value===!0?b("div",{...l,ref:d,class:["q-tooltip q-tooltip--style q-position-engine no-pointer-events",l.class],style:[l.style,P.value],role:"tooltip"},Q(t.default)):null}function Ie(){return b(Me,A.value,J)}return F(G),Object.assign(n.proxy,{updatePosition:V}),ie}});let ce,X=0;const _=new Array(256);for(let e=0;e<256;e++)_[e]=(e+256).toString(16).substring(1);const Pt=(()=>{const e=typeof crypto<"u"?crypto:typeof window<"u"?window.crypto||window.msCrypto:void 0;if(e!==void 0){if(e.randomBytes!==void 0)return e.randomBytes;if(e.getRandomValues!==void 0)return t=>{const o=new Uint8Array(t);return e.getRandomValues(o),o}}return t=>{const o=[];for(let l=t;l>0;l--)o.push(Math.floor(Math.random()*256));return o}})(),_e=4096;function pe(){(ce===void 0||X+16>_e)&&(X=0,ce=Pt(_e));const e=Array.prototype.slice.call(ce,X,X+=16);return e[6]=e[6]&15|64,e[8]=e[8]&63|128,_[e[0]]+_[e[1]]+_[e[2]]+_[e[3]]+"-"+_[e[4]]+_[e[5]]+"-"+_[e[6]]+_[e[7]]+"-"+_[e[8]]+_[e[9]]+"-"+_[e[10]]+_[e[11]]+_[e[12]]+_[e[13]]+_[e[14]]+_[e[15]]}function $t(e){return e??null}function Ae(e,t){return e??(t===!0?`f_${pe()}`:null)}function kt({getValue:e,required:t=!0}={}){if(et.value===!0){const o=e!==void 0?q($t(e())):q(null);return t===!0&&o.value===null&&K(()=>{o.value=`f_${pe()}`}),e!==void 0&&k(e,l=>{o.value=Ae(l,t)}),o}return e!==void 0?g(()=>Ae(e(),t)):q(`f_${pe()}`)}const Pe=/^on[A-Z]/;function Bt(){const{attrs:e,vnode:t}=R(),o={listeners:q({}),attributes:q({})};function l(){const i={},u={};for(const n in e)n!=="class"&&n!=="style"&&Pe.test(n)===!1&&(i[n]=e[n]);for(const n in t.props)Pe.test(n)===!0&&(u[n]=t.props[n]);o.attributes.value=i,o.listeners.value=u}return tt(l),l(),o}function Ht({validate:e,resetValidation:t,requiresQForm:o}){const l=ot(lt,!1);if(l!==!1){const{props:i,proxy:u}=R();Object.assign(u,{validate:e,resetValidation:t}),k(()=>i.disable,n=>{n===!0?(typeof t=="function"&&t(),l.unbindComponent(u)):l.bindComponent(u)}),K(()=>{i.disable!==!0&&l.bindComponent(u)}),F(()=>{i.disable!==!0&&l.unbindComponent(u)})}else o===!0&&console.error("Parent QForm not found on useFormChild()!")}const $e=/^#[0-9a-fA-F]{3}([0-9a-fA-F]{3})?$/,ke=/^#[0-9a-fA-F]{4}([0-9a-fA-F]{4})?$/,Be=/^#([0-9a-fA-F]{3}|[0-9a-fA-F]{4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/,Y=/^rgb\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5])\)$/,ee=/^rgba\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),(0|0\.[0-9]+[1-9]|0\.[1-9]+|1)\)$/,fe={date:e=>/^-?[\d]+\/[0-1]\d\/[0-3]\d$/.test(e),time:e=>/^([0-1]?\d|2[0-3]):[0-5]\d$/.test(e),fulltime:e=>/^([0-1]?\d|2[0-3]):[0-5]\d:[0-5]\d$/.test(e),timeOrFulltime:e=>/^([0-1]?\d|2[0-3]):[0-5]\d(:[0-5]\d)?$/.test(e),email:e=>/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(e),hexColor:e=>$e.test(e),hexaColor:e=>ke.test(e),hexOrHexaColor:e=>Be.test(e),rgbColor:e=>Y.test(e),rgbaColor:e=>ee.test(e),rgbOrRgbaColor:e=>Y.test(e)||ee.test(e),hexOrRgbColor:e=>$e.test(e)||Y.test(e),hexaOrRgbaColor:e=>ke.test(e)||ee.test(e),anyColor:e=>Be.test(e)||Y.test(e)||ee.test(e)},Vt=[!0,!1,"ondemand"],Rt={modelValue:{},error:{type:Boolean,default:null},errorMessage:String,noErrorIcon:Boolean,rules:Array,reactiveRules:Boolean,lazyRules:{type:[Boolean,String],default:!1,validator:e=>Vt.includes(e)}};function Ft(e,t){const{props:o,proxy:l}=R(),i=q(!1),u=q(null),n=q(!1);Ht({validate:T,resetValidation:v});let a=0,d;const c=g(()=>o.rules!==void 0&&o.rules!==null&&o.rules.length!==0),f=g(()=>o.disable!==!0&&c.value===!0&&t.value===!1),S=g(()=>o.error===!0||i.value===!0),E=g(()=>typeof o.errorMessage=="string"&&o.errorMessage.length!==0?o.errorMessage:u.value);k(()=>o.modelValue,()=>{n.value=!0,f.value===!0&&o.lazyRules===!1&&A()});function s(){o.lazyRules!=="ondemand"&&f.value===!0&&n.value===!0&&A()}k(()=>o.reactiveRules,P=>{P===!0?d===void 0&&(d=k(()=>o.rules,s,{immediate:!0,deep:!0})):d!==void 0&&(d(),d=void 0)},{immediate:!0}),k(()=>o.lazyRules,s),k(e,P=>{P===!0?n.value=!0:f.value===!0&&o.lazyRules!=="ondemand"&&A()});function v(){a++,t.value=!1,n.value=!1,i.value=!1,u.value=null,A.cancel()}function T(P=o.modelValue){if(o.disable===!0||c.value===!1)return!0;const m=++a,x=t.value!==!0?()=>{n.value=!0}:()=>{},$=(C,y)=>{C===!0&&x(),i.value=C,u.value=y||null,t.value=!1},h=[];for(let C=0;C<o.rules.length;C++){const y=o.rules[C];let w;if(typeof y=="function"?w=y(P,fe):typeof y=="string"&&fe[y]!==void 0&&(w=fe[y](P)),w===!1||typeof w=="string")return $(!0,w),!1;w!==!0&&w!==void 0&&h.push(w)}return h.length===0?($(!1),!0):(t.value=!0,Promise.all(h).then(C=>{if(C===void 0||Array.isArray(C)===!1||C.length===0)return m===a&&$(!1),!0;const y=C.find(w=>w===!1||typeof w=="string");return m===a&&$(y!==void 0,y),y===void 0},C=>(m===a&&(console.error(C),$(!0)),!1)))}const A=nt(T,0);return F(()=>{d?.(),A.cancel()}),Object.assign(l,{resetValidation:v,validate:T}),Ve(l,"hasError",()=>S.value),{isDirtyModel:n,hasRules:c,hasError:S,errorMessage:E,validate:T,resetValidation:v}}function Mt(e){return e!=null&&(""+e).length!==0}const Wt={...ut,...Rt,label:String,stackLabel:Boolean,hint:String,hideHint:Boolean,prefix:String,suffix:String,labelColor:String,color:String,bgColor:String,filled:Boolean,outlined:Boolean,borderless:Boolean,standout:[Boolean,String],square:Boolean,loading:Boolean,labelSlot:Boolean,bottomSlots:Boolean,hideBottomSpace:Boolean,rounded:Boolean,dense:Boolean,itemAligned:Boolean,counter:Boolean,clearable:Boolean,clearIcon:String,disable:Boolean,readonly:Boolean,autofocus:Boolean,for:String},Gt={...Wt,maxlength:[Number,String]},Zt=["update:modelValue","clear","focus","blur"];function Jt({requiredForAttr:e=!0,tagProp:t,changeEvent:o=!1}={}){const{props:l,proxy:i}=R(),u=st(l,i.$q),n=kt({required:e,getValue:()=>l.for});return{requiredForAttr:e,changeEvent:o,tag:t===!0?g(()=>l.tag):{value:"label"},isDark:u,editable:g(()=>l.disable!==!0&&l.readonly!==!0),innerLoading:q(!1),focused:q(!1),hasPopupOpen:!1,splitAttrs:Bt(),targetUid:n,rootRef:q(null),targetRef:q(null),controlRef:q(null)}}function Xt(e){const{props:t,emit:o,slots:l,attrs:i,proxy:u}=R(),{$q:n}=u;let a=null;e.hasValue===void 0&&(e.hasValue=g(()=>Mt(t.modelValue))),e.emitValue===void 0&&(e.emitValue=r=>{o("update:modelValue",r)}),e.controlEvents===void 0&&(e.controlEvents={onFocusin:H,onFocusout:O}),Object.assign(e,{clearValue:z,onControlFocusin:H,onControlFocusout:O,focus:y}),e.computedCounter===void 0&&(e.computedCounter=g(()=>{if(t.counter!==!1){const r=typeof t.modelValue=="string"||typeof t.modelValue=="number"?(""+t.modelValue).length:Array.isArray(t.modelValue)===!0?t.modelValue.length:0,p=t.maxlength!==void 0?t.maxlength:t.maxValues;return r+(p!==void 0?" / "+p:"")}}));const{isDirtyModel:d,hasRules:c,hasError:f,errorMessage:S,resetValidation:E}=Ft(e.focused,e.innerLoading),s=e.floatingLabel!==void 0?g(()=>t.stackLabel===!0||e.focused.value===!0||e.floatingLabel.value===!0):g(()=>t.stackLabel===!0||e.focused.value===!0||e.hasValue.value===!0),v=g(()=>t.bottomSlots===!0||t.hint!==void 0||c.value===!0||t.counter===!0||t.error!==null),T=g(()=>t.filled===!0?"filled":t.outlined===!0?"outlined":t.borderless===!0?"borderless":t.standout?"standout":"standard"),A=g(()=>`q-field row no-wrap items-start q-field--${T.value}`+(e.fieldClass!==void 0?` ${e.fieldClass.value}`:"")+(t.rounded===!0?" q-field--rounded":"")+(t.square===!0?" q-field--square":"")+(s.value===!0?" q-field--float":"")+(m.value===!0?" q-field--labeled":"")+(t.dense===!0?" q-field--dense":"")+(t.itemAligned===!0?" q-field--item-aligned q-item-type":"")+(e.isDark.value===!0?" q-field--dark":"")+(e.getControl===void 0?" q-field--auto-height":"")+(e.focused.value===!0?" q-field--focused":"")+(f.value===!0?" q-field--error":"")+(f.value===!0||e.focused.value===!0?" q-field--highlighted":"")+(t.hideBottomSpace!==!0&&v.value===!0?" q-field--with-bottom":"")+(t.disable===!0?" q-field--disabled":t.readonly===!0?" q-field--readonly":"")),P=g(()=>"q-field__control relative-position row no-wrap"+(t.bgColor!==void 0?` bg-${t.bgColor}`:"")+(f.value===!0?" text-negative":typeof t.standout=="string"&&t.standout.length!==0&&e.focused.value===!0?` ${t.standout}`:t.color!==void 0?` text-${t.color}`:"")),m=g(()=>t.labelSlot===!0||t.label!==void 0),x=g(()=>"q-field__label no-pointer-events absolute ellipsis"+(t.labelColor!==void 0&&f.value!==!0?` text-${t.labelColor}`:"")),$=g(()=>({id:e.targetUid.value,editable:e.editable.value,focused:e.focused.value,floatingLabel:s.value,modelValue:t.modelValue,emitValue:e.emitValue})),h=g(()=>{const r={};return e.targetUid.value&&(r.for=e.targetUid.value),t.disable===!0&&(r["aria-disabled"]="true"),r});function C(){const r=document.activeElement;let p=e.targetRef?.value;p&&(r===null||r.id!==e.targetUid.value)&&(p.hasAttribute("tabindex")===!0||(p=p.querySelector("[tabindex]")),p!==r&&p?.focus({preventScroll:!0}))}function y(){gt(C)}function w(){pt(C);const r=document.activeElement;r!==null&&e.rootRef.value.contains(r)&&r.blur()}function H(r){a!==null&&(clearTimeout(a),a=null),e.editable.value===!0&&e.focused.value===!1&&(e.focused.value=!0,o("focus",r))}function O(r,p){a!==null&&clearTimeout(a),a=setTimeout(()=>{a=null,!(document.hasFocus()===!0&&(e.hasPopupOpen===!0||e.controlRef===void 0||e.controlRef.value===null||e.controlRef.value.contains(document.activeElement)!==!1))&&(e.focused.value===!0&&(e.focused.value=!1,o("blur",r)),p?.())})}function z(r){We(r),n.platform.is.mobile!==!0?(e.targetRef?.value||e.rootRef.value).focus():e.rootRef.value.contains(document.activeElement)===!0&&document.activeElement.blur(),t.type==="file"&&(e.inputRef.value.value=null),o("update:modelValue",null),e.changeEvent===!0&&o("change",null),o("clear",t.modelValue),U(()=>{const p=d.value;E(),d.value=p})}function ie(r){[13,32].includes(r.keyCode)&&z(r)}function ae(){const r=[];return l.prepend!==void 0&&r.push(b("div",{class:"q-field__prepend q-field__marginal row no-wrap items-center",key:"prepend",onClick:W},l.prepend())),r.push(b("div",{class:"q-field__control-container col relative-position row no-wrap q-anchor--skip"},re())),f.value===!0&&t.noErrorIcon===!1&&r.push(V("error",[b(ye,{name:n.iconSet.field.error,color:"negative"})])),t.loading===!0||e.innerLoading.value===!0?r.push(V("inner-loading-append",l.loading!==void 0?l.loading():[b(at,{color:t.color})])):t.clearable===!0&&e.hasValue.value===!0&&e.editable.value===!0&&r.push(V("inner-clearable-append",[b(ye,{class:"q-field__focusable-action",name:t.clearIcon||n.iconSet.field.clear,tabindex:0,role:"button","aria-hidden":"false","aria-label":n.lang.label.clear,onKeyup:ie,onClick:z})])),l.append!==void 0&&r.push(b("div",{class:"q-field__append q-field__marginal row no-wrap items-center",key:"append",onClick:W},l.append())),e.getInnerAppend!==void 0&&r.push(V("inner-append",e.getInnerAppend())),e.getControlChild!==void 0&&r.push(e.getControlChild()),r}function re(){const r=[];return t.prefix!==void 0&&t.prefix!==null&&r.push(b("div",{class:"q-field__prefix no-pointer-events row items-center"},t.prefix)),e.getShadowControl!==void 0&&e.hasShadow.value===!0&&r.push(e.getShadowControl()),e.getControl!==void 0?r.push(e.getControl()):l.rawControl!==void 0?r.push(l.rawControl()):l.control!==void 0&&r.push(b("div",{ref:e.targetRef,class:"q-field__native row",tabindex:-1,...e.splitAttrs.attributes.value,"data-autofocus":t.autofocus===!0||void 0},l.control($.value))),m.value===!0&&r.push(b("div",{class:x.value},Q(l.label,t.label))),t.suffix!==void 0&&t.suffix!==null&&r.push(b("div",{class:"q-field__suffix no-pointer-events row items-center"},t.suffix)),r.concat(Q(l.default))}function G(){let r,p;f.value===!0?S.value!==null?(r=[b("div",{role:"alert"},S.value)],p=`q--slot-error-${S.value}`):(r=Q(l.error),p="q--slot-error"):(t.hideHint!==!0||e.focused.value===!0)&&(t.hint!==void 0?(r=[b("div",t.hint)],p=`q--slot-hint-${t.hint}`):(r=Q(l.hint),p="q--slot-hint"));const j=t.counter===!0||l.counter!==void 0;if(t.hideBottomSpace===!0&&j===!1&&r===void 0)return;const J=b("div",{key:p,class:"q-field__messages col"},r);return b("div",{class:"q-field__bottom row items-start q-field__bottom--"+(t.hideBottomSpace!==!0?"animated":"stale"),onClick:W},[t.hideBottomSpace===!0?J:b(Me,{name:"q-transition--field-message"},()=>J),j===!0?b("div",{class:"q-field__counter"},l.counter!==void 0?l.counter():e.computedCounter.value):null])}function V(r,p){return p===null?null:b("div",{key:r,class:"q-field__append q-field__marginal row no-wrap items-center q-anchor--skip"},p)}let Z=!1;return Re(()=>{Z=!0}),it(()=>{Z===!0&&t.autofocus===!0&&u.focus()}),t.autofocus===!0&&K(()=>{u.focus()}),F(()=>{a!==null&&clearTimeout(a)}),Object.assign(u,{focus:y,blur:w}),function(){const p=e.getControl===void 0&&l.control===void 0?{...e.splitAttrs.attributes.value,"data-autofocus":t.autofocus===!0||void 0,...h.value}:h.value;return b(e.tag.value,{ref:e.rootRef,class:[A.value,i.class],style:i.style,...p},[l.before!==void 0?b("div",{class:"q-field__before q-field__marginal row no-wrap items-center",onClick:W},l.before()):null,b("div",{class:"q-field__inner relative-position col self-stretch"},[b("div",{ref:e.controlRef,class:P.value,tabindex:-1,...e.controlEvents},ae()),v.value===!0?G():null]),l.after!==void 0?b("div",{class:"q-field__after q-field__marginal row no-wrap items-center",onClick:W},l.after()):null])}}const Yt={name:String};function eo(e){return g(()=>({type:"hidden",name:e.name,value:e.modelValue}))}function to(e={}){return(t,o,l)=>{t[o](b("input",{class:"hidden"+(l||""),...e.value}))}}function oo(e){return g(()=>e.name||e.for)}const Lt=/[\u3000-\u303f\u3040-\u309f\u30a0-\u30ff\uff00-\uff9f\u4e00-\u9faf\u3400-\u4dbf]/,Dt=/[\u4e00-\u9fff\u3400-\u4dbf\u{20000}-\u{2a6df}\u{2a700}-\u{2b73f}\u{2b740}-\u{2b81f}\u{2b820}-\u{2ceaf}\uf900-\ufaff\u3300-\u33ff\ufe30-\ufe4f\uf900-\ufaff\u{2f800}-\u{2fa1f}]/u,zt=/[\u3131-\u314e\u314f-\u3163\uac00-\ud7a3]/,It=/[a-z0-9_ -]$/i;function lo(e){return function(o){if(o.type==="compositionend"||o.type==="change"){if(o.target.qComposing!==!0)return;o.target.qComposing=!1,e(o)}else o.type==="compositionupdate"&&o.target.qComposing!==!0&&typeof o.data=="string"&&(Fe.is.firefox===!0?It.test(o.data)===!1:Lt.test(o.data)===!0||Dt.test(o.data)===!0||zt.test(o.data)===!0)===!0&&(o.target.qComposing=!0)}}export{qe as A,ct as B,Ee as C,qt as D,Ce as E,bt as F,ze as G,Kt as Q,ut as a,ft as b,st as c,mt as d,Yt as e,to as f,eo as g,Ut as h,Nt as i,St as j,Qt as k,dt as l,he as m,gt as n,Zt as o,Gt as p,oo as q,Mt as r,Jt as s,lo as t,vt as u,Xt as v,se as w,wt as x,Ct as y,Et as z};
