import{o as ct,p as $e,v as dt,s as ft,e as Tt,q as Bt,r as ot,t as Ht}from"./use-key-composition-CoMUTTxZ.js";import{c as vt,u as _t,g as mt,r as q,a as h,w as me,n as J,aH as Lt,L as Rt,al as Dt,am as Pt,o as St,h as b,aI as Ce,ak as Kt,ay as Pe,a8 as fe,aJ as Nt,aK as $t,Q as jt,az as ve,ac as Qt,C as Ut}from"./index-CzmOWWdj.js";import{c as Wt,b as Xt,Q as Yt}from"./QDialog-Cuvxr_1w.js";import{n as it,Q as Jt,a as Gt}from"./focusout-C-pmmZED.js";import{Q as Zt}from"./QMenu-D3shCIOy.js";const el=vt({name:"QField",inheritAttrs:!1,props:{...$e,tag:{type:String,default:"label"}},emits:ct,setup(){return dt(ft({tagProp:!0}))}});let Te=!1;{const e=document.createElement("div");e.setAttribute("dir","rtl"),Object.assign(e.style,{width:"1px",height:"1px",overflow:"auto"});const r=document.createElement("div");Object.assign(r.style,{width:"1000px",height:"1px"}),document.body.appendChild(e),e.appendChild(r),e.scrollLeft=-1e3,Te=e.scrollLeft>=0,e.remove()}const N=1e3,tl=["start","center","end","start-force","center-force","end-force"],gt=Array.prototype.filter,ll=window.getComputedStyle(document.body).overflowAnchor===void 0?_t:function(e,r){e!==null&&(e._qOverflowAnimationFrame!==void 0&&cancelAnimationFrame(e._qOverflowAnimationFrame),e._qOverflowAnimationFrame=requestAnimationFrame(()=>{if(e===null)return;e._qOverflowAnimationFrame=void 0;const a=e.children||[];gt.call(a,F=>F.dataset&&F.dataset.qVsAnchor!==void 0).forEach(F=>{delete F.dataset.qVsAnchor});const m=a[r];m?.dataset&&(m.dataset.qVsAnchor="")}))};function Se(e,r){return e+r}function Ke(e,r,a,m,F,i,E,y){const S=e===window?document.scrollingElement||document.documentElement:e,T=F===!0?"offsetWidth":"offsetHeight",s={scrollStart:0,scrollViewSize:-E-y,scrollMaxSize:0,offsetStart:-E,offsetEnd:-y};if(F===!0?(e===window?(s.scrollStart=window.pageXOffset||window.scrollX||document.body.scrollLeft||0,s.scrollViewSize+=document.documentElement.clientWidth):(s.scrollStart=S.scrollLeft,s.scrollViewSize+=S.clientWidth),s.scrollMaxSize=S.scrollWidth,i===!0&&(s.scrollStart=(Te===!0?s.scrollMaxSize-s.scrollViewSize:0)-s.scrollStart)):(e===window?(s.scrollStart=window.pageYOffset||window.scrollY||document.body.scrollTop||0,s.scrollViewSize+=document.documentElement.clientHeight):(s.scrollStart=S.scrollTop,s.scrollViewSize+=S.clientHeight),s.scrollMaxSize=S.scrollHeight),a!==null)for(let w=a.previousElementSibling;w!==null;w=w.previousElementSibling)w.classList.contains("q-virtual-scroll--skip")===!1&&(s.offsetStart+=w[T]);if(m!==null)for(let w=m.nextElementSibling;w!==null;w=w.nextElementSibling)w.classList.contains("q-virtual-scroll--skip")===!1&&(s.offsetEnd+=w[T]);if(r!==e){const w=S.getBoundingClientRect(),V=r.getBoundingClientRect();F===!0?(s.offsetStart+=V.left-w.left,s.offsetEnd-=V.width):(s.offsetStart+=V.top-w.top,s.offsetEnd-=V.height),e!==window&&(s.offsetStart+=s.scrollStart),s.offsetEnd+=s.scrollMaxSize-s.offsetStart}return s}function at(e,r,a,m){r==="end"&&(r=(e===window?document.body:e)[a===!0?"scrollWidth":"scrollHeight"]),e===window?a===!0?(m===!0&&(r=(Te===!0?document.body.scrollWidth-document.documentElement.clientWidth:0)-r),window.scrollTo(r,window.pageYOffset||window.scrollY||document.body.scrollTop||0)):window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,r):a===!0?(m===!0&&(r=(Te===!0?e.scrollWidth-e.offsetWidth:0)-r),e.scrollLeft=r):e.scrollTop=r}function xe(e,r,a,m){if(a>=m)return 0;const F=r.length,i=Math.floor(a/N),E=Math.floor((m-1)/N)+1;let y=e.slice(i,E).reduce(Se,0);return a%N!==0&&(y-=r.slice(i*N,a).reduce(Se,0)),m%N!==0&&m!==F&&(y-=r.slice(m,E*N).reduce(Se,0)),y}const ht={virtualScrollSliceSize:{type:[Number,String],default:10},virtualScrollSliceRatioBefore:{type:[Number,String],default:1},virtualScrollSliceRatioAfter:{type:[Number,String],default:1},virtualScrollItemSize:{type:[Number,String],default:24},virtualScrollStickySizeStart:{type:[Number,String],default:0},virtualScrollStickySizeEnd:{type:[Number,String],default:0},tableColspan:[Number,String]},dl=Object.keys(ht),rt={virtualScrollHorizontal:Boolean,onVirtualScroll:Function,...ht};function ul({virtualScrollLength:e,getVirtualScrollTarget:r,getVirtualScrollEl:a,virtualScrollItemSizeComputed:m}){const F=mt(),{props:i,emit:E,proxy:y}=F,{$q:S}=y;let T,s,w,V=[],k;const I=q(0),K=q(0),H=q({}),Q=q(null),W=q(null),L=q(null),z=q({from:0,to:0}),ke=h(()=>i.tableColspan!==void 0?i.tableColspan:100);m===void 0&&(m=h(()=>i.virtualScrollItemSize));const _=h(()=>m.value+";"+i.virtualScrollHorizontal),X=h(()=>_.value+";"+i.virtualScrollSliceRatioBefore+";"+i.virtualScrollSliceRatioAfter);me(X,()=>{$()}),me(_,G);function G(){ne(s,!0)}function ge(l){ne(l===void 0?s:l)}function Z(l,o){const d=r();if(d==null||d.nodeType===8)return;const x=Ke(d,a(),Q.value,W.value,i.virtualScrollHorizontal,S.lang.rtl,i.virtualScrollStickySizeStart,i.virtualScrollStickySizeEnd);w!==x.scrollViewSize&&$(x.scrollViewSize),R(d,x,Math.min(e.value-1,Math.max(0,parseInt(l,10)||0)),0,tl.indexOf(o)!==-1?o:s!==-1&&l>s?"end":"start")}function Ae(){const l=r();if(l==null||l.nodeType===8)return;const o=Ke(l,a(),Q.value,W.value,i.virtualScrollHorizontal,S.lang.rtl,i.virtualScrollStickySizeStart,i.virtualScrollStickySizeEnd),d=e.value-1,x=o.scrollMaxSize-o.offsetStart-o.offsetEnd-K.value;if(T===o.scrollStart)return;if(o.scrollMaxSize<=0){R(l,o,0,0);return}w!==o.scrollViewSize&&$(o.scrollViewSize),he(z.value.from);const M=Math.floor(o.scrollMaxSize-Math.max(o.scrollViewSize,o.offsetEnd)-Math.min(k[d],o.scrollViewSize/2));if(M>0&&Math.ceil(o.scrollStart)>=M){R(l,o,d,o.scrollMaxSize-o.offsetEnd-V.reduce(Se,0));return}let g=0,v=o.scrollStart-o.offsetStart,O=v;if(v<=x&&v+o.scrollViewSize>=I.value)v-=I.value,g=z.value.from,O=v;else for(let f=0;v>=V[f]&&g<d;f++)v-=V[f],g+=N;for(;v>0&&g<d;)v-=k[g],v>-o.scrollViewSize?(g++,O=v):O=k[g]+v;R(l,o,g,O)}function R(l,o,d,x,M){const g=typeof M=="string"&&M.indexOf("-force")!==-1,v=g===!0?M.replace("-force",""):M,O=v!==void 0?v:"start";let f=Math.max(0,d-H.value[O]),D=f+H.value.total;D>e.value&&(D=e.value,f=Math.max(0,D-H.value.total)),T=o.scrollStart;const Y=f!==z.value.from||D!==z.value.to;if(Y===!1&&v===void 0){ye(d);return}const{activeElement:Ie}=document,U=L.value;Y===!0&&U!==null&&U!==Ie&&U.contains(Ie)===!0&&(U.addEventListener("focusout",ze),setTimeout(()=>{U?.removeEventListener("focusout",ze)})),ll(U,d-f);const Me=v!==void 0?k.slice(f,d).reduce(Se,0):0;if(Y===!0){const ee=D>=z.value.from&&f<=z.value.to?z.value.to:D;z.value={from:f,to:ee},I.value=xe(V,k,0,f),K.value=xe(V,k,D,e.value),requestAnimationFrame(()=>{z.value.to!==D&&T===o.scrollStart&&(z.value={from:z.value.from,to:D},K.value=xe(V,k,D,e.value))})}requestAnimationFrame(()=>{if(T!==o.scrollStart)return;Y===!0&&he(f);const ee=k.slice(f,d).reduce(Se,0),te=ee+o.offsetStart+I.value,Oe=te+k[d];let be=te+x;if(v!==void 0){const He=ee-Me,Ve=o.scrollStart+He;be=g!==!0&&Ve<te&&Oe<Ve+o.scrollViewSize?Ve:v==="end"?Oe-o.scrollViewSize:te-(v==="start"?0:Math.round((o.scrollViewSize-k[d])/2))}T=be,at(l,be,i.virtualScrollHorizontal,S.lang.rtl),ye(d)})}function he(l){const o=L.value;if(o){const d=gt.call(o.children,f=>f.classList&&f.classList.contains("q-virtual-scroll--skip")===!1),x=d.length,M=i.virtualScrollHorizontal===!0?f=>f.getBoundingClientRect().width:f=>f.offsetHeight;let g=l,v,O;for(let f=0;f<x;){for(v=M(d[f]),f++;f<x&&d[f].classList.contains("q-virtual-scroll--with-prev")===!0;)v+=M(d[f]),f++;O=v-k[g],O!==0&&(k[g]+=O,V[Math.floor(g/N)]+=O),g++}}}function ze(){L.value?.focus()}function ne(l,o){const d=1*m.value;(o===!0||Array.isArray(k)===!1)&&(k=[]);const x=k.length;k.length=e.value;for(let g=e.value-1;g>=x;g--)k[g]=d;const M=Math.floor((e.value-1)/N);V=[];for(let g=0;g<=M;g++){let v=0;const O=Math.min((g+1)*N,e.value);for(let f=g*N;f<O;f++)v+=k[f];V.push(v)}s=-1,T=void 0,I.value=xe(V,k,0,z.value.from),K.value=xe(V,k,z.value.to,e.value),l>=0?(he(z.value.from),J(()=>{Z(l)})):oe()}function $(l){if(l===void 0&&typeof window<"u"){const v=r();v!=null&&v.nodeType!==8&&(l=Ke(v,a(),Q.value,W.value,i.virtualScrollHorizontal,S.lang.rtl,i.virtualScrollStickySizeStart,i.virtualScrollStickySizeEnd).scrollViewSize)}w=l;const o=parseFloat(i.virtualScrollSliceRatioBefore)||0,d=parseFloat(i.virtualScrollSliceRatioAfter)||0,x=1+o+d,M=l===void 0||l<=0?1:Math.ceil(l/m.value),g=Math.max(1,M,Math.ceil((i.virtualScrollSliceSize>0?i.virtualScrollSliceSize:10)/x));H.value={total:Math.ceil(g*x),start:Math.ceil(g*o),center:Math.ceil(g*(.5+o)),end:Math.ceil(g*(1+o)),view:M}}function Be(l,o){const d=i.virtualScrollHorizontal===!0?"width":"height",x={["--q-virtual-scroll-item-"+d]:m.value+"px"};return[l==="tbody"?b(l,{class:"q-virtual-scroll__padding",key:"before",ref:Q},[b("tr",[b("td",{style:{[d]:`${I.value}px`,...x},colspan:ke.value})])]):b(l,{class:"q-virtual-scroll__padding",key:"before",ref:Q,style:{[d]:`${I.value}px`,...x}}),b(l,{class:"q-virtual-scroll__content",key:"content",ref:L,tabindex:-1},o.flat()),l==="tbody"?b(l,{class:"q-virtual-scroll__padding",key:"after",ref:W},[b("tr",[b("td",{style:{[d]:`${K.value}px`,...x},colspan:ke.value})])]):b(l,{class:"q-virtual-scroll__padding",key:"after",ref:W,style:{[d]:`${K.value}px`,...x}})]}function ye(l){s!==l&&(i.onVirtualScroll!==void 0&&E("virtualScroll",{index:l,from:z.value.from,to:z.value.to-1,direction:l<s?"decrease":"increase",ref:y}),s=l)}$();const oe=Lt(Ae,S.platform.is.ios===!0?120:35);Rt(()=>{$()});let we=!1;return Dt(()=>{we=!0}),Pt(()=>{if(we!==!0)return;const l=r();T!==void 0&&l!==void 0&&l!==null&&l.nodeType!==8?at(l,T,i.virtualScrollHorizontal,S.lang.rtl):Z(s)}),St(()=>{oe.cancel()}),Object.assign(y,{scrollTo:Z,reset:G,refresh:ge}),{virtualScrollSliceRange:z,virtualScrollSliceSizeComputed:H,setVirtualScrollSize:$,onVirtualScrollEvt:oe,localResetVirtualScroll:ne,padVirtualScroll:Be,scrollTo:Z,reset:G,refresh:ge}}const st=e=>["add","add-unique","toggle"].includes(e),nl=".*+?^${}()|[]\\",ol=Object.keys($e);function Ne(e,r){if(typeof e=="function")return e;const a=e!==void 0?e:r;return m=>m!==null&&typeof m=="object"&&a in m?m[a]:m}const fl=vt({name:"QSelect",inheritAttrs:!1,props:{...rt,...Tt,...$e,modelValue:{required:!0},multiple:Boolean,displayValue:[String,Number],displayValueHtml:Boolean,dropdownIcon:String,options:{type:Array,default:()=>[]},optionValue:[Function,String],optionLabel:[Function,String],optionDisable:[Function,String],hideSelected:Boolean,hideDropdownIcon:Boolean,fillInput:Boolean,maxValues:[Number,String],optionsDense:Boolean,optionsDark:{type:Boolean,default:null},optionsSelectedClass:String,optionsHtml:Boolean,optionsCover:Boolean,menuShrink:Boolean,menuAnchor:String,menuSelf:String,menuOffset:Array,popupContentClass:String,popupContentStyle:[String,Array,Object],popupNoRouteDismiss:Boolean,useInput:Boolean,useChips:Boolean,newValueMode:{type:String,validator:st},mapOptions:Boolean,emitValue:Boolean,disableTabSelection:Boolean,inputDebounce:{type:[Number,String],default:500},inputClass:[Array,String,Object],inputStyle:[Array,String,Object],tabindex:{type:[String,Number],default:0},autocomplete:String,transitionShow:{},transitionHide:{},transitionDuration:{},behavior:{type:String,validator:e=>["default","menu","dialog"].includes(e),default:"default"},virtualScrollItemSize:rt.virtualScrollItemSize.type,onNewValue:Function,onFilter:Function},emits:[...ct,"add","remove","inputValue","keyup","keypress","keydown","popupShow","popupHide","filterAbort"],setup(e,{slots:r,emit:a}){const{proxy:m}=mt(),{$q:F}=m,i=q(!1),E=q(!1),y=q(-1),S=q(""),T=q(!1),s=q(!1);let w=null,V=null,k,I,K,H=null,Q,W,L,z;const ke=q(null),_=q(null),X=q(null),G=q(null),ge=q(null),Z=Bt(e),Ae=Ht(et),R=h(()=>Array.isArray(e.options)?e.options.length:0),he=h(()=>e.virtualScrollItemSize===void 0?e.optionsDense===!0?24:48:e.virtualScrollItemSize),{virtualScrollSliceRange:ze,virtualScrollSliceSizeComputed:ne,localResetVirtualScroll:$,padVirtualScroll:Be,onVirtualScrollEvt:ye,scrollTo:oe,setVirtualScrollSize:we}=ul({virtualScrollLength:R,getVirtualScrollTarget:Vt,getVirtualScrollEl:Ge,virtualScrollItemSizeComputed:he}),l=ft(),o=h(()=>{const t=e.mapOptions===!0&&e.multiple!==!0,u=e.modelValue!==void 0&&(e.modelValue!==null||t===!0)?e.multiple===!0&&Array.isArray(e.modelValue)?e.modelValue:[e.modelValue]:[];if(e.mapOptions===!0&&Array.isArray(e.options)===!0){const n=e.mapOptions===!0&&k!==void 0?k:[],c=u.map(C=>bt(C,n));return e.modelValue===null&&t===!0?c.filter(C=>C!==null):c}return u}),d=h(()=>{const t={};return ol.forEach(u=>{const n=e[u];n!==void 0&&(t[u]=n)}),t}),x=h(()=>e.optionsDark===null?l.isDark.value:e.optionsDark),M=h(()=>ot(o.value)),g=h(()=>{let t="q-field__input q-placeholder col";return e.hideSelected===!0||o.value.length===0?[t,e.inputClass]:(t+=" q-field__input--padding",e.inputClass===void 0?t:[t,e.inputClass])}),v=h(()=>(e.virtualScrollHorizontal===!0?"q-virtual-scroll--horizontal":"")+(e.popupContentClass?" "+e.popupContentClass:"")),O=h(()=>R.value===0),f=h(()=>o.value.map(t=>P.value(t)).join(", ")),D=h(()=>e.displayValue!==void 0?e.displayValue:f.value),Y=h(()=>e.optionsHtml===!0?()=>!0:t=>t?.html===!0),Ie=h(()=>e.displayValueHtml===!0||e.displayValue===void 0&&(e.optionsHtml===!0||o.value.some(Y.value))),U=h(()=>l.focused.value===!0?e.tabindex:-1),Me=h(()=>{const t={tabindex:e.tabindex,role:"combobox","aria-label":e.label,"aria-readonly":e.readonly===!0?"true":"false","aria-autocomplete":e.useInput===!0?"list":"none","aria-expanded":i.value===!0?"true":"false","aria-controls":`${l.targetUid.value}_lb`};return y.value>=0&&(t["aria-activedescendant"]=`${l.targetUid.value}_${y.value}`),t}),ee=h(()=>({id:`${l.targetUid.value}_lb`,role:"listbox","aria-multiselectable":e.multiple===!0?"true":"false"})),te=h(()=>o.value.map((t,u)=>({index:u,opt:t,html:Y.value(t),selected:!0,removeAtIndex:wt,toggleOption:le,tabindex:U.value}))),Oe=h(()=>{if(R.value===0)return[];const{from:t,to:u}=ze.value;return e.options.slice(t,u).map((n,c)=>{const C=ie.value(n)===!0,p=Le(n)===!0,B=t+c,A={clickable:!0,active:p,activeClass:Ve.value,manualFocus:!0,focused:!1,disable:C,tabindex:-1,dense:e.optionsDense,dark:x.value,role:"option","aria-selected":p===!0?"true":"false",id:`${l.targetUid.value}_${B}`,onClick:()=>{le(n)}};return C!==!0&&(y.value===B&&(A.focused=!0),F.platform.is.desktop===!0&&(A.onMousemove=()=>{i.value===!0&&ae(B)})),{index:B,opt:n,html:Y.value(n),label:P.value(n),selected:A.active,focused:A.focused,toggleOption:le,setOptionIndex:ae,itemProps:A}})}),be=h(()=>e.dropdownIcon!==void 0?e.dropdownIcon:F.iconSet.arrow.dropdown),He=h(()=>e.optionsCover===!1&&e.outlined!==!0&&e.standout!==!0&&e.borderless!==!0&&e.rounded!==!0),Ve=h(()=>e.optionsSelectedClass!==void 0?e.optionsSelectedClass:e.color!==void 0?`text-${e.color}`:""),j=h(()=>Ne(e.optionValue,"value")),P=h(()=>Ne(e.optionLabel,"label")),ie=h(()=>Ne(e.optionDisable,"disable")),Fe=h(()=>o.value.map(j.value)),yt=h(()=>{const t={onInput:et,onChange:Ae,onKeydown:Je,onKeyup:Xe,onKeypress:Ye,onFocus:Ue,onClick(u){I===!0&&ve(u)}};return t.onCompositionstart=t.onCompositionupdate=t.onCompositionend=Ae,t});me(o,t=>{k=t,e.useInput===!0&&e.fillInput===!0&&e.multiple!==!0&&l.innerLoading.value!==!0&&(E.value!==!0&&i.value!==!0||M.value!==!0)&&(K!==!0&&de(),(E.value===!0||i.value===!0)&&re(""))},{immediate:!0}),me(()=>e.fillInput,de),me(i,Re),me(R,Et);function je(t){return e.emitValue===!0?j.value(t):t}function _e(t){if(t!==-1&&t<o.value.length)if(e.multiple===!0){const u=e.modelValue.slice();a("remove",{index:t,value:u.splice(t,1)[0]}),a("update:modelValue",u)}else a("update:modelValue",null)}function wt(t){_e(t),l.focus()}function Qe(t,u){const n=je(t);if(e.multiple!==!0){e.fillInput===!0&&pe(P.value(t),!0,!0),a("update:modelValue",n);return}if(o.value.length===0){a("add",{index:0,value:n}),a("update:modelValue",e.multiple===!0?[n]:n);return}if(u===!0&&Le(t)===!0||e.maxValues!==void 0&&e.modelValue.length>=e.maxValues)return;const c=e.modelValue.slice();a("add",{index:c.length,value:n}),c.push(n),a("update:modelValue",c)}function le(t,u){if(l.editable.value!==!0||t===void 0||ie.value(t)===!0)return;const n=j.value(t);if(e.multiple!==!0){u!==!0&&(pe(e.fillInput===!0?P.value(t):"",!0,!0),ue()),_.value?.focus(),(o.value.length===0||Ce(j.value(o.value[0]),n)!==!0)&&a("update:modelValue",e.emitValue===!0?n:t);return}if((I!==!0||T.value===!0)&&l.focus(),Ue(),o.value.length===0){const p=e.emitValue===!0?n:t;a("add",{index:0,value:p}),a("update:modelValue",e.multiple===!0?[p]:p);return}const c=e.modelValue.slice(),C=Fe.value.findIndex(p=>Ce(p,n));if(C!==-1)a("remove",{index:C,value:c.splice(C,1)[0]});else{if(e.maxValues!==void 0&&c.length>=e.maxValues)return;const p=e.emitValue===!0?n:t;a("add",{index:c.length,value:p}),c.push(p)}a("update:modelValue",c)}function ae(t){if(F.platform.is.desktop!==!0)return;const u=t!==-1&&t<R.value?t:-1;y.value!==u&&(y.value=u)}function qe(t=1,u){if(i.value===!0){let n=y.value;do n=it(n+t,-1,R.value-1);while(n!==-1&&n!==y.value&&ie.value(e.options[n])===!0);y.value!==n&&(ae(n),oe(n),u!==!0&&e.useInput===!0&&e.fillInput===!0&&Ee(n>=0?P.value(e.options[n]):Q,!0))}}function bt(t,u){const n=c=>Ce(j.value(c),t);return e.options.find(n)||u.find(n)||t}function Le(t){const u=j.value(t);return Fe.value.find(n=>Ce(n,u))!==void 0}function Ue(t){e.useInput===!0&&_.value!==null&&(t===void 0||_.value===t.target&&t.target.value===f.value)&&_.value.select()}function We(t){Qt(t,27)===!0&&i.value===!0&&(ve(t),ue(),de()),a("keyup",t)}function Xe(t){const{value:u}=t.target;if(t.keyCode!==void 0){We(t);return}if(t.target.value="",w!==null&&(clearTimeout(w),w=null),V!==null&&(clearTimeout(V),V=null),de(),typeof u=="string"&&u.length!==0){const n=u.toLocaleLowerCase(),c=p=>{const B=e.options.find(A=>String(p.value(A)).toLocaleLowerCase()===n);return B===void 0?!1:(o.value.indexOf(B)===-1?le(B):ue(),!0)},C=p=>{c(j)!==!0&&p!==!0&&c(P)!==!0&&re(u,!0,()=>C(!0))};C()}else l.clearValue(t)}function Ye(t){a("keypress",t)}function Je(t){if(a("keydown",t),Kt(t)===!0)return;const u=S.value.length!==0&&(e.newValueMode!==void 0||e.onNewValue!==void 0),n=t.shiftKey!==!0&&e.disableTabSelection!==!0&&e.multiple!==!0&&(y.value!==-1||u===!0);if(t.keyCode===27){Pe(t);return}if(t.keyCode===9&&n===!1){se();return}if(t.target===void 0||t.target.id!==l.targetUid.value||l.editable.value!==!0)return;if(t.keyCode===40&&l.innerLoading.value!==!0&&i.value===!1){fe(t),ce();return}if(t.keyCode===8&&(e.useChips===!0||e.clearable===!0)&&e.hideSelected!==!0&&S.value.length===0){e.multiple===!0&&Array.isArray(e.modelValue)===!0?_e(e.modelValue.length-1):e.multiple!==!0&&e.modelValue!==null&&a("update:modelValue",null);return}(t.keyCode===35||t.keyCode===36)&&(typeof S.value!="string"||S.value.length===0)&&(fe(t),y.value=-1,qe(t.keyCode===36?1:-1,e.multiple)),(t.keyCode===33||t.keyCode===34)&&ne.value!==void 0&&(fe(t),y.value=Math.max(-1,Math.min(R.value,y.value+(t.keyCode===33?-1:1)*ne.value.view)),qe(t.keyCode===33?1:-1,e.multiple)),(t.keyCode===38||t.keyCode===40)&&(fe(t),qe(t.keyCode===38?-1:1,e.multiple));const c=R.value;if((L===void 0||z<Date.now())&&(L=""),c>0&&e.useInput!==!0&&t.key!==void 0&&t.key.length===1&&t.altKey===!1&&t.ctrlKey===!1&&t.metaKey===!1&&(t.keyCode!==32||L.length!==0)){i.value!==!0&&ce(t);const C=t.key.toLocaleLowerCase(),p=L.length===1&&L[0]===C;z=Date.now()+1500,p===!1&&(fe(t),L+=C);const B=new RegExp("^"+L.split("").map(De=>nl.indexOf(De)!==-1?"\\"+De:De).join(".*"),"i");let A=y.value;if(p===!0||A<0||B.test(P.value(e.options[A]))!==!0)do A=it(A+1,-1,c-1);while(A!==y.value&&(ie.value(e.options[A])===!0||B.test(P.value(e.options[A]))!==!0));y.value!==A&&J(()=>{ae(A),oe(A),A>=0&&e.useInput===!0&&e.fillInput===!0&&Ee(P.value(e.options[A]),!0)});return}if(!(t.keyCode!==13&&(t.keyCode!==32||e.useInput===!0||L!=="")&&(t.keyCode!==9||n===!1))){if(t.keyCode!==9&&fe(t),y.value!==-1&&y.value<c){le(e.options[y.value]);return}if(u===!0){const C=(p,B)=>{if(B){if(st(B)!==!0)return}else B=e.newValueMode;if(pe("",e.multiple!==!0,!0),p==null)return;(B==="toggle"?le:Qe)(p,B==="add-unique"),e.multiple!==!0&&(_.value?.focus(),ue())};if(e.onNewValue!==void 0?a("newValue",S.value,C):C(S.value),e.multiple!==!0)return}i.value===!0?se():l.innerLoading.value!==!0&&ce()}}function Ge(){return I===!0?ge.value:X.value!==null&&X.value.contentEl!==null?X.value.contentEl:void 0}function Vt(){return Ge()}function pt(){return e.hideSelected===!0?[]:r["selected-item"]!==void 0?te.value.map(t=>r["selected-item"](t)).slice():r.selected!==void 0?[].concat(r.selected()):e.useChips===!0?te.value.map((t,u)=>b(Wt,{key:"option-"+u,removable:l.editable.value===!0&&ie.value(t.opt)!==!0,dense:!0,textColor:e.color,tabindex:U.value,onRemove(){t.removeAtIndex(u)}},()=>b("span",{class:"ellipsis",[t.html===!0?"innerHTML":"textContent"]:P.value(t.opt)}))):[b("span",{class:"ellipsis",[Ie.value===!0?"innerHTML":"textContent"]:D.value})]}function Ze(){if(O.value===!0)return r["no-option"]!==void 0?r["no-option"]({inputValue:S.value}):void 0;const t=r.option!==void 0?r.option:n=>b(Gt,{key:n.index,...n.itemProps},()=>b(Jt,()=>b(Yt,()=>b("span",{[n.html===!0?"innerHTML":"textContent"]:n.label}))));let u=Be("div",Oe.value.map(t));return r["before-options"]!==void 0&&(u=r["before-options"]().concat(u)),Ut(r["after-options"],u)}function Ct(t,u){const n=u===!0?{...Me.value,...l.splitAttrs.attributes.value}:void 0,c={ref:u===!0?_:void 0,key:"i_t",class:g.value,style:e.inputStyle,value:S.value!==void 0?S.value:"",type:"search",...n,id:u===!0?l.targetUid.value:void 0,maxlength:e.maxlength,autocomplete:e.autocomplete,"data-autofocus":t===!0||e.autofocus===!0||void 0,disabled:e.disable===!0,readonly:e.readonly===!0,...yt.value};return t!==!0&&I===!0&&(Array.isArray(c.class)===!0?c.class=[...c.class,"no-pointer-events"]:c.class+=" no-pointer-events"),b("input",c)}function et(t){w!==null&&(clearTimeout(w),w=null),V!==null&&(clearTimeout(V),V=null),!(t&&t.target&&t.target.qComposing===!0)&&(Ee(t.target.value||""),K=!0,Q=S.value,l.focused.value!==!0&&(I!==!0||T.value===!0)&&l.focus(),e.onFilter!==void 0&&(w=setTimeout(()=>{w=null,re(S.value)},e.inputDebounce)))}function Ee(t,u){S.value!==t&&(S.value=t,u===!0||e.inputDebounce===0||e.inputDebounce==="0"?a("inputValue",t):V=setTimeout(()=>{V=null,a("inputValue",t)},e.inputDebounce))}function pe(t,u,n){K=n!==!0,e.useInput===!0&&(Ee(t,!0),(u===!0||n!==!0)&&(Q=t),u!==!0&&re(t))}function re(t,u,n){if(e.onFilter===void 0||u!==!0&&l.focused.value!==!0)return;l.innerLoading.value===!0?a("filterAbort"):(l.innerLoading.value=!0,s.value=!0),t!==""&&e.multiple!==!0&&o.value.length!==0&&K!==!0&&t===P.value(o.value[0])&&(t="");const c=setTimeout(()=>{i.value===!0&&(i.value=!1)},10);H!==null&&clearTimeout(H),H=c,a("filter",t,(C,p)=>{(u===!0||l.focused.value===!0)&&H===c&&(clearTimeout(H),typeof C=="function"&&C(),s.value=!1,J(()=>{l.innerLoading.value=!1,l.editable.value===!0&&(u===!0?i.value===!0&&ue():i.value===!0?Re(!0):i.value=!0),typeof p=="function"&&J(()=>{p(m)}),typeof n=="function"&&J(()=>{n(m)})}))},()=>{l.focused.value===!0&&H===c&&(clearTimeout(H),l.innerLoading.value=!1,s.value=!1),i.value===!0&&(i.value=!1)})}function xt(){return b(Zt,{ref:X,class:v.value,style:e.popupContentStyle,modelValue:i.value,fit:e.menuShrink!==!0,cover:e.optionsCover===!0&&O.value!==!0&&e.useInput!==!0,anchor:e.menuAnchor,self:e.menuSelf,offset:e.menuOffset,dark:x.value,noParentEvent:!0,noRefocus:!0,noFocus:!0,noRouteDismiss:e.popupNoRouteDismiss,square:He.value,transitionShow:e.transitionShow,transitionHide:e.transitionHide,transitionDuration:e.transitionDuration,separateClosePopup:!0,...ee.value,onScrollPassive:ye,onBeforeShow:lt,onBeforeHide:kt,onShow:At},Ze)}function kt(t){ut(t),se()}function At(){we()}function zt(t){ve(t),_.value?.focus(),T.value=!0,window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,0)}function It(t){ve(t),J(()=>{T.value=!1})}function Mt(){const t=[b(el,{class:`col-auto ${l.fieldClass.value}`,...d.value,for:l.targetUid.value,dark:x.value,square:!0,loading:s.value,itemAligned:!1,filled:!0,stackLabel:S.value.length!==0,...l.splitAttrs.listeners.value,onFocus:zt,onBlur:It},{...r,rawControl:()=>l.getControl(!0),before:void 0,after:void 0})];return i.value===!0&&t.push(b("div",{ref:ge,class:v.value+" scroll",style:e.popupContentStyle,...ee.value,onClick:Pe,onScrollPassive:ye},Ze())),b(Xt,{ref:G,modelValue:E.value,position:e.useInput===!0?"top":void 0,transitionShow:W,transitionHide:e.transitionHide,transitionDuration:e.transitionDuration,noRouteDismiss:e.popupNoRouteDismiss,onBeforeShow:lt,onBeforeHide:Ot,onHide:Ft,onShow:qt},()=>b("div",{class:"q-select__dialog"+(x.value===!0?" q-select__dialog--dark q-dark":"")+(T.value===!0?" q-select__dialog--focused":"")},t))}function Ot(t){ut(t),G.value!==null&&G.value.__updateRefocusTarget(l.rootRef.value.querySelector(".q-field__native > [tabindex]:last-child")),l.focused.value=!1}function Ft(t){ue(),l.focused.value===!1&&a("blur",t),de()}function qt(){const t=document.activeElement;(t===null||t.id!==l.targetUid.value)&&_.value!==null&&_.value!==t&&_.value.focus(),we()}function se(){E.value!==!0&&(y.value=-1,i.value===!0&&(i.value=!1),l.focused.value===!1&&(H!==null&&(clearTimeout(H),H=null),l.innerLoading.value===!0&&(a("filterAbort"),l.innerLoading.value=!1,s.value=!1)))}function ce(t){l.editable.value===!0&&(I===!0?(l.onControlFocusin(t),E.value=!0,J(()=>{l.focus()})):l.focus(),e.onFilter!==void 0?re(S.value):(O.value!==!0||r["no-option"]!==void 0)&&(i.value=!0))}function ue(){E.value=!1,se()}function de(){e.useInput===!0&&pe(e.multiple!==!0&&e.fillInput===!0&&o.value.length!==0&&P.value(o.value[0])||"",!0,!0)}function Re(t){let u=-1;if(t===!0){if(o.value.length!==0){const n=j.value(o.value[0]);u=e.options.findIndex(c=>Ce(j.value(c),n))}$(u)}ae(u)}function Et(t,u){i.value===!0&&l.innerLoading.value===!1&&($(-1,!0),J(()=>{i.value===!0&&l.innerLoading.value===!1&&(t>u?$():Re(!0))}))}function tt(){E.value===!1&&X.value!==null&&X.value.updatePosition()}function lt(t){t!==void 0&&ve(t),a("popupShow",t),l.hasPopupOpen=!0,l.onControlFocusin(t)}function ut(t){t!==void 0&&ve(t),a("popupHide",t),l.hasPopupOpen=!1,l.onControlFocusout(t)}function nt(){I=F.platform.is.mobile!==!0&&e.behavior!=="dialog"?!1:e.behavior!=="menu"&&(e.useInput===!0?r["no-option"]!==void 0||e.onFilter!==void 0||O.value===!1:!0),W=F.platform.is.ios===!0&&I===!0&&e.useInput===!0?"fade":e.transitionShow}return Nt(nt),$t(tt),nt(),St(()=>{w!==null&&clearTimeout(w),V!==null&&clearTimeout(V)}),Object.assign(m,{showPopup:ce,hidePopup:ue,removeAtIndex:_e,add:Qe,toggleOption:le,getOptionIndex:()=>y.value,setOptionIndex:ae,moveOptionSelection:qe,filter:re,updateMenuPosition:tt,updateInputValue:pe,isOptionSelected:Le,getEmittingOptionValue:je,isOptionDisabled:(...t)=>ie.value.apply(null,t)===!0,getOptionValue:(...t)=>j.value.apply(null,t),getOptionLabel:(...t)=>P.value.apply(null,t)}),Object.assign(l,{innerValue:o,fieldClass:h(()=>`q-select q-field--auto-height q-select--with${e.useInput!==!0?"out":""}-input q-select--with${e.useChips!==!0?"out":""}-chips q-select--${e.multiple===!0?"multiple":"single"}`),inputRef:ke,targetRef:_,hasValue:M,showPopup:ce,floatingLabel:h(()=>e.hideSelected!==!0&&M.value===!0||typeof S.value=="number"||S.value.length!==0||ot(e.displayValue)),getControlChild:()=>{if(l.editable.value!==!1&&(E.value===!0||O.value!==!0||r["no-option"]!==void 0))return I===!0?Mt():xt();l.hasPopupOpen===!0&&(l.hasPopupOpen=!1)},controlEvents:{onFocusin(t){l.onControlFocusin(t)},onFocusout(t){l.onControlFocusout(t,()=>{de(),se()})},onClick(t){if(Pe(t),I!==!0&&i.value===!0){se(),_.value?.focus();return}ce(t)}},getControl:t=>{const u=pt(),n=t===!0||E.value!==!0||I!==!0;if(e.useInput===!0)u.push(Ct(t,n));else if(l.editable.value===!0){const C=n===!0?Me.value:void 0;u.push(b("input",{ref:n===!0?_:void 0,key:"d_t",class:"q-select__focus-target",id:n===!0?l.targetUid.value:void 0,value:D.value,readonly:!0,"data-autofocus":t===!0||e.autofocus===!0||void 0,...C,onKeydown:Je,onKeyup:We,onKeypress:Ye})),n===!0&&typeof e.autocomplete=="string"&&e.autocomplete.length!==0&&u.push(b("input",{class:"q-select__autocomplete-input",autocomplete:e.autocomplete,tabindex:-1,onKeyup:Xe}))}if(Z.value!==void 0&&e.disable!==!0&&Fe.value.length!==0){const C=Fe.value.map(p=>b("option",{value:p,selected:!0}));u.push(b("select",{class:"hidden",name:Z.value,multiple:e.multiple},C))}const c=e.useInput===!0||n!==!0?void 0:l.splitAttrs.attributes.value;return b("div",{class:"q-field__native row items-center",...c,...l.splitAttrs.listeners.value},u)},getInnerAppend:()=>e.loading!==!0&&s.value!==!0&&e.hideDropdownIcon!==!0?[b(jt,{class:"q-select__dropdown-icon"+(i.value===!0?" rotate-180":""),name:be.value})]:null}),dt(l)}});export{fl as Q,el as a,ul as b,dl as c,Te as r,rt as u};
